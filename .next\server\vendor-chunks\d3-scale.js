"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/d3-scale";
exports.ids = ["vendor-chunks/d3-scale"];
exports.modules = {

/***/ "(ssr)/./node_modules/d3-scale/src/band.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-scale/src/band.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ band),\n/* harmony export */   point: () => (/* binding */ point)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/range.js\");\n/* harmony import */ var _init_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./init.js */ \"(ssr)/./node_modules/d3-scale/src/init.js\");\n/* harmony import */ var _ordinal_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ordinal.js */ \"(ssr)/./node_modules/d3-scale/src/ordinal.js\");\n\n\n\n\nfunction band() {\n  var scale = (0,_ordinal_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])().unknown(undefined),\n      domain = scale.domain,\n      ordinalRange = scale.range,\n      r0 = 0,\n      r1 = 1,\n      step,\n      bandwidth,\n      round = false,\n      paddingInner = 0,\n      paddingOuter = 0,\n      align = 0.5;\n\n  delete scale.unknown;\n\n  function rescale() {\n    var n = domain().length,\n        reverse = r1 < r0,\n        start = reverse ? r1 : r0,\n        stop = reverse ? r0 : r1;\n    step = (stop - start) / Math.max(1, n - paddingInner + paddingOuter * 2);\n    if (round) step = Math.floor(step);\n    start += (stop - start - step * (n - paddingInner)) * align;\n    bandwidth = step * (1 - paddingInner);\n    if (round) start = Math.round(start), bandwidth = Math.round(bandwidth);\n    var values = (0,d3_array__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(n).map(function(i) { return start + step * i; });\n    return ordinalRange(reverse ? values.reverse() : values);\n  }\n\n  scale.domain = function(_) {\n    return arguments.length ? (domain(_), rescale()) : domain();\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? ([r0, r1] = _, r0 = +r0, r1 = +r1, rescale()) : [r0, r1];\n  };\n\n  scale.rangeRound = function(_) {\n    return [r0, r1] = _, r0 = +r0, r1 = +r1, round = true, rescale();\n  };\n\n  scale.bandwidth = function() {\n    return bandwidth;\n  };\n\n  scale.step = function() {\n    return step;\n  };\n\n  scale.round = function(_) {\n    return arguments.length ? (round = !!_, rescale()) : round;\n  };\n\n  scale.padding = function(_) {\n    return arguments.length ? (paddingInner = Math.min(1, paddingOuter = +_), rescale()) : paddingInner;\n  };\n\n  scale.paddingInner = function(_) {\n    return arguments.length ? (paddingInner = Math.min(1, _), rescale()) : paddingInner;\n  };\n\n  scale.paddingOuter = function(_) {\n    return arguments.length ? (paddingOuter = +_, rescale()) : paddingOuter;\n  };\n\n  scale.align = function(_) {\n    return arguments.length ? (align = Math.max(0, Math.min(1, _)), rescale()) : align;\n  };\n\n  scale.copy = function() {\n    return band(domain(), [r0, r1])\n        .round(round)\n        .paddingInner(paddingInner)\n        .paddingOuter(paddingOuter)\n        .align(align);\n  };\n\n  return _init_js__WEBPACK_IMPORTED_MODULE_2__.initRange.apply(rescale(), arguments);\n}\n\nfunction pointish(scale) {\n  var copy = scale.copy;\n\n  scale.padding = scale.paddingOuter;\n  delete scale.paddingInner;\n  delete scale.paddingOuter;\n\n  scale.copy = function() {\n    return pointish(copy());\n  };\n\n  return scale;\n}\n\nfunction point() {\n  return pointish(band.apply(null, arguments).paddingInner(1));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/band.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-scale/src/constant.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-scale/src/constant.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ constants)\n/* harmony export */ });\nfunction constants(x) {\n  return function() {\n    return x;\n  };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtc2NhbGUvc3JjL2NvbnN0YW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFBDLUxQS1xcRG9jdW1lbnRzXFxIS1VZQVxcYXR0ZW5kYW5jZVxcbm9kZV9tb2R1bGVzXFxkMy1zY2FsZVxcc3JjXFxjb25zdGFudC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBjb25zdGFudHMoeCkge1xuICByZXR1cm4gZnVuY3Rpb24oKSB7XG4gICAgcmV0dXJuIHg7XG4gIH07XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/constant.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-scale/src/continuous.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-scale/src/continuous.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   copy: () => (/* binding */ copy),\n/* harmony export */   \"default\": () => (/* binding */ continuous),\n/* harmony export */   identity: () => (/* binding */ identity),\n/* harmony export */   transformer: () => (/* binding */ transformer)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/bisect.js\");\n/* harmony import */ var d3_interpolate__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3-interpolate */ \"(ssr)/./node_modules/d3-interpolate/src/value.js\");\n/* harmony import */ var d3_interpolate__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! d3-interpolate */ \"(ssr)/./node_modules/d3-interpolate/src/number.js\");\n/* harmony import */ var d3_interpolate__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! d3-interpolate */ \"(ssr)/./node_modules/d3-interpolate/src/round.js\");\n/* harmony import */ var _constant_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constant.js */ \"(ssr)/./node_modules/d3-scale/src/constant.js\");\n/* harmony import */ var _number_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./number.js */ \"(ssr)/./node_modules/d3-scale/src/number.js\");\n\n\n\n\n\nvar unit = [0, 1];\n\nfunction identity(x) {\n  return x;\n}\n\nfunction normalize(a, b) {\n  return (b -= (a = +a))\n      ? function(x) { return (x - a) / b; }\n      : (0,_constant_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(isNaN(b) ? NaN : 0.5);\n}\n\nfunction clamper(a, b) {\n  var t;\n  if (a > b) t = a, a = b, b = t;\n  return function(x) { return Math.max(a, Math.min(b, x)); };\n}\n\n// normalize(a, b)(x) takes a domain value x in [a,b] and returns the corresponding parameter t in [0,1].\n// interpolate(a, b)(t) takes a parameter t in [0,1] and returns the corresponding range value x in [a,b].\nfunction bimap(domain, range, interpolate) {\n  var d0 = domain[0], d1 = domain[1], r0 = range[0], r1 = range[1];\n  if (d1 < d0) d0 = normalize(d1, d0), r0 = interpolate(r1, r0);\n  else d0 = normalize(d0, d1), r0 = interpolate(r0, r1);\n  return function(x) { return r0(d0(x)); };\n}\n\nfunction polymap(domain, range, interpolate) {\n  var j = Math.min(domain.length, range.length) - 1,\n      d = new Array(j),\n      r = new Array(j),\n      i = -1;\n\n  // Reverse descending domains.\n  if (domain[j] < domain[0]) {\n    domain = domain.slice().reverse();\n    range = range.slice().reverse();\n  }\n\n  while (++i < j) {\n    d[i] = normalize(domain[i], domain[i + 1]);\n    r[i] = interpolate(range[i], range[i + 1]);\n  }\n\n  return function(x) {\n    var i = (0,d3_array__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(domain, x, 1, j) - 1;\n    return r[i](d[i](x));\n  };\n}\n\nfunction copy(source, target) {\n  return target\n      .domain(source.domain())\n      .range(source.range())\n      .interpolate(source.interpolate())\n      .clamp(source.clamp())\n      .unknown(source.unknown());\n}\n\nfunction transformer() {\n  var domain = unit,\n      range = unit,\n      interpolate = d3_interpolate__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n      transform,\n      untransform,\n      unknown,\n      clamp = identity,\n      piecewise,\n      output,\n      input;\n\n  function rescale() {\n    var n = Math.min(domain.length, range.length);\n    if (clamp !== identity) clamp = clamper(domain[0], domain[n - 1]);\n    piecewise = n > 2 ? polymap : bimap;\n    output = input = null;\n    return scale;\n  }\n\n  function scale(x) {\n    return x == null || isNaN(x = +x) ? unknown : (output || (output = piecewise(domain.map(transform), range, interpolate)))(transform(clamp(x)));\n  }\n\n  scale.invert = function(y) {\n    return clamp(untransform((input || (input = piecewise(range, domain.map(transform), d3_interpolate__WEBPACK_IMPORTED_MODULE_3__[\"default\"])))(y)));\n  };\n\n  scale.domain = function(_) {\n    return arguments.length ? (domain = Array.from(_, _number_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]), rescale()) : domain.slice();\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? (range = Array.from(_), rescale()) : range.slice();\n  };\n\n  scale.rangeRound = function(_) {\n    return range = Array.from(_), interpolate = d3_interpolate__WEBPACK_IMPORTED_MODULE_5__[\"default\"], rescale();\n  };\n\n  scale.clamp = function(_) {\n    return arguments.length ? (clamp = _ ? true : identity, rescale()) : clamp !== identity;\n  };\n\n  scale.interpolate = function(_) {\n    return arguments.length ? (interpolate = _, rescale()) : interpolate;\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  return function(t, u) {\n    transform = t, untransform = u;\n    return rescale();\n  };\n}\n\nfunction continuous() {\n  return transformer()(identity, identity);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/continuous.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-scale/src/diverging.js":
/*!************************************************!*\
  !*** ./node_modules/d3-scale/src/diverging.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ diverging),\n/* harmony export */   divergingLog: () => (/* binding */ divergingLog),\n/* harmony export */   divergingPow: () => (/* binding */ divergingPow),\n/* harmony export */   divergingSqrt: () => (/* binding */ divergingSqrt),\n/* harmony export */   divergingSymlog: () => (/* binding */ divergingSymlog)\n/* harmony export */ });\n/* harmony import */ var d3_interpolate__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! d3-interpolate */ \"(ssr)/./node_modules/d3-interpolate/src/piecewise.js\");\n/* harmony import */ var d3_interpolate__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3-interpolate */ \"(ssr)/./node_modules/d3-interpolate/src/value.js\");\n/* harmony import */ var d3_interpolate__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! d3-interpolate */ \"(ssr)/./node_modules/d3-interpolate/src/round.js\");\n/* harmony import */ var _continuous_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./continuous.js */ \"(ssr)/./node_modules/d3-scale/src/continuous.js\");\n/* harmony import */ var _init_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./init.js */ \"(ssr)/./node_modules/d3-scale/src/init.js\");\n/* harmony import */ var _linear_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./linear.js */ \"(ssr)/./node_modules/d3-scale/src/linear.js\");\n/* harmony import */ var _log_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./log.js */ \"(ssr)/./node_modules/d3-scale/src/log.js\");\n/* harmony import */ var _sequential_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./sequential.js */ \"(ssr)/./node_modules/d3-scale/src/sequential.js\");\n/* harmony import */ var _symlog_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./symlog.js */ \"(ssr)/./node_modules/d3-scale/src/symlog.js\");\n/* harmony import */ var _pow_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./pow.js */ \"(ssr)/./node_modules/d3-scale/src/pow.js\");\n\n\n\n\n\n\n\n\n\nfunction transformer() {\n  var x0 = 0,\n      x1 = 0.5,\n      x2 = 1,\n      s = 1,\n      t0,\n      t1,\n      t2,\n      k10,\n      k21,\n      interpolator = _continuous_js__WEBPACK_IMPORTED_MODULE_0__.identity,\n      transform,\n      clamp = false,\n      unknown;\n\n  function scale(x) {\n    return isNaN(x = +x) ? unknown : (x = 0.5 + ((x = +transform(x)) - t1) * (s * x < s * t1 ? k10 : k21), interpolator(clamp ? Math.max(0, Math.min(1, x)) : x));\n  }\n\n  scale.domain = function(_) {\n    return arguments.length ? ([x0, x1, x2] = _, t0 = transform(x0 = +x0), t1 = transform(x1 = +x1), t2 = transform(x2 = +x2), k10 = t0 === t1 ? 0 : 0.5 / (t1 - t0), k21 = t1 === t2 ? 0 : 0.5 / (t2 - t1), s = t1 < t0 ? -1 : 1, scale) : [x0, x1, x2];\n  };\n\n  scale.clamp = function(_) {\n    return arguments.length ? (clamp = !!_, scale) : clamp;\n  };\n\n  scale.interpolator = function(_) {\n    return arguments.length ? (interpolator = _, scale) : interpolator;\n  };\n\n  function range(interpolate) {\n    return function(_) {\n      var r0, r1, r2;\n      return arguments.length ? ([r0, r1, r2] = _, interpolator = (0,d3_interpolate__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(interpolate, [r0, r1, r2]), scale) : [interpolator(0), interpolator(0.5), interpolator(1)];\n    };\n  }\n\n  scale.range = range(d3_interpolate__WEBPACK_IMPORTED_MODULE_2__[\"default\"]);\n\n  scale.rangeRound = range(d3_interpolate__WEBPACK_IMPORTED_MODULE_3__[\"default\"]);\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  return function(t) {\n    transform = t, t0 = t(x0), t1 = t(x1), t2 = t(x2), k10 = t0 === t1 ? 0 : 0.5 / (t1 - t0), k21 = t1 === t2 ? 0 : 0.5 / (t2 - t1), s = t1 < t0 ? -1 : 1;\n    return scale;\n  };\n}\n\nfunction diverging() {\n  var scale = (0,_linear_js__WEBPACK_IMPORTED_MODULE_4__.linearish)(transformer()(_continuous_js__WEBPACK_IMPORTED_MODULE_0__.identity));\n\n  scale.copy = function() {\n    return (0,_sequential_js__WEBPACK_IMPORTED_MODULE_5__.copy)(scale, diverging());\n  };\n\n  return _init_js__WEBPACK_IMPORTED_MODULE_6__.initInterpolator.apply(scale, arguments);\n}\n\nfunction divergingLog() {\n  var scale = (0,_log_js__WEBPACK_IMPORTED_MODULE_7__.loggish)(transformer()).domain([0.1, 1, 10]);\n\n  scale.copy = function() {\n    return (0,_sequential_js__WEBPACK_IMPORTED_MODULE_5__.copy)(scale, divergingLog()).base(scale.base());\n  };\n\n  return _init_js__WEBPACK_IMPORTED_MODULE_6__.initInterpolator.apply(scale, arguments);\n}\n\nfunction divergingSymlog() {\n  var scale = (0,_symlog_js__WEBPACK_IMPORTED_MODULE_8__.symlogish)(transformer());\n\n  scale.copy = function() {\n    return (0,_sequential_js__WEBPACK_IMPORTED_MODULE_5__.copy)(scale, divergingSymlog()).constant(scale.constant());\n  };\n\n  return _init_js__WEBPACK_IMPORTED_MODULE_6__.initInterpolator.apply(scale, arguments);\n}\n\nfunction divergingPow() {\n  var scale = (0,_pow_js__WEBPACK_IMPORTED_MODULE_9__.powish)(transformer());\n\n  scale.copy = function() {\n    return (0,_sequential_js__WEBPACK_IMPORTED_MODULE_5__.copy)(scale, divergingPow()).exponent(scale.exponent());\n  };\n\n  return _init_js__WEBPACK_IMPORTED_MODULE_6__.initInterpolator.apply(scale, arguments);\n}\n\nfunction divergingSqrt() {\n  return divergingPow.apply(null, arguments).exponent(0.5);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/diverging.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-scale/src/identity.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-scale/src/identity.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ identity)\n/* harmony export */ });\n/* harmony import */ var _linear_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./linear.js */ \"(ssr)/./node_modules/d3-scale/src/linear.js\");\n/* harmony import */ var _number_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./number.js */ \"(ssr)/./node_modules/d3-scale/src/number.js\");\n\n\n\nfunction identity(domain) {\n  var unknown;\n\n  function scale(x) {\n    return x == null || isNaN(x = +x) ? unknown : x;\n  }\n\n  scale.invert = scale;\n\n  scale.domain = scale.range = function(_) {\n    return arguments.length ? (domain = Array.from(_, _number_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]), scale) : domain.slice();\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  scale.copy = function() {\n    return identity(domain).unknown(unknown);\n  };\n\n  domain = arguments.length ? Array.from(domain, _number_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]) : [0, 1];\n\n  return (0,_linear_js__WEBPACK_IMPORTED_MODULE_1__.linearish)(scale);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtc2NhbGUvc3JjL2lkZW50aXR5LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFzQztBQUNMOztBQUVsQjtBQUNmOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBLHNEQUFzRCxrREFBTTtBQUM1RDs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBLGlEQUFpRCxrREFBTTs7QUFFdkQsU0FBUyxxREFBUztBQUNsQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxQQy1MUEtcXERvY3VtZW50c1xcSEtVWUFcXGF0dGVuZGFuY2VcXG5vZGVfbW9kdWxlc1xcZDMtc2NhbGVcXHNyY1xcaWRlbnRpdHkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtsaW5lYXJpc2h9IGZyb20gXCIuL2xpbmVhci5qc1wiO1xuaW1wb3J0IG51bWJlciBmcm9tIFwiLi9udW1iZXIuanNcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gaWRlbnRpdHkoZG9tYWluKSB7XG4gIHZhciB1bmtub3duO1xuXG4gIGZ1bmN0aW9uIHNjYWxlKHgpIHtcbiAgICByZXR1cm4geCA9PSBudWxsIHx8IGlzTmFOKHggPSAreCkgPyB1bmtub3duIDogeDtcbiAgfVxuXG4gIHNjYWxlLmludmVydCA9IHNjYWxlO1xuXG4gIHNjYWxlLmRvbWFpbiA9IHNjYWxlLnJhbmdlID0gZnVuY3Rpb24oXykge1xuICAgIHJldHVybiBhcmd1bWVudHMubGVuZ3RoID8gKGRvbWFpbiA9IEFycmF5LmZyb20oXywgbnVtYmVyKSwgc2NhbGUpIDogZG9tYWluLnNsaWNlKCk7XG4gIH07XG5cbiAgc2NhbGUudW5rbm93biA9IGZ1bmN0aW9uKF8pIHtcbiAgICByZXR1cm4gYXJndW1lbnRzLmxlbmd0aCA/ICh1bmtub3duID0gXywgc2NhbGUpIDogdW5rbm93bjtcbiAgfTtcblxuICBzY2FsZS5jb3B5ID0gZnVuY3Rpb24oKSB7XG4gICAgcmV0dXJuIGlkZW50aXR5KGRvbWFpbikudW5rbm93bih1bmtub3duKTtcbiAgfTtcblxuICBkb21haW4gPSBhcmd1bWVudHMubGVuZ3RoID8gQXJyYXkuZnJvbShkb21haW4sIG51bWJlcikgOiBbMCwgMV07XG5cbiAgcmV0dXJuIGxpbmVhcmlzaChzY2FsZSk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/identity.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-scale/src/index.js":
/*!********************************************!*\
  !*** ./node_modules/d3-scale/src/index.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   scaleBand: () => (/* reexport safe */ _band_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   scaleDiverging: () => (/* reexport safe */ _diverging_js__WEBPACK_IMPORTED_MODULE_15__[\"default\"]),\n/* harmony export */   scaleDivergingLog: () => (/* reexport safe */ _diverging_js__WEBPACK_IMPORTED_MODULE_15__.divergingLog),\n/* harmony export */   scaleDivergingPow: () => (/* reexport safe */ _diverging_js__WEBPACK_IMPORTED_MODULE_15__.divergingPow),\n/* harmony export */   scaleDivergingSqrt: () => (/* reexport safe */ _diverging_js__WEBPACK_IMPORTED_MODULE_15__.divergingSqrt),\n/* harmony export */   scaleDivergingSymlog: () => (/* reexport safe */ _diverging_js__WEBPACK_IMPORTED_MODULE_15__.divergingSymlog),\n/* harmony export */   scaleIdentity: () => (/* reexport safe */ _identity_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   scaleImplicit: () => (/* reexport safe */ _ordinal_js__WEBPACK_IMPORTED_MODULE_5__.implicit),\n/* harmony export */   scaleLinear: () => (/* reexport safe */ _linear_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   scaleLog: () => (/* reexport safe */ _log_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   scaleOrdinal: () => (/* reexport safe */ _ordinal_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   scalePoint: () => (/* reexport safe */ _band_js__WEBPACK_IMPORTED_MODULE_0__.point),\n/* harmony export */   scalePow: () => (/* reexport safe */ _pow_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   scaleQuantile: () => (/* reexport safe */ _quantile_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"]),\n/* harmony export */   scaleQuantize: () => (/* reexport safe */ _quantize_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"]),\n/* harmony export */   scaleRadial: () => (/* reexport safe */ _radial_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n/* harmony export */   scaleSequential: () => (/* reexport safe */ _sequential_js__WEBPACK_IMPORTED_MODULE_13__[\"default\"]),\n/* harmony export */   scaleSequentialLog: () => (/* reexport safe */ _sequential_js__WEBPACK_IMPORTED_MODULE_13__.sequentialLog),\n/* harmony export */   scaleSequentialPow: () => (/* reexport safe */ _sequential_js__WEBPACK_IMPORTED_MODULE_13__.sequentialPow),\n/* harmony export */   scaleSequentialQuantile: () => (/* reexport safe */ _sequentialQuantile_js__WEBPACK_IMPORTED_MODULE_14__[\"default\"]),\n/* harmony export */   scaleSequentialSqrt: () => (/* reexport safe */ _sequential_js__WEBPACK_IMPORTED_MODULE_13__.sequentialSqrt),\n/* harmony export */   scaleSequentialSymlog: () => (/* reexport safe */ _sequential_js__WEBPACK_IMPORTED_MODULE_13__.sequentialSymlog),\n/* harmony export */   scaleSqrt: () => (/* reexport safe */ _pow_js__WEBPACK_IMPORTED_MODULE_6__.sqrt),\n/* harmony export */   scaleSymlog: () => (/* reexport safe */ _symlog_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   scaleThreshold: () => (/* reexport safe */ _threshold_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"]),\n/* harmony export */   scaleTime: () => (/* reexport safe */ _time_js__WEBPACK_IMPORTED_MODULE_11__[\"default\"]),\n/* harmony export */   scaleUtc: () => (/* reexport safe */ _utcTime_js__WEBPACK_IMPORTED_MODULE_12__[\"default\"]),\n/* harmony export */   tickFormat: () => (/* reexport safe */ _tickFormat_js__WEBPACK_IMPORTED_MODULE_16__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _band_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./band.js */ \"(ssr)/./node_modules/d3-scale/src/band.js\");\n/* harmony import */ var _identity_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./identity.js */ \"(ssr)/./node_modules/d3-scale/src/identity.js\");\n/* harmony import */ var _linear_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./linear.js */ \"(ssr)/./node_modules/d3-scale/src/linear.js\");\n/* harmony import */ var _log_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./log.js */ \"(ssr)/./node_modules/d3-scale/src/log.js\");\n/* harmony import */ var _symlog_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./symlog.js */ \"(ssr)/./node_modules/d3-scale/src/symlog.js\");\n/* harmony import */ var _ordinal_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ordinal.js */ \"(ssr)/./node_modules/d3-scale/src/ordinal.js\");\n/* harmony import */ var _pow_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./pow.js */ \"(ssr)/./node_modules/d3-scale/src/pow.js\");\n/* harmony import */ var _radial_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./radial.js */ \"(ssr)/./node_modules/d3-scale/src/radial.js\");\n/* harmony import */ var _quantile_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./quantile.js */ \"(ssr)/./node_modules/d3-scale/src/quantile.js\");\n/* harmony import */ var _quantize_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./quantize.js */ \"(ssr)/./node_modules/d3-scale/src/quantize.js\");\n/* harmony import */ var _threshold_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./threshold.js */ \"(ssr)/./node_modules/d3-scale/src/threshold.js\");\n/* harmony import */ var _time_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./time.js */ \"(ssr)/./node_modules/d3-scale/src/time.js\");\n/* harmony import */ var _utcTime_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./utcTime.js */ \"(ssr)/./node_modules/d3-scale/src/utcTime.js\");\n/* harmony import */ var _sequential_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./sequential.js */ \"(ssr)/./node_modules/d3-scale/src/sequential.js\");\n/* harmony import */ var _sequentialQuantile_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./sequentialQuantile.js */ \"(ssr)/./node_modules/d3-scale/src/sequentialQuantile.js\");\n/* harmony import */ var _diverging_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./diverging.js */ \"(ssr)/./node_modules/d3-scale/src/diverging.js\");\n/* harmony import */ var _tickFormat_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./tickFormat.js */ \"(ssr)/./node_modules/d3-scale/src/tickFormat.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-scale/src/init.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-scale/src/init.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initInterpolator: () => (/* binding */ initInterpolator),\n/* harmony export */   initRange: () => (/* binding */ initRange)\n/* harmony export */ });\nfunction initRange(domain, range) {\n  switch (arguments.length) {\n    case 0: break;\n    case 1: this.range(domain); break;\n    default: this.range(range).domain(domain); break;\n  }\n  return this;\n}\n\nfunction initInterpolator(domain, interpolator) {\n  switch (arguments.length) {\n    case 0: break;\n    case 1: {\n      if (typeof domain === \"function\") this.interpolator(domain);\n      else this.range(domain);\n      break;\n    }\n    default: {\n      this.domain(domain);\n      if (typeof interpolator === \"function\") this.interpolator(interpolator);\n      else this.range(interpolator);\n      break;\n    }\n  }\n  return this;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtc2NhbGUvc3JjL2luaXQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQSxnQ0FBZ0M7QUFDaEMsK0NBQStDO0FBQy9DO0FBQ0E7QUFDQTs7QUFFTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFBDLUxQS1xcRG9jdW1lbnRzXFxIS1VZQVxcYXR0ZW5kYW5jZVxcbm9kZV9tb2R1bGVzXFxkMy1zY2FsZVxcc3JjXFxpbml0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBpbml0UmFuZ2UoZG9tYWluLCByYW5nZSkge1xuICBzd2l0Y2ggKGFyZ3VtZW50cy5sZW5ndGgpIHtcbiAgICBjYXNlIDA6IGJyZWFrO1xuICAgIGNhc2UgMTogdGhpcy5yYW5nZShkb21haW4pOyBicmVhaztcbiAgICBkZWZhdWx0OiB0aGlzLnJhbmdlKHJhbmdlKS5kb21haW4oZG9tYWluKTsgYnJlYWs7XG4gIH1cbiAgcmV0dXJuIHRoaXM7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBpbml0SW50ZXJwb2xhdG9yKGRvbWFpbiwgaW50ZXJwb2xhdG9yKSB7XG4gIHN3aXRjaCAoYXJndW1lbnRzLmxlbmd0aCkge1xuICAgIGNhc2UgMDogYnJlYWs7XG4gICAgY2FzZSAxOiB7XG4gICAgICBpZiAodHlwZW9mIGRvbWFpbiA9PT0gXCJmdW5jdGlvblwiKSB0aGlzLmludGVycG9sYXRvcihkb21haW4pO1xuICAgICAgZWxzZSB0aGlzLnJhbmdlKGRvbWFpbik7XG4gICAgICBicmVhaztcbiAgICB9XG4gICAgZGVmYXVsdDoge1xuICAgICAgdGhpcy5kb21haW4oZG9tYWluKTtcbiAgICAgIGlmICh0eXBlb2YgaW50ZXJwb2xhdG9yID09PSBcImZ1bmN0aW9uXCIpIHRoaXMuaW50ZXJwb2xhdG9yKGludGVycG9sYXRvcik7XG4gICAgICBlbHNlIHRoaXMucmFuZ2UoaW50ZXJwb2xhdG9yKTtcbiAgICAgIGJyZWFrO1xuICAgIH1cbiAgfVxuICByZXR1cm4gdGhpcztcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/init.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-scale/src/linear.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-scale/src/linear.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ linear),\n/* harmony export */   linearish: () => (/* binding */ linearish)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/ticks.js\");\n/* harmony import */ var _continuous_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./continuous.js */ \"(ssr)/./node_modules/d3-scale/src/continuous.js\");\n/* harmony import */ var _init_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./init.js */ \"(ssr)/./node_modules/d3-scale/src/init.js\");\n/* harmony import */ var _tickFormat_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tickFormat.js */ \"(ssr)/./node_modules/d3-scale/src/tickFormat.js\");\n\n\n\n\n\nfunction linearish(scale) {\n  var domain = scale.domain;\n\n  scale.ticks = function(count) {\n    var d = domain();\n    return (0,d3_array__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(d[0], d[d.length - 1], count == null ? 10 : count);\n  };\n\n  scale.tickFormat = function(count, specifier) {\n    var d = domain();\n    return (0,_tickFormat_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(d[0], d[d.length - 1], count == null ? 10 : count, specifier);\n  };\n\n  scale.nice = function(count) {\n    if (count == null) count = 10;\n\n    var d = domain();\n    var i0 = 0;\n    var i1 = d.length - 1;\n    var start = d[i0];\n    var stop = d[i1];\n    var prestep;\n    var step;\n    var maxIter = 10;\n\n    if (stop < start) {\n      step = start, start = stop, stop = step;\n      step = i0, i0 = i1, i1 = step;\n    }\n    \n    while (maxIter-- > 0) {\n      step = (0,d3_array__WEBPACK_IMPORTED_MODULE_0__.tickIncrement)(start, stop, count);\n      if (step === prestep) {\n        d[i0] = start\n        d[i1] = stop\n        return domain(d);\n      } else if (step > 0) {\n        start = Math.floor(start / step) * step;\n        stop = Math.ceil(stop / step) * step;\n      } else if (step < 0) {\n        start = Math.ceil(start * step) / step;\n        stop = Math.floor(stop * step) / step;\n      } else {\n        break;\n      }\n      prestep = step;\n    }\n\n    return scale;\n  };\n\n  return scale;\n}\n\nfunction linear() {\n  var scale = (0,_continuous_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n\n  scale.copy = function() {\n    return (0,_continuous_js__WEBPACK_IMPORTED_MODULE_2__.copy)(scale, linear());\n  };\n\n  _init_js__WEBPACK_IMPORTED_MODULE_3__.initRange.apply(scale, arguments);\n\n  return linearish(scale);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/linear.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-scale/src/log.js":
/*!******************************************!*\
  !*** ./node_modules/d3-scale/src/log.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ log),\n/* harmony export */   loggish: () => (/* binding */ loggish)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/ticks.js\");\n/* harmony import */ var d3_format__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! d3-format */ \"(ssr)/./node_modules/d3-format/src/formatSpecifier.js\");\n/* harmony import */ var d3_format__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3-format */ \"(ssr)/./node_modules/d3-format/src/defaultLocale.js\");\n/* harmony import */ var _nice_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./nice.js */ \"(ssr)/./node_modules/d3-scale/src/nice.js\");\n/* harmony import */ var _continuous_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./continuous.js */ \"(ssr)/./node_modules/d3-scale/src/continuous.js\");\n/* harmony import */ var _init_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./init.js */ \"(ssr)/./node_modules/d3-scale/src/init.js\");\n\n\n\n\n\n\nfunction transformLog(x) {\n  return Math.log(x);\n}\n\nfunction transformExp(x) {\n  return Math.exp(x);\n}\n\nfunction transformLogn(x) {\n  return -Math.log(-x);\n}\n\nfunction transformExpn(x) {\n  return -Math.exp(-x);\n}\n\nfunction pow10(x) {\n  return isFinite(x) ? +(\"1e\" + x) : x < 0 ? 0 : x;\n}\n\nfunction powp(base) {\n  return base === 10 ? pow10\n      : base === Math.E ? Math.exp\n      : x => Math.pow(base, x);\n}\n\nfunction logp(base) {\n  return base === Math.E ? Math.log\n      : base === 10 && Math.log10\n      || base === 2 && Math.log2\n      || (base = Math.log(base), x => Math.log(x) / base);\n}\n\nfunction reflect(f) {\n  return (x, k) => -f(-x, k);\n}\n\nfunction loggish(transform) {\n  const scale = transform(transformLog, transformExp);\n  const domain = scale.domain;\n  let base = 10;\n  let logs;\n  let pows;\n\n  function rescale() {\n    logs = logp(base), pows = powp(base);\n    if (domain()[0] < 0) {\n      logs = reflect(logs), pows = reflect(pows);\n      transform(transformLogn, transformExpn);\n    } else {\n      transform(transformLog, transformExp);\n    }\n    return scale;\n  }\n\n  scale.base = function(_) {\n    return arguments.length ? (base = +_, rescale()) : base;\n  };\n\n  scale.domain = function(_) {\n    return arguments.length ? (domain(_), rescale()) : domain();\n  };\n\n  scale.ticks = count => {\n    const d = domain();\n    let u = d[0];\n    let v = d[d.length - 1];\n    const r = v < u;\n\n    if (r) ([u, v] = [v, u]);\n\n    let i = logs(u);\n    let j = logs(v);\n    let k;\n    let t;\n    const n = count == null ? 10 : +count;\n    let z = [];\n\n    if (!(base % 1) && j - i < n) {\n      i = Math.floor(i), j = Math.ceil(j);\n      if (u > 0) for (; i <= j; ++i) {\n        for (k = 1; k < base; ++k) {\n          t = i < 0 ? k / pows(-i) : k * pows(i);\n          if (t < u) continue;\n          if (t > v) break;\n          z.push(t);\n        }\n      } else for (; i <= j; ++i) {\n        for (k = base - 1; k >= 1; --k) {\n          t = i > 0 ? k / pows(-i) : k * pows(i);\n          if (t < u) continue;\n          if (t > v) break;\n          z.push(t);\n        }\n      }\n      if (z.length * 2 < n) z = (0,d3_array__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(u, v, n);\n    } else {\n      z = (0,d3_array__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(i, j, Math.min(j - i, n)).map(pows);\n    }\n    return r ? z.reverse() : z;\n  };\n\n  scale.tickFormat = (count, specifier) => {\n    if (count == null) count = 10;\n    if (specifier == null) specifier = base === 10 ? \"s\" : \",\";\n    if (typeof specifier !== \"function\") {\n      if (!(base % 1) && (specifier = (0,d3_format__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(specifier)).precision == null) specifier.trim = true;\n      specifier = (0,d3_format__WEBPACK_IMPORTED_MODULE_2__.format)(specifier);\n    }\n    if (count === Infinity) return specifier;\n    const k = Math.max(1, base * count / scale.ticks().length); // TODO fast estimate?\n    return d => {\n      let i = d / pows(Math.round(logs(d)));\n      if (i * base < base - 0.5) i *= base;\n      return i <= k ? specifier(d) : \"\";\n    };\n  };\n\n  scale.nice = () => {\n    return domain((0,_nice_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(domain(), {\n      floor: x => pows(Math.floor(logs(x))),\n      ceil: x => pows(Math.ceil(logs(x)))\n    }));\n  };\n\n  return scale;\n}\n\nfunction log() {\n  const scale = loggish((0,_continuous_js__WEBPACK_IMPORTED_MODULE_4__.transformer)()).domain([1, 10]);\n  scale.copy = () => (0,_continuous_js__WEBPACK_IMPORTED_MODULE_4__.copy)(scale, log()).base(scale.base());\n  _init_js__WEBPACK_IMPORTED_MODULE_5__.initRange.apply(scale, arguments);\n  return scale;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/log.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-scale/src/nice.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-scale/src/nice.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ nice)\n/* harmony export */ });\nfunction nice(domain, interval) {\n  domain = domain.slice();\n\n  var i0 = 0,\n      i1 = domain.length - 1,\n      x0 = domain[i0],\n      x1 = domain[i1],\n      t;\n\n  if (x1 < x0) {\n    t = i0, i0 = i1, i1 = t;\n    t = x0, x0 = x1, x1 = t;\n  }\n\n  domain[i0] = interval.floor(x0);\n  domain[i1] = interval.ceil(x1);\n  return domain;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtc2NhbGUvc3JjL25pY2UuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFlO0FBQ2Y7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxQQy1MUEtcXERvY3VtZW50c1xcSEtVWUFcXGF0dGVuZGFuY2VcXG5vZGVfbW9kdWxlc1xcZDMtc2NhbGVcXHNyY1xcbmljZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBuaWNlKGRvbWFpbiwgaW50ZXJ2YWwpIHtcbiAgZG9tYWluID0gZG9tYWluLnNsaWNlKCk7XG5cbiAgdmFyIGkwID0gMCxcbiAgICAgIGkxID0gZG9tYWluLmxlbmd0aCAtIDEsXG4gICAgICB4MCA9IGRvbWFpbltpMF0sXG4gICAgICB4MSA9IGRvbWFpbltpMV0sXG4gICAgICB0O1xuXG4gIGlmICh4MSA8IHgwKSB7XG4gICAgdCA9IGkwLCBpMCA9IGkxLCBpMSA9IHQ7XG4gICAgdCA9IHgwLCB4MCA9IHgxLCB4MSA9IHQ7XG4gIH1cblxuICBkb21haW5baTBdID0gaW50ZXJ2YWwuZmxvb3IoeDApO1xuICBkb21haW5baTFdID0gaW50ZXJ2YWwuY2VpbCh4MSk7XG4gIHJldHVybiBkb21haW47XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/nice.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-scale/src/number.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-scale/src/number.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ number)\n/* harmony export */ });\nfunction number(x) {\n  return +x;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtc2NhbGUvc3JjL251bWJlci5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWU7QUFDZjtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFBDLUxQS1xcRG9jdW1lbnRzXFxIS1VZQVxcYXR0ZW5kYW5jZVxcbm9kZV9tb2R1bGVzXFxkMy1zY2FsZVxcc3JjXFxudW1iZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gbnVtYmVyKHgpIHtcbiAgcmV0dXJuICt4O1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/number.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-scale/src/ordinal.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-scale/src/ordinal.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ordinal),\n/* harmony export */   implicit: () => (/* binding */ implicit)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/internmap/src/index.js\");\n/* harmony import */ var _init_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./init.js */ \"(ssr)/./node_modules/d3-scale/src/init.js\");\n\n\n\nconst implicit = Symbol(\"implicit\");\n\nfunction ordinal() {\n  var index = new d3_array__WEBPACK_IMPORTED_MODULE_0__.InternMap(),\n      domain = [],\n      range = [],\n      unknown = implicit;\n\n  function scale(d) {\n    let i = index.get(d);\n    if (i === undefined) {\n      if (unknown !== implicit) return unknown;\n      index.set(d, i = domain.push(d) - 1);\n    }\n    return range[i % range.length];\n  }\n\n  scale.domain = function(_) {\n    if (!arguments.length) return domain.slice();\n    domain = [], index = new d3_array__WEBPACK_IMPORTED_MODULE_0__.InternMap();\n    for (const value of _) {\n      if (index.has(value)) continue;\n      index.set(value, domain.push(value) - 1);\n    }\n    return scale;\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? (range = Array.from(_), scale) : range.slice();\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  scale.copy = function() {\n    return ordinal(domain, range).unknown(unknown);\n  };\n\n  _init_js__WEBPACK_IMPORTED_MODULE_1__.initRange.apply(scale, arguments);\n\n  return scale;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/ordinal.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-scale/src/pow.js":
/*!******************************************!*\
  !*** ./node_modules/d3-scale/src/pow.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ pow),\n/* harmony export */   powish: () => (/* binding */ powish),\n/* harmony export */   sqrt: () => (/* binding */ sqrt)\n/* harmony export */ });\n/* harmony import */ var _linear_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./linear.js */ \"(ssr)/./node_modules/d3-scale/src/linear.js\");\n/* harmony import */ var _continuous_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./continuous.js */ \"(ssr)/./node_modules/d3-scale/src/continuous.js\");\n/* harmony import */ var _init_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./init.js */ \"(ssr)/./node_modules/d3-scale/src/init.js\");\n\n\n\n\nfunction transformPow(exponent) {\n  return function(x) {\n    return x < 0 ? -Math.pow(-x, exponent) : Math.pow(x, exponent);\n  };\n}\n\nfunction transformSqrt(x) {\n  return x < 0 ? -Math.sqrt(-x) : Math.sqrt(x);\n}\n\nfunction transformSquare(x) {\n  return x < 0 ? -x * x : x * x;\n}\n\nfunction powish(transform) {\n  var scale = transform(_continuous_js__WEBPACK_IMPORTED_MODULE_0__.identity, _continuous_js__WEBPACK_IMPORTED_MODULE_0__.identity),\n      exponent = 1;\n\n  function rescale() {\n    return exponent === 1 ? transform(_continuous_js__WEBPACK_IMPORTED_MODULE_0__.identity, _continuous_js__WEBPACK_IMPORTED_MODULE_0__.identity)\n        : exponent === 0.5 ? transform(transformSqrt, transformSquare)\n        : transform(transformPow(exponent), transformPow(1 / exponent));\n  }\n\n  scale.exponent = function(_) {\n    return arguments.length ? (exponent = +_, rescale()) : exponent;\n  };\n\n  return (0,_linear_js__WEBPACK_IMPORTED_MODULE_1__.linearish)(scale);\n}\n\nfunction pow() {\n  var scale = powish((0,_continuous_js__WEBPACK_IMPORTED_MODULE_0__.transformer)());\n\n  scale.copy = function() {\n    return (0,_continuous_js__WEBPACK_IMPORTED_MODULE_0__.copy)(scale, pow()).exponent(scale.exponent());\n  };\n\n  _init_js__WEBPACK_IMPORTED_MODULE_2__.initRange.apply(scale, arguments);\n\n  return scale;\n}\n\nfunction sqrt() {\n  return pow.apply(null, arguments).exponent(0.5);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/pow.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-scale/src/quantile.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-scale/src/quantile.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ quantile)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/quantile.js\");\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/bisect.js\");\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/ascending.js\");\n/* harmony import */ var _init_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./init.js */ \"(ssr)/./node_modules/d3-scale/src/init.js\");\n\n\n\nfunction quantile() {\n  var domain = [],\n      range = [],\n      thresholds = [],\n      unknown;\n\n  function rescale() {\n    var i = 0, n = Math.max(1, range.length);\n    thresholds = new Array(n - 1);\n    while (++i < n) thresholds[i - 1] = (0,d3_array__WEBPACK_IMPORTED_MODULE_0__.quantileSorted)(domain, i / n);\n    return scale;\n  }\n\n  function scale(x) {\n    return x == null || isNaN(x = +x) ? unknown : range[(0,d3_array__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(thresholds, x)];\n  }\n\n  scale.invertExtent = function(y) {\n    var i = range.indexOf(y);\n    return i < 0 ? [NaN, NaN] : [\n      i > 0 ? thresholds[i - 1] : domain[0],\n      i < thresholds.length ? thresholds[i] : domain[domain.length - 1]\n    ];\n  };\n\n  scale.domain = function(_) {\n    if (!arguments.length) return domain.slice();\n    domain = [];\n    for (let d of _) if (d != null && !isNaN(d = +d)) domain.push(d);\n    domain.sort(d3_array__WEBPACK_IMPORTED_MODULE_2__[\"default\"]);\n    return rescale();\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? (range = Array.from(_), rescale()) : range.slice();\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  scale.quantiles = function() {\n    return thresholds.slice();\n  };\n\n  scale.copy = function() {\n    return quantile()\n        .domain(domain)\n        .range(range)\n        .unknown(unknown);\n  };\n\n  return _init_js__WEBPACK_IMPORTED_MODULE_3__.initRange.apply(scale, arguments);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtc2NhbGUvc3JjL3F1YW50aWxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQXdFO0FBQ3BDOztBQUVyQjtBQUNmO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLHdDQUF3Qyx3REFBUztBQUNqRDtBQUNBOztBQUVBO0FBQ0Esd0RBQXdELG9EQUFNO0FBQzlEOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLGdEQUFTO0FBQ3pCO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLFNBQVMsK0NBQVM7QUFDbEIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcUEMtTFBLXFxEb2N1bWVudHNcXEhLVVlBXFxhdHRlbmRhbmNlXFxub2RlX21vZHVsZXNcXGQzLXNjYWxlXFxzcmNcXHF1YW50aWxlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7YXNjZW5kaW5nLCBiaXNlY3QsIHF1YW50aWxlU29ydGVkIGFzIHRocmVzaG9sZH0gZnJvbSBcImQzLWFycmF5XCI7XG5pbXBvcnQge2luaXRSYW5nZX0gZnJvbSBcIi4vaW5pdC5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBxdWFudGlsZSgpIHtcbiAgdmFyIGRvbWFpbiA9IFtdLFxuICAgICAgcmFuZ2UgPSBbXSxcbiAgICAgIHRocmVzaG9sZHMgPSBbXSxcbiAgICAgIHVua25vd247XG5cbiAgZnVuY3Rpb24gcmVzY2FsZSgpIHtcbiAgICB2YXIgaSA9IDAsIG4gPSBNYXRoLm1heCgxLCByYW5nZS5sZW5ndGgpO1xuICAgIHRocmVzaG9sZHMgPSBuZXcgQXJyYXkobiAtIDEpO1xuICAgIHdoaWxlICgrK2kgPCBuKSB0aHJlc2hvbGRzW2kgLSAxXSA9IHRocmVzaG9sZChkb21haW4sIGkgLyBuKTtcbiAgICByZXR1cm4gc2NhbGU7XG4gIH1cblxuICBmdW5jdGlvbiBzY2FsZSh4KSB7XG4gICAgcmV0dXJuIHggPT0gbnVsbCB8fCBpc05hTih4ID0gK3gpID8gdW5rbm93biA6IHJhbmdlW2Jpc2VjdCh0aHJlc2hvbGRzLCB4KV07XG4gIH1cblxuICBzY2FsZS5pbnZlcnRFeHRlbnQgPSBmdW5jdGlvbih5KSB7XG4gICAgdmFyIGkgPSByYW5nZS5pbmRleE9mKHkpO1xuICAgIHJldHVybiBpIDwgMCA/IFtOYU4sIE5hTl0gOiBbXG4gICAgICBpID4gMCA/IHRocmVzaG9sZHNbaSAtIDFdIDogZG9tYWluWzBdLFxuICAgICAgaSA8IHRocmVzaG9sZHMubGVuZ3RoID8gdGhyZXNob2xkc1tpXSA6IGRvbWFpbltkb21haW4ubGVuZ3RoIC0gMV1cbiAgICBdO1xuICB9O1xuXG4gIHNjYWxlLmRvbWFpbiA9IGZ1bmN0aW9uKF8pIHtcbiAgICBpZiAoIWFyZ3VtZW50cy5sZW5ndGgpIHJldHVybiBkb21haW4uc2xpY2UoKTtcbiAgICBkb21haW4gPSBbXTtcbiAgICBmb3IgKGxldCBkIG9mIF8pIGlmIChkICE9IG51bGwgJiYgIWlzTmFOKGQgPSArZCkpIGRvbWFpbi5wdXNoKGQpO1xuICAgIGRvbWFpbi5zb3J0KGFzY2VuZGluZyk7XG4gICAgcmV0dXJuIHJlc2NhbGUoKTtcbiAgfTtcblxuICBzY2FsZS5yYW5nZSA9IGZ1bmN0aW9uKF8pIHtcbiAgICByZXR1cm4gYXJndW1lbnRzLmxlbmd0aCA/IChyYW5nZSA9IEFycmF5LmZyb20oXyksIHJlc2NhbGUoKSkgOiByYW5nZS5zbGljZSgpO1xuICB9O1xuXG4gIHNjYWxlLnVua25vd24gPSBmdW5jdGlvbihfKSB7XG4gICAgcmV0dXJuIGFyZ3VtZW50cy5sZW5ndGggPyAodW5rbm93biA9IF8sIHNjYWxlKSA6IHVua25vd247XG4gIH07XG5cbiAgc2NhbGUucXVhbnRpbGVzID0gZnVuY3Rpb24oKSB7XG4gICAgcmV0dXJuIHRocmVzaG9sZHMuc2xpY2UoKTtcbiAgfTtcblxuICBzY2FsZS5jb3B5ID0gZnVuY3Rpb24oKSB7XG4gICAgcmV0dXJuIHF1YW50aWxlKClcbiAgICAgICAgLmRvbWFpbihkb21haW4pXG4gICAgICAgIC5yYW5nZShyYW5nZSlcbiAgICAgICAgLnVua25vd24odW5rbm93bik7XG4gIH07XG5cbiAgcmV0dXJuIGluaXRSYW5nZS5hcHBseShzY2FsZSwgYXJndW1lbnRzKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/quantile.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-scale/src/quantize.js":
/*!***********************************************!*\
  !*** ./node_modules/d3-scale/src/quantize.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ quantize)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/bisect.js\");\n/* harmony import */ var _linear_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./linear.js */ \"(ssr)/./node_modules/d3-scale/src/linear.js\");\n/* harmony import */ var _init_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./init.js */ \"(ssr)/./node_modules/d3-scale/src/init.js\");\n\n\n\n\nfunction quantize() {\n  var x0 = 0,\n      x1 = 1,\n      n = 1,\n      domain = [0.5],\n      range = [0, 1],\n      unknown;\n\n  function scale(x) {\n    return x != null && x <= x ? range[(0,d3_array__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(domain, x, 0, n)] : unknown;\n  }\n\n  function rescale() {\n    var i = -1;\n    domain = new Array(n);\n    while (++i < n) domain[i] = ((i + 1) * x1 - (i - n) * x0) / (n + 1);\n    return scale;\n  }\n\n  scale.domain = function(_) {\n    return arguments.length ? ([x0, x1] = _, x0 = +x0, x1 = +x1, rescale()) : [x0, x1];\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? (n = (range = Array.from(_)).length - 1, rescale()) : range.slice();\n  };\n\n  scale.invertExtent = function(y) {\n    var i = range.indexOf(y);\n    return i < 0 ? [NaN, NaN]\n        : i < 1 ? [x0, domain[0]]\n        : i >= n ? [domain[n - 1], x1]\n        : [domain[i - 1], domain[i]];\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : scale;\n  };\n\n  scale.thresholds = function() {\n    return domain.slice();\n  };\n\n  scale.copy = function() {\n    return quantize()\n        .domain([x0, x1])\n        .range(range)\n        .unknown(unknown);\n  };\n\n  return _init_js__WEBPACK_IMPORTED_MODULE_1__.initRange.apply((0,_linear_js__WEBPACK_IMPORTED_MODULE_2__.linearish)(scale), arguments);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/quantize.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-scale/src/radial.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-scale/src/radial.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ radial)\n/* harmony export */ });\n/* harmony import */ var _continuous_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./continuous.js */ \"(ssr)/./node_modules/d3-scale/src/continuous.js\");\n/* harmony import */ var _init_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./init.js */ \"(ssr)/./node_modules/d3-scale/src/init.js\");\n/* harmony import */ var _linear_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./linear.js */ \"(ssr)/./node_modules/d3-scale/src/linear.js\");\n/* harmony import */ var _number_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./number.js */ \"(ssr)/./node_modules/d3-scale/src/number.js\");\n\n\n\n\n\nfunction square(x) {\n  return Math.sign(x) * x * x;\n}\n\nfunction unsquare(x) {\n  return Math.sign(x) * Math.sqrt(Math.abs(x));\n}\n\nfunction radial() {\n  var squared = (0,_continuous_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(),\n      range = [0, 1],\n      round = false,\n      unknown;\n\n  function scale(x) {\n    var y = unsquare(squared(x));\n    return isNaN(y) ? unknown : round ? Math.round(y) : y;\n  }\n\n  scale.invert = function(y) {\n    return squared.invert(square(y));\n  };\n\n  scale.domain = function(_) {\n    return arguments.length ? (squared.domain(_), scale) : squared.domain();\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? (squared.range((range = Array.from(_, _number_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])).map(square)), scale) : range.slice();\n  };\n\n  scale.rangeRound = function(_) {\n    return scale.range(_).round(true);\n  };\n\n  scale.round = function(_) {\n    return arguments.length ? (round = !!_, scale) : round;\n  };\n\n  scale.clamp = function(_) {\n    return arguments.length ? (squared.clamp(_), scale) : squared.clamp();\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  scale.copy = function() {\n    return radial(squared.domain(), range)\n        .round(round)\n        .clamp(squared.clamp())\n        .unknown(unknown);\n  };\n\n  _init_js__WEBPACK_IMPORTED_MODULE_2__.initRange.apply(scale, arguments);\n\n  return (0,_linear_js__WEBPACK_IMPORTED_MODULE_3__.linearish)(scale);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtc2NhbGUvc3JjL3JhZGlhbC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUF5QztBQUNMO0FBQ0U7QUFDTDs7QUFFakM7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFZTtBQUNmLGdCQUFnQiwwREFBVTtBQUMxQjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLG9FQUFvRSxrREFBTTtBQUMxRTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLEVBQUUsK0NBQVM7O0FBRVgsU0FBUyxxREFBUztBQUNsQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxQQy1MUEtcXERvY3VtZW50c1xcSEtVWUFcXGF0dGVuZGFuY2VcXG5vZGVfbW9kdWxlc1xcZDMtc2NhbGVcXHNyY1xccmFkaWFsLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjb250aW51b3VzIGZyb20gXCIuL2NvbnRpbnVvdXMuanNcIjtcbmltcG9ydCB7aW5pdFJhbmdlfSBmcm9tIFwiLi9pbml0LmpzXCI7XG5pbXBvcnQge2xpbmVhcmlzaH0gZnJvbSBcIi4vbGluZWFyLmpzXCI7XG5pbXBvcnQgbnVtYmVyIGZyb20gXCIuL251bWJlci5qc1wiO1xuXG5mdW5jdGlvbiBzcXVhcmUoeCkge1xuICByZXR1cm4gTWF0aC5zaWduKHgpICogeCAqIHg7XG59XG5cbmZ1bmN0aW9uIHVuc3F1YXJlKHgpIHtcbiAgcmV0dXJuIE1hdGguc2lnbih4KSAqIE1hdGguc3FydChNYXRoLmFicyh4KSk7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHJhZGlhbCgpIHtcbiAgdmFyIHNxdWFyZWQgPSBjb250aW51b3VzKCksXG4gICAgICByYW5nZSA9IFswLCAxXSxcbiAgICAgIHJvdW5kID0gZmFsc2UsXG4gICAgICB1bmtub3duO1xuXG4gIGZ1bmN0aW9uIHNjYWxlKHgpIHtcbiAgICB2YXIgeSA9IHVuc3F1YXJlKHNxdWFyZWQoeCkpO1xuICAgIHJldHVybiBpc05hTih5KSA/IHVua25vd24gOiByb3VuZCA/IE1hdGgucm91bmQoeSkgOiB5O1xuICB9XG5cbiAgc2NhbGUuaW52ZXJ0ID0gZnVuY3Rpb24oeSkge1xuICAgIHJldHVybiBzcXVhcmVkLmludmVydChzcXVhcmUoeSkpO1xuICB9O1xuXG4gIHNjYWxlLmRvbWFpbiA9IGZ1bmN0aW9uKF8pIHtcbiAgICByZXR1cm4gYXJndW1lbnRzLmxlbmd0aCA/IChzcXVhcmVkLmRvbWFpbihfKSwgc2NhbGUpIDogc3F1YXJlZC5kb21haW4oKTtcbiAgfTtcblxuICBzY2FsZS5yYW5nZSA9IGZ1bmN0aW9uKF8pIHtcbiAgICByZXR1cm4gYXJndW1lbnRzLmxlbmd0aCA/IChzcXVhcmVkLnJhbmdlKChyYW5nZSA9IEFycmF5LmZyb20oXywgbnVtYmVyKSkubWFwKHNxdWFyZSkpLCBzY2FsZSkgOiByYW5nZS5zbGljZSgpO1xuICB9O1xuXG4gIHNjYWxlLnJhbmdlUm91bmQgPSBmdW5jdGlvbihfKSB7XG4gICAgcmV0dXJuIHNjYWxlLnJhbmdlKF8pLnJvdW5kKHRydWUpO1xuICB9O1xuXG4gIHNjYWxlLnJvdW5kID0gZnVuY3Rpb24oXykge1xuICAgIHJldHVybiBhcmd1bWVudHMubGVuZ3RoID8gKHJvdW5kID0gISFfLCBzY2FsZSkgOiByb3VuZDtcbiAgfTtcblxuICBzY2FsZS5jbGFtcCA9IGZ1bmN0aW9uKF8pIHtcbiAgICByZXR1cm4gYXJndW1lbnRzLmxlbmd0aCA/IChzcXVhcmVkLmNsYW1wKF8pLCBzY2FsZSkgOiBzcXVhcmVkLmNsYW1wKCk7XG4gIH07XG5cbiAgc2NhbGUudW5rbm93biA9IGZ1bmN0aW9uKF8pIHtcbiAgICByZXR1cm4gYXJndW1lbnRzLmxlbmd0aCA/ICh1bmtub3duID0gXywgc2NhbGUpIDogdW5rbm93bjtcbiAgfTtcblxuICBzY2FsZS5jb3B5ID0gZnVuY3Rpb24oKSB7XG4gICAgcmV0dXJuIHJhZGlhbChzcXVhcmVkLmRvbWFpbigpLCByYW5nZSlcbiAgICAgICAgLnJvdW5kKHJvdW5kKVxuICAgICAgICAuY2xhbXAoc3F1YXJlZC5jbGFtcCgpKVxuICAgICAgICAudW5rbm93bih1bmtub3duKTtcbiAgfTtcblxuICBpbml0UmFuZ2UuYXBwbHkoc2NhbGUsIGFyZ3VtZW50cyk7XG5cbiAgcmV0dXJuIGxpbmVhcmlzaChzY2FsZSk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/radial.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-scale/src/sequential.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-scale/src/sequential.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   copy: () => (/* binding */ copy),\n/* harmony export */   \"default\": () => (/* binding */ sequential),\n/* harmony export */   sequentialLog: () => (/* binding */ sequentialLog),\n/* harmony export */   sequentialPow: () => (/* binding */ sequentialPow),\n/* harmony export */   sequentialSqrt: () => (/* binding */ sequentialSqrt),\n/* harmony export */   sequentialSymlog: () => (/* binding */ sequentialSymlog)\n/* harmony export */ });\n/* harmony import */ var d3_interpolate__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! d3-interpolate */ \"(ssr)/./node_modules/d3-interpolate/src/value.js\");\n/* harmony import */ var d3_interpolate__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3-interpolate */ \"(ssr)/./node_modules/d3-interpolate/src/round.js\");\n/* harmony import */ var _continuous_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./continuous.js */ \"(ssr)/./node_modules/d3-scale/src/continuous.js\");\n/* harmony import */ var _init_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./init.js */ \"(ssr)/./node_modules/d3-scale/src/init.js\");\n/* harmony import */ var _linear_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./linear.js */ \"(ssr)/./node_modules/d3-scale/src/linear.js\");\n/* harmony import */ var _log_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./log.js */ \"(ssr)/./node_modules/d3-scale/src/log.js\");\n/* harmony import */ var _symlog_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./symlog.js */ \"(ssr)/./node_modules/d3-scale/src/symlog.js\");\n/* harmony import */ var _pow_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./pow.js */ \"(ssr)/./node_modules/d3-scale/src/pow.js\");\n\n\n\n\n\n\n\n\nfunction transformer() {\n  var x0 = 0,\n      x1 = 1,\n      t0,\n      t1,\n      k10,\n      transform,\n      interpolator = _continuous_js__WEBPACK_IMPORTED_MODULE_0__.identity,\n      clamp = false,\n      unknown;\n\n  function scale(x) {\n    return x == null || isNaN(x = +x) ? unknown : interpolator(k10 === 0 ? 0.5 : (x = (transform(x) - t0) * k10, clamp ? Math.max(0, Math.min(1, x)) : x));\n  }\n\n  scale.domain = function(_) {\n    return arguments.length ? ([x0, x1] = _, t0 = transform(x0 = +x0), t1 = transform(x1 = +x1), k10 = t0 === t1 ? 0 : 1 / (t1 - t0), scale) : [x0, x1];\n  };\n\n  scale.clamp = function(_) {\n    return arguments.length ? (clamp = !!_, scale) : clamp;\n  };\n\n  scale.interpolator = function(_) {\n    return arguments.length ? (interpolator = _, scale) : interpolator;\n  };\n\n  function range(interpolate) {\n    return function(_) {\n      var r0, r1;\n      return arguments.length ? ([r0, r1] = _, interpolator = interpolate(r0, r1), scale) : [interpolator(0), interpolator(1)];\n    };\n  }\n\n  scale.range = range(d3_interpolate__WEBPACK_IMPORTED_MODULE_1__[\"default\"]);\n\n  scale.rangeRound = range(d3_interpolate__WEBPACK_IMPORTED_MODULE_2__[\"default\"]);\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  return function(t) {\n    transform = t, t0 = t(x0), t1 = t(x1), k10 = t0 === t1 ? 0 : 1 / (t1 - t0);\n    return scale;\n  };\n}\n\nfunction copy(source, target) {\n  return target\n      .domain(source.domain())\n      .interpolator(source.interpolator())\n      .clamp(source.clamp())\n      .unknown(source.unknown());\n}\n\nfunction sequential() {\n  var scale = (0,_linear_js__WEBPACK_IMPORTED_MODULE_3__.linearish)(transformer()(_continuous_js__WEBPACK_IMPORTED_MODULE_0__.identity));\n\n  scale.copy = function() {\n    return copy(scale, sequential());\n  };\n\n  return _init_js__WEBPACK_IMPORTED_MODULE_4__.initInterpolator.apply(scale, arguments);\n}\n\nfunction sequentialLog() {\n  var scale = (0,_log_js__WEBPACK_IMPORTED_MODULE_5__.loggish)(transformer()).domain([1, 10]);\n\n  scale.copy = function() {\n    return copy(scale, sequentialLog()).base(scale.base());\n  };\n\n  return _init_js__WEBPACK_IMPORTED_MODULE_4__.initInterpolator.apply(scale, arguments);\n}\n\nfunction sequentialSymlog() {\n  var scale = (0,_symlog_js__WEBPACK_IMPORTED_MODULE_6__.symlogish)(transformer());\n\n  scale.copy = function() {\n    return copy(scale, sequentialSymlog()).constant(scale.constant());\n  };\n\n  return _init_js__WEBPACK_IMPORTED_MODULE_4__.initInterpolator.apply(scale, arguments);\n}\n\nfunction sequentialPow() {\n  var scale = (0,_pow_js__WEBPACK_IMPORTED_MODULE_7__.powish)(transformer());\n\n  scale.copy = function() {\n    return copy(scale, sequentialPow()).exponent(scale.exponent());\n  };\n\n  return _init_js__WEBPACK_IMPORTED_MODULE_4__.initInterpolator.apply(scale, arguments);\n}\n\nfunction sequentialSqrt() {\n  return sequentialPow.apply(null, arguments).exponent(0.5);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/sequential.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-scale/src/sequentialQuantile.js":
/*!*********************************************************!*\
  !*** ./node_modules/d3-scale/src/sequentialQuantile.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ sequentialQuantile)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/bisect.js\");\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/ascending.js\");\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/quantile.js\");\n/* harmony import */ var _continuous_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./continuous.js */ \"(ssr)/./node_modules/d3-scale/src/continuous.js\");\n/* harmony import */ var _init_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./init.js */ \"(ssr)/./node_modules/d3-scale/src/init.js\");\n\n\n\n\nfunction sequentialQuantile() {\n  var domain = [],\n      interpolator = _continuous_js__WEBPACK_IMPORTED_MODULE_0__.identity;\n\n  function scale(x) {\n    if (x != null && !isNaN(x = +x)) return interpolator(((0,d3_array__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(domain, x, 1) - 1) / (domain.length - 1));\n  }\n\n  scale.domain = function(_) {\n    if (!arguments.length) return domain.slice();\n    domain = [];\n    for (let d of _) if (d != null && !isNaN(d = +d)) domain.push(d);\n    domain.sort(d3_array__WEBPACK_IMPORTED_MODULE_2__[\"default\"]);\n    return scale;\n  };\n\n  scale.interpolator = function(_) {\n    return arguments.length ? (interpolator = _, scale) : interpolator;\n  };\n\n  scale.range = function() {\n    return domain.map((d, i) => interpolator(i / (domain.length - 1)));\n  };\n\n  scale.quantiles = function(n) {\n    return Array.from({length: n + 1}, (_, i) => (0,d3_array__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(domain, i / n));\n  };\n\n  scale.copy = function() {\n    return sequentialQuantile(interpolator).domain(domain);\n  };\n\n  return _init_js__WEBPACK_IMPORTED_MODULE_4__.initInterpolator.apply(scale, arguments);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/sequentialQuantile.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-scale/src/symlog.js":
/*!*********************************************!*\
  !*** ./node_modules/d3-scale/src/symlog.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ symlog),\n/* harmony export */   symlogish: () => (/* binding */ symlogish)\n/* harmony export */ });\n/* harmony import */ var _linear_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./linear.js */ \"(ssr)/./node_modules/d3-scale/src/linear.js\");\n/* harmony import */ var _continuous_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./continuous.js */ \"(ssr)/./node_modules/d3-scale/src/continuous.js\");\n/* harmony import */ var _init_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./init.js */ \"(ssr)/./node_modules/d3-scale/src/init.js\");\n\n\n\n\nfunction transformSymlog(c) {\n  return function(x) {\n    return Math.sign(x) * Math.log1p(Math.abs(x / c));\n  };\n}\n\nfunction transformSymexp(c) {\n  return function(x) {\n    return Math.sign(x) * Math.expm1(Math.abs(x)) * c;\n  };\n}\n\nfunction symlogish(transform) {\n  var c = 1, scale = transform(transformSymlog(c), transformSymexp(c));\n\n  scale.constant = function(_) {\n    return arguments.length ? transform(transformSymlog(c = +_), transformSymexp(c)) : c;\n  };\n\n  return (0,_linear_js__WEBPACK_IMPORTED_MODULE_0__.linearish)(scale);\n}\n\nfunction symlog() {\n  var scale = symlogish((0,_continuous_js__WEBPACK_IMPORTED_MODULE_1__.transformer)());\n\n  scale.copy = function() {\n    return (0,_continuous_js__WEBPACK_IMPORTED_MODULE_1__.copy)(scale, symlog()).constant(scale.constant());\n  };\n\n  return _init_js__WEBPACK_IMPORTED_MODULE_2__.initRange.apply(scale, arguments);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtc2NhbGUvc3JjL3N5bWxvZy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFzQztBQUNZO0FBQ2Q7O0FBRXBDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFTztBQUNQOztBQUVBO0FBQ0E7QUFDQTs7QUFFQSxTQUFTLHFEQUFTO0FBQ2xCOztBQUVlO0FBQ2Ysd0JBQXdCLDJEQUFXOztBQUVuQztBQUNBLFdBQVcsb0RBQUk7QUFDZjs7QUFFQSxTQUFTLCtDQUFTO0FBQ2xCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFBDLUxQS1xcRG9jdW1lbnRzXFxIS1VZQVxcYXR0ZW5kYW5jZVxcbm9kZV9tb2R1bGVzXFxkMy1zY2FsZVxcc3JjXFxzeW1sb2cuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtsaW5lYXJpc2h9IGZyb20gXCIuL2xpbmVhci5qc1wiO1xuaW1wb3J0IHtjb3B5LCB0cmFuc2Zvcm1lcn0gZnJvbSBcIi4vY29udGludW91cy5qc1wiO1xuaW1wb3J0IHtpbml0UmFuZ2V9IGZyb20gXCIuL2luaXQuanNcIjtcblxuZnVuY3Rpb24gdHJhbnNmb3JtU3ltbG9nKGMpIHtcbiAgcmV0dXJuIGZ1bmN0aW9uKHgpIHtcbiAgICByZXR1cm4gTWF0aC5zaWduKHgpICogTWF0aC5sb2cxcChNYXRoLmFicyh4IC8gYykpO1xuICB9O1xufVxuXG5mdW5jdGlvbiB0cmFuc2Zvcm1TeW1leHAoYykge1xuICByZXR1cm4gZnVuY3Rpb24oeCkge1xuICAgIHJldHVybiBNYXRoLnNpZ24oeCkgKiBNYXRoLmV4cG0xKE1hdGguYWJzKHgpKSAqIGM7XG4gIH07XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBzeW1sb2dpc2godHJhbnNmb3JtKSB7XG4gIHZhciBjID0gMSwgc2NhbGUgPSB0cmFuc2Zvcm0odHJhbnNmb3JtU3ltbG9nKGMpLCB0cmFuc2Zvcm1TeW1leHAoYykpO1xuXG4gIHNjYWxlLmNvbnN0YW50ID0gZnVuY3Rpb24oXykge1xuICAgIHJldHVybiBhcmd1bWVudHMubGVuZ3RoID8gdHJhbnNmb3JtKHRyYW5zZm9ybVN5bWxvZyhjID0gK18pLCB0cmFuc2Zvcm1TeW1leHAoYykpIDogYztcbiAgfTtcblxuICByZXR1cm4gbGluZWFyaXNoKHNjYWxlKTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gc3ltbG9nKCkge1xuICB2YXIgc2NhbGUgPSBzeW1sb2dpc2godHJhbnNmb3JtZXIoKSk7XG5cbiAgc2NhbGUuY29weSA9IGZ1bmN0aW9uKCkge1xuICAgIHJldHVybiBjb3B5KHNjYWxlLCBzeW1sb2coKSkuY29uc3RhbnQoc2NhbGUuY29uc3RhbnQoKSk7XG4gIH07XG5cbiAgcmV0dXJuIGluaXRSYW5nZS5hcHBseShzY2FsZSwgYXJndW1lbnRzKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/symlog.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-scale/src/threshold.js":
/*!************************************************!*\
  !*** ./node_modules/d3-scale/src/threshold.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ threshold)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/bisect.js\");\n/* harmony import */ var _init_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./init.js */ \"(ssr)/./node_modules/d3-scale/src/init.js\");\n\n\n\nfunction threshold() {\n  var domain = [0.5],\n      range = [0, 1],\n      unknown,\n      n = 1;\n\n  function scale(x) {\n    return x != null && x <= x ? range[(0,d3_array__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(domain, x, 0, n)] : unknown;\n  }\n\n  scale.domain = function(_) {\n    return arguments.length ? (domain = Array.from(_), n = Math.min(domain.length, range.length - 1), scale) : domain.slice();\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? (range = Array.from(_), n = Math.min(domain.length, range.length - 1), scale) : range.slice();\n  };\n\n  scale.invertExtent = function(y) {\n    var i = range.indexOf(y);\n    return [domain[i - 1], domain[i]];\n  };\n\n  scale.unknown = function(_) {\n    return arguments.length ? (unknown = _, scale) : unknown;\n  };\n\n  scale.copy = function() {\n    return threshold()\n        .domain(domain)\n        .range(range)\n        .unknown(unknown);\n  };\n\n  return _init_js__WEBPACK_IMPORTED_MODULE_1__.initRange.apply(scale, arguments);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtc2NhbGUvc3JjL3RocmVzaG9sZC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBZ0M7QUFDSTs7QUFFckI7QUFDZjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLHVDQUF1QyxvREFBTTtBQUM3Qzs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsU0FBUywrQ0FBUztBQUNsQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxQQy1MUEtcXERvY3VtZW50c1xcSEtVWUFcXGF0dGVuZGFuY2VcXG5vZGVfbW9kdWxlc1xcZDMtc2NhbGVcXHNyY1xcdGhyZXNob2xkLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7YmlzZWN0fSBmcm9tIFwiZDMtYXJyYXlcIjtcbmltcG9ydCB7aW5pdFJhbmdlfSBmcm9tIFwiLi9pbml0LmpzXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHRocmVzaG9sZCgpIHtcbiAgdmFyIGRvbWFpbiA9IFswLjVdLFxuICAgICAgcmFuZ2UgPSBbMCwgMV0sXG4gICAgICB1bmtub3duLFxuICAgICAgbiA9IDE7XG5cbiAgZnVuY3Rpb24gc2NhbGUoeCkge1xuICAgIHJldHVybiB4ICE9IG51bGwgJiYgeCA8PSB4ID8gcmFuZ2VbYmlzZWN0KGRvbWFpbiwgeCwgMCwgbildIDogdW5rbm93bjtcbiAgfVxuXG4gIHNjYWxlLmRvbWFpbiA9IGZ1bmN0aW9uKF8pIHtcbiAgICByZXR1cm4gYXJndW1lbnRzLmxlbmd0aCA/IChkb21haW4gPSBBcnJheS5mcm9tKF8pLCBuID0gTWF0aC5taW4oZG9tYWluLmxlbmd0aCwgcmFuZ2UubGVuZ3RoIC0gMSksIHNjYWxlKSA6IGRvbWFpbi5zbGljZSgpO1xuICB9O1xuXG4gIHNjYWxlLnJhbmdlID0gZnVuY3Rpb24oXykge1xuICAgIHJldHVybiBhcmd1bWVudHMubGVuZ3RoID8gKHJhbmdlID0gQXJyYXkuZnJvbShfKSwgbiA9IE1hdGgubWluKGRvbWFpbi5sZW5ndGgsIHJhbmdlLmxlbmd0aCAtIDEpLCBzY2FsZSkgOiByYW5nZS5zbGljZSgpO1xuICB9O1xuXG4gIHNjYWxlLmludmVydEV4dGVudCA9IGZ1bmN0aW9uKHkpIHtcbiAgICB2YXIgaSA9IHJhbmdlLmluZGV4T2YoeSk7XG4gICAgcmV0dXJuIFtkb21haW5baSAtIDFdLCBkb21haW5baV1dO1xuICB9O1xuXG4gIHNjYWxlLnVua25vd24gPSBmdW5jdGlvbihfKSB7XG4gICAgcmV0dXJuIGFyZ3VtZW50cy5sZW5ndGggPyAodW5rbm93biA9IF8sIHNjYWxlKSA6IHVua25vd247XG4gIH07XG5cbiAgc2NhbGUuY29weSA9IGZ1bmN0aW9uKCkge1xuICAgIHJldHVybiB0aHJlc2hvbGQoKVxuICAgICAgICAuZG9tYWluKGRvbWFpbilcbiAgICAgICAgLnJhbmdlKHJhbmdlKVxuICAgICAgICAudW5rbm93bih1bmtub3duKTtcbiAgfTtcblxuICByZXR1cm4gaW5pdFJhbmdlLmFwcGx5KHNjYWxlLCBhcmd1bWVudHMpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/threshold.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-scale/src/tickFormat.js":
/*!*************************************************!*\
  !*** ./node_modules/d3-scale/src/tickFormat.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ tickFormat)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! d3-array */ \"(ssr)/./node_modules/d3-array/src/ticks.js\");\n/* harmony import */ var d3_format__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! d3-format */ \"(ssr)/./node_modules/d3-format/src/formatSpecifier.js\");\n/* harmony import */ var d3_format__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3-format */ \"(ssr)/./node_modules/d3-format/src/precisionPrefix.js\");\n/* harmony import */ var d3_format__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! d3-format */ \"(ssr)/./node_modules/d3-format/src/defaultLocale.js\");\n/* harmony import */ var d3_format__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! d3-format */ \"(ssr)/./node_modules/d3-format/src/precisionRound.js\");\n/* harmony import */ var d3_format__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! d3-format */ \"(ssr)/./node_modules/d3-format/src/precisionFixed.js\");\n\n\n\nfunction tickFormat(start, stop, count, specifier) {\n  var step = (0,d3_array__WEBPACK_IMPORTED_MODULE_0__.tickStep)(start, stop, count),\n      precision;\n  specifier = (0,d3_format__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(specifier == null ? \",f\" : specifier);\n  switch (specifier.type) {\n    case \"s\": {\n      var value = Math.max(Math.abs(start), Math.abs(stop));\n      if (specifier.precision == null && !isNaN(precision = (0,d3_format__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(step, value))) specifier.precision = precision;\n      return (0,d3_format__WEBPACK_IMPORTED_MODULE_3__.formatPrefix)(specifier, value);\n    }\n    case \"\":\n    case \"e\":\n    case \"g\":\n    case \"p\":\n    case \"r\": {\n      if (specifier.precision == null && !isNaN(precision = (0,d3_format__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(step, Math.max(Math.abs(start), Math.abs(stop))))) specifier.precision = precision - (specifier.type === \"e\");\n      break;\n    }\n    case \"f\":\n    case \"%\": {\n      if (specifier.precision == null && !isNaN(precision = (0,d3_format__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(step))) specifier.precision = precision - (specifier.type === \"%\") * 2;\n      break;\n    }\n  }\n  return (0,d3_format__WEBPACK_IMPORTED_MODULE_3__.format)(specifier);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/tickFormat.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-scale/src/time.js":
/*!*******************************************!*\
  !*** ./node_modules/d3-scale/src/time.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calendar: () => (/* binding */ calendar),\n/* harmony export */   \"default\": () => (/* binding */ time)\n/* harmony export */ });\n/* harmony import */ var d3_time__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! d3-time */ \"(ssr)/./node_modules/d3-time/src/ticks.js\");\n/* harmony import */ var d3_time__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! d3-time */ \"(ssr)/./node_modules/d3-time/src/year.js\");\n/* harmony import */ var d3_time__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! d3-time */ \"(ssr)/./node_modules/d3-time/src/month.js\");\n/* harmony import */ var d3_time__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! d3-time */ \"(ssr)/./node_modules/d3-time/src/week.js\");\n/* harmony import */ var d3_time__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! d3-time */ \"(ssr)/./node_modules/d3-time/src/day.js\");\n/* harmony import */ var d3_time__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! d3-time */ \"(ssr)/./node_modules/d3-time/src/hour.js\");\n/* harmony import */ var d3_time__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! d3-time */ \"(ssr)/./node_modules/d3-time/src/minute.js\");\n/* harmony import */ var d3_time__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! d3-time */ \"(ssr)/./node_modules/d3-time/src/second.js\");\n/* harmony import */ var d3_time_format__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! d3-time-format */ \"(ssr)/./node_modules/d3-time-format/src/defaultLocale.js\");\n/* harmony import */ var _continuous_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./continuous.js */ \"(ssr)/./node_modules/d3-scale/src/continuous.js\");\n/* harmony import */ var _init_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./init.js */ \"(ssr)/./node_modules/d3-scale/src/init.js\");\n/* harmony import */ var _nice_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./nice.js */ \"(ssr)/./node_modules/d3-scale/src/nice.js\");\n\n\n\n\n\n\nfunction date(t) {\n  return new Date(t);\n}\n\nfunction number(t) {\n  return t instanceof Date ? +t : +new Date(+t);\n}\n\nfunction calendar(ticks, tickInterval, year, month, week, day, hour, minute, second, format) {\n  var scale = (0,_continuous_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(),\n      invert = scale.invert,\n      domain = scale.domain;\n\n  var formatMillisecond = format(\".%L\"),\n      formatSecond = format(\":%S\"),\n      formatMinute = format(\"%I:%M\"),\n      formatHour = format(\"%I %p\"),\n      formatDay = format(\"%a %d\"),\n      formatWeek = format(\"%b %d\"),\n      formatMonth = format(\"%B\"),\n      formatYear = format(\"%Y\");\n\n  function tickFormat(date) {\n    return (second(date) < date ? formatMillisecond\n        : minute(date) < date ? formatSecond\n        : hour(date) < date ? formatMinute\n        : day(date) < date ? formatHour\n        : month(date) < date ? (week(date) < date ? formatDay : formatWeek)\n        : year(date) < date ? formatMonth\n        : formatYear)(date);\n  }\n\n  scale.invert = function(y) {\n    return new Date(invert(y));\n  };\n\n  scale.domain = function(_) {\n    return arguments.length ? domain(Array.from(_, number)) : domain().map(date);\n  };\n\n  scale.ticks = function(interval) {\n    var d = domain();\n    return ticks(d[0], d[d.length - 1], interval == null ? 10 : interval);\n  };\n\n  scale.tickFormat = function(count, specifier) {\n    return specifier == null ? tickFormat : format(specifier);\n  };\n\n  scale.nice = function(interval) {\n    var d = domain();\n    if (!interval || typeof interval.range !== \"function\") interval = tickInterval(d[0], d[d.length - 1], interval == null ? 10 : interval);\n    return interval ? domain((0,_nice_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(d, interval)) : scale;\n  };\n\n  scale.copy = function() {\n    return (0,_continuous_js__WEBPACK_IMPORTED_MODULE_0__.copy)(scale, calendar(ticks, tickInterval, year, month, week, day, hour, minute, second, format));\n  };\n\n  return scale;\n}\n\nfunction time() {\n  return _init_js__WEBPACK_IMPORTED_MODULE_2__.initRange.apply(calendar(d3_time__WEBPACK_IMPORTED_MODULE_3__.timeTicks, d3_time__WEBPACK_IMPORTED_MODULE_3__.timeTickInterval, d3_time__WEBPACK_IMPORTED_MODULE_4__.timeYear, d3_time__WEBPACK_IMPORTED_MODULE_5__.timeMonth, d3_time__WEBPACK_IMPORTED_MODULE_6__.timeSunday, d3_time__WEBPACK_IMPORTED_MODULE_7__.timeDay, d3_time__WEBPACK_IMPORTED_MODULE_8__.timeHour, d3_time__WEBPACK_IMPORTED_MODULE_9__.timeMinute, d3_time__WEBPACK_IMPORTED_MODULE_10__.second, d3_time_format__WEBPACK_IMPORTED_MODULE_11__.timeFormat).domain([new Date(2000, 0, 1), new Date(2000, 0, 2)]), arguments);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/time.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/d3-scale/src/utcTime.js":
/*!**********************************************!*\
  !*** ./node_modules/d3-scale/src/utcTime.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ utcTime)\n/* harmony export */ });\n/* harmony import */ var d3_time__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3-time */ \"(ssr)/./node_modules/d3-time/src/ticks.js\");\n/* harmony import */ var d3_time__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! d3-time */ \"(ssr)/./node_modules/d3-time/src/year.js\");\n/* harmony import */ var d3_time__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! d3-time */ \"(ssr)/./node_modules/d3-time/src/month.js\");\n/* harmony import */ var d3_time__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! d3-time */ \"(ssr)/./node_modules/d3-time/src/week.js\");\n/* harmony import */ var d3_time__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! d3-time */ \"(ssr)/./node_modules/d3-time/src/day.js\");\n/* harmony import */ var d3_time__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! d3-time */ \"(ssr)/./node_modules/d3-time/src/hour.js\");\n/* harmony import */ var d3_time__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! d3-time */ \"(ssr)/./node_modules/d3-time/src/minute.js\");\n/* harmony import */ var d3_time__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! d3-time */ \"(ssr)/./node_modules/d3-time/src/second.js\");\n/* harmony import */ var d3_time_format__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! d3-time-format */ \"(ssr)/./node_modules/d3-time-format/src/defaultLocale.js\");\n/* harmony import */ var _time_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./time.js */ \"(ssr)/./node_modules/d3-scale/src/time.js\");\n/* harmony import */ var _init_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./init.js */ \"(ssr)/./node_modules/d3-scale/src/init.js\");\n\n\n\n\n\nfunction utcTime() {\n  return _init_js__WEBPACK_IMPORTED_MODULE_0__.initRange.apply((0,_time_js__WEBPACK_IMPORTED_MODULE_1__.calendar)(d3_time__WEBPACK_IMPORTED_MODULE_2__.utcTicks, d3_time__WEBPACK_IMPORTED_MODULE_2__.utcTickInterval, d3_time__WEBPACK_IMPORTED_MODULE_3__.utcYear, d3_time__WEBPACK_IMPORTED_MODULE_4__.utcMonth, d3_time__WEBPACK_IMPORTED_MODULE_5__.utcSunday, d3_time__WEBPACK_IMPORTED_MODULE_6__.utcDay, d3_time__WEBPACK_IMPORTED_MODULE_7__.utcHour, d3_time__WEBPACK_IMPORTED_MODULE_8__.utcMinute, d3_time__WEBPACK_IMPORTED_MODULE_9__.second, d3_time_format__WEBPACK_IMPORTED_MODULE_10__.utcFormat).domain([Date.UTC(2000, 0, 1), Date.UTC(2000, 0, 2)]), arguments);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZDMtc2NhbGUvc3JjL3V0Y1RpbWUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBQXFIO0FBQzVFO0FBQ047QUFDQzs7QUFFckI7QUFDZixTQUFTLCtDQUFTLE9BQU8sa0RBQVEsQ0FBQyw2Q0FBUSxFQUFFLG9EQUFlLEVBQUUsNENBQU8sRUFBRSw2Q0FBUSxFQUFFLDhDQUFPLEVBQUUsMkNBQU0sRUFBRSw0Q0FBTyxFQUFFLDhDQUFTLEVBQUUsMkNBQVMsRUFBRSxzREFBUztBQUN6SSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxQQy1MUEtcXERvY3VtZW50c1xcSEtVWUFcXGF0dGVuZGFuY2VcXG5vZGVfbW9kdWxlc1xcZDMtc2NhbGVcXHNyY1xcdXRjVGltZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge3V0Y1llYXIsIHV0Y01vbnRoLCB1dGNXZWVrLCB1dGNEYXksIHV0Y0hvdXIsIHV0Y01pbnV0ZSwgdXRjU2Vjb25kLCB1dGNUaWNrcywgdXRjVGlja0ludGVydmFsfSBmcm9tIFwiZDMtdGltZVwiO1xuaW1wb3J0IHt1dGNGb3JtYXR9IGZyb20gXCJkMy10aW1lLWZvcm1hdFwiO1xuaW1wb3J0IHtjYWxlbmRhcn0gZnJvbSBcIi4vdGltZS5qc1wiO1xuaW1wb3J0IHtpbml0UmFuZ2V9IGZyb20gXCIuL2luaXQuanNcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gdXRjVGltZSgpIHtcbiAgcmV0dXJuIGluaXRSYW5nZS5hcHBseShjYWxlbmRhcih1dGNUaWNrcywgdXRjVGlja0ludGVydmFsLCB1dGNZZWFyLCB1dGNNb250aCwgdXRjV2VlaywgdXRjRGF5LCB1dGNIb3VyLCB1dGNNaW51dGUsIHV0Y1NlY29uZCwgdXRjRm9ybWF0KS5kb21haW4oW0RhdGUuVVRDKDIwMDAsIDAsIDEpLCBEYXRlLlVUQygyMDAwLCAwLCAyKV0pLCBhcmd1bWVudHMpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/d3-scale/src/utcTime.js\n");

/***/ })

};
;