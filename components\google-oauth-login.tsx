// Google OAuth2 登入組件
import React, { useState, useEffect } from 'react'
import { LogIn, LogOut, AlertCircle, CheckCircle } from 'lucide-react'

interface GoogleOAuthLoginProps {
  clientId: string
  onAuthSuccess: (accessToken: string) => void
  onAuthError: (error: string) => void
  className?: string
}

declare global {
  interface Window {
    google: any
    gapi: any
  }
}

export function GoogleOAuthLogin({
  clientId,
  onAuthSuccess,
  onAuthError,
  className = ''
}: GoogleOAuthLoginProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [userInfo, setUserInfo] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)

  // 載入 Google API 和處理重定向
  useEffect(() => {
    // 檢查 URL 中是否有 access token（重定向認證）
    const checkForRedirectToken = () => {
      const hash = window.location.hash
      if (hash.includes('access_token=')) {
        const params = new URLSearchParams(hash.substring(1))
        const accessToken = params.get('access_token')
        if (accessToken) {
          // 清除 URL 中的 hash
          window.history.replaceState({}, document.title, window.location.pathname)

          // 模擬用戶信息（重定向方式無法獲取用戶詳細信息）
          setIsAuthenticated(true)
          setUserInfo({
            name: '已認證用戶',
            email: '通過重定向認證',
            imageUrl: ''
          })
          setError(null)

          onAuthSuccess(accessToken)
          return true
        }
      }
      return false
    }

    // 先檢查重定向 token
    if (checkForRedirectToken()) {
      return
    }

    const loadGoogleAPI = () => {
      if (window.gapi) {
        initializeGapi()
        return
      }

      const script = document.createElement('script')
      script.src = 'https://apis.google.com/js/api.js'
      script.onload = initializeGapi
      script.onerror = () => {
        setError('無法載入 Google API')
      }
      document.body.appendChild(script)
    }

    const initializeGapi = () => {
      window.gapi.load('auth2', () => {
        // 驗證 Client ID 格式
        if (!clientId || !clientId.includes('.apps.googleusercontent.com')) {
          setError('Client ID 格式不正確，請檢查配置')
          return
        }

        // 添加更詳細的配置和錯誤處理
        const initConfig = {
          client_id: clientId,
          scope: 'https://www.googleapis.com/auth/spreadsheets',
          // 添加更多配置選項來避免常見問題
          hosted_domain: undefined,
          fetch_basic_profile: true,
          ux_mode: 'popup'
        }

        console.log('正在初始化 Google Auth2，配置:', {
          client_id: clientId.substring(0, 20) + '...',
          scope: initConfig.scope,
          origin: window.location.origin
        })

        window.gapi.auth2.init(initConfig).then((authInstance: any) => {
          console.log('Google Auth2 初始化成功')
          const isSignedIn = authInstance.isSignedIn.get()

          if (isSignedIn) {
            const currentUser = authInstance.currentUser.get()
            handleAuthSuccess(currentUser)
          }
        }).catch((error: any) => {
          console.error('Google API 初始化失敗 - 完整錯誤對象:', error)
          console.error('錯誤類型:', typeof error)
          console.error('錯誤鍵值:', Object.keys(error))

          let errorMessage = 'Google API 初始化失敗'
          let errorDetails = []

          // 更全面的錯誤處理
          if (error.error) {
            switch (error.error) {
              case 'invalid_client':
                errorMessage = 'Client ID 無效'
                errorDetails.push('請檢查 Google Cloud Console 中的 OAuth2 客戶端設置')
                errorDetails.push('確認 Client ID 格式正確且存在')
                break
              case 'unauthorized_client':
                errorMessage = '客戶端未授權'
                errorDetails.push('請在 Google Cloud Console 中添加當前域名到授權來源')
                errorDetails.push(`當前域名: ${window.location.origin}`)
                break
              case 'redirect_uri_mismatch':
                errorMessage = '重定向 URI 不匹配'
                errorDetails.push('請在 OAuth2 設置中添加正確的重定向 URI')
                break
              default:
                errorMessage = `Google API 錯誤: ${error.error}`
            }
          } else if (error.details) {
            errorMessage = `初始化失敗: ${error.details}`
          } else if (error.message) {
            errorMessage = `初始化失敗: ${error.message}`
          } else if (Object.keys(error).length === 0) {
            errorMessage = '初始化失敗：可能是 Google Cloud Console 配置問題'
            errorDetails.push('請檢查 OAuth2 客戶端的授權 JavaScript 來源')
            errorDetails.push(`需要添加: ${window.location.origin}`)
            errorDetails.push('請檢查 Google Sheets API 是否已啟用')
          } else {
            errorMessage = `初始化失敗: ${JSON.stringify(error)}`
          }

          // 組合錯誤信息
          const fullErrorMessage = errorDetails.length > 0
            ? `${errorMessage}\n\n解決方案:\n${errorDetails.map((detail, index) => `${index + 1}. ${detail}`).join('\n')}`
            : errorMessage

          setError(fullErrorMessage)
        })
      })
    }

    loadGoogleAPI()
  }, [clientId])

  const handleAuthSuccess = (user: any) => {
    const authResponse = user.getAuthResponse()
    const profile = user.getBasicProfile()

    setIsAuthenticated(true)
    setUserInfo({
      name: profile.getName(),
      email: profile.getEmail(),
      imageUrl: profile.getImageUrl()
    })
    setError(null)

    onAuthSuccess(authResponse.access_token)
  }

  const handleSignIn = async () => {
    if (!window.gapi || !window.gapi.auth2) {
      setError('Google API 尚未載入')
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const authInstance = window.gapi.auth2.getAuthInstance()

      // 使用 signIn 選項來改善彈出窗口體驗
      const user = await authInstance.signIn({
        prompt: 'select_account'  // 總是顯示帳戶選擇
      })

      handleAuthSuccess(user)
    } catch (error: any) {
      console.error('登入失敗:', error)

      // 處理特定錯誤
      let errorMessage = '登入失敗'

      if (error.error === 'popup_closed_by_user') {
        errorMessage = '登入窗口被關閉，請重試。如果持續出現此問題，請檢查瀏覽器是否阻止了彈出窗口。'
      } else if (error.error === 'popup_blocked_by_browser') {
        errorMessage = '瀏覽器阻止了彈出窗口，請在瀏覽器設置中允許此網站的彈出窗口，然後重試。'
      } else if (error.error === 'access_denied') {
        errorMessage = '用戶拒絕了授權請求'
      } else if (error.error === 'immediate_failed') {
        errorMessage = '無法自動登入，請手動登入'
      } else {
        errorMessage = error.error || error.message || '未知錯誤'
      }

      setError(errorMessage)
      onAuthError(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSignOut = async () => {
    if (!window.gapi || !window.gapi.auth2) {
      return
    }

    try {
      const authInstance = window.gapi.auth2.getAuthInstance()
      await authInstance.signOut()

      setIsAuthenticated(false)
      setUserInfo(null)
      setError(null)
    } catch (error: any) {
      console.error('登出失敗:', error)
      setError('登出失敗')
    }
  }

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Google 帳戶認證
        </h3>

        {isAuthenticated && (
          <div className="flex items-center space-x-2 text-green-600 dark:text-green-400">
            <CheckCircle className="h-4 w-4" />
            <span className="text-sm">已認證</span>
          </div>
        )}
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded">
          <div className="flex items-start space-x-2">
            <AlertCircle className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-red-700 dark:text-red-300">
              <div className="font-medium mb-1">登入錯誤</div>
              <div>{error}</div>

              {error.includes('彈出窗口') && (
                <div className="mt-2 p-2 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded">
                  <div className="text-xs text-yellow-700 dark:text-yellow-300">
                    <div className="font-medium mb-1">解決方法：</div>
                    <ol className="list-decimal list-inside space-y-1">
                      <li>檢查瀏覽器地址欄右側是否有彈出窗口被阻止的圖標</li>
                      <li>點擊該圖標並選擇「總是允許來自此網站的彈出式視窗」</li>
                      <li>重新整理頁面並再次嘗試登入</li>
                    </ol>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {isAuthenticated && userInfo ? (
        <div className="space-y-4">
          <div className="flex items-center space-x-3">
            <img
              src={userInfo.imageUrl}
              alt={userInfo.name}
              className="w-10 h-10 rounded-full"
            />
            <div>
              <div className="font-medium text-gray-900 dark:text-white">
                {userInfo.name}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                {userInfo.email}
              </div>
            </div>
          </div>

          <button
            onClick={handleSignOut}
            className="flex items-center space-x-2 px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600"
          >
            <LogOut className="h-4 w-4" />
            <span>登出</span>
          </button>
        </div>
      ) : (
        <div className="space-y-4">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            請登入你的 Google 帳戶以存取 Google Sheets
          </p>

          <div className="space-y-2">
            <button
              onClick={handleSignIn}
              disabled={isLoading}
              className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              ) : (
                <LogIn className="h-4 w-4" />
              )}
              <span>{isLoading ? '登入中...' : '使用 Google 登入'}</span>
            </button>

            {error && error.includes('彈出窗口') && (
              <button
                onClick={() => {
                  // 使用重定向方式登入
                  const redirectUrl = `https://accounts.google.com/oauth/authorize?client_id=${clientId}&redirect_uri=${encodeURIComponent(window.location.origin)}&scope=https://www.googleapis.com/auth/spreadsheets&response_type=token&prompt=select_account`
                  window.location.href = redirectUrl
                }}
                className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600"
              >
                <LogIn className="h-4 w-4" />
                <span>使用重定向方式登入</span>
              </button>
            )}
          </div>
        </div>
      )}

      <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded">
        <div className="text-sm text-blue-700 dark:text-blue-300">
          <div className="font-medium mb-1">權限說明：</div>
          <div>此應用需要存取你的 Google Sheets 以進行數據同步</div>

          {/* 調試信息 */}
          <div className="mt-2 pt-2 border-t border-blue-200 dark:border-blue-700">
            <div className="font-medium mb-1">調試信息：</div>
            <div className="text-xs space-y-1">
              <div>Client ID: {clientId ? `${clientId.substring(0, 20)}...` : '未設置'}</div>
              <div>當前域名: {typeof window !== 'undefined' ? window.location.origin : 'N/A'}</div>
              <div>Google API 狀態: {typeof window !== 'undefined' && window.gapi ? '已載入' : '未載入'}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
