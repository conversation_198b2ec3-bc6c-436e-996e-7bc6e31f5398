// Google Sheets 連接 API 路由
import { NextRequest, NextResponse } from 'next/server'
import { GoogleSheetsService } from '../../../../lib/google-sheets'
import type { GoogleSheetsConfig, SheetNames } from '../../../../types/google-sheets'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { config } = body as { config: GoogleSheetsConfig }

    // 驗證必要的配置
    if (!config.spreadsheetId) {
      return NextResponse.json(
        { success: false, error: '缺少 Spreadsheet ID' },
        { status: 400 }
      )
    }

    if (!config.serviceAccountEmail || !config.privateKey) {
      if (!config.clientId || !config.clientSecret) {
        return NextResponse.json(
          { success: false, error: '缺少認證配置' },
          { status: 400 }
        )
      }
    }

    const sheetNames: SheetNames = {
      participants: process.env.NEXT_PUBLIC_PARTICIPANTS_SHEET_NAME || '參加者資料',
      activities: process.env.NEXT_PUBLIC_ACTIVITIES_SHEET_NAME || '活動資料',
      attendance: process.env.NEXT_PUBLIC_ATTENDANCE_SHEET_NAME || '出席記錄',
      sessions: process.env.NEXT_PUBLIC_SESSIONS_SHEET_NAME || '屆別資料'
    }

    // 初始化 Google Sheets 服務
    const googleSheetsService = new GoogleSheetsService(config, sheetNames)

    // 測試連接
    const initResult = await googleSheetsService.initialize()
    if (!initResult.success) {
      return NextResponse.json(
        { success: false, error: initResult.error },
        { status: 500 }
      )
    }

    // 確保工作表存在
    const sheetsResult = await googleSheetsService.ensureSheetsExist()
    if (!sheetsResult.success) {
      return NextResponse.json(
        { success: false, error: sheetsResult.error },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Google Sheets 連接成功'
    })

  } catch (error) {
    console.error('Google Sheets 連接錯誤:', error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : '未知錯誤'
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json(
    { message: 'Google Sheets 連接 API' },
    { status: 200 }
  )
}
