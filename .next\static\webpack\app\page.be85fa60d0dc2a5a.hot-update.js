"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./hooks/use-participant-handlers.ts":
/*!*******************************************!*\
  !*** ./hooks/use-participant-handlers.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useParticipantHandlers: () => (/* binding */ useParticipantHandlers)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useParticipantHandlers auto */ \nfunction useParticipantHandlers(props) {\n    const { allParticipants, sessionParticipants, activities, setAllParticipants, setSessionParticipants, setActivities } = props;\n    const handleAddParticipant = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useParticipantHandlers.useCallback[handleAddParticipant]\": (newParticipant)=>{\n            // 使用時間戳和隨機數生成唯一ID，避免ID衝突\n            const id = \"\".concat(Date.now(), \"-\").concat(Math.random().toString(36).substr(2, 9));\n            const participant = {\n                id,\n                ...newParticipant,\n                attendance: {}\n            };\n            console.log('Adding participant to global list:', participant) // 調試日誌\n            ;\n            setAllParticipants([\n                ...allParticipants,\n                participant\n            ]);\n        }\n    }[\"useParticipantHandlers.useCallback[handleAddParticipant]\"], [\n        allParticipants,\n        setAllParticipants\n    ]);\n    const handleAddSessionParticipant = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useParticipantHandlers.useCallback[handleAddSessionParticipant]\": (newSessionParticipant)=>{\n            // 使用時間戳和隨機數生成唯一ID，避免ID衝突\n            const id = \"session-\".concat(Date.now(), \"-\").concat(Math.random().toString(36).substr(2, 9));\n            const sessionParticipant = {\n                id,\n                ...newSessionParticipant\n            };\n            console.log('Adding session participant:', sessionParticipant) // 調試日誌\n            ;\n            setSessionParticipants([\n                ...sessionParticipants,\n                sessionParticipant\n            ]);\n        }\n    }[\"useParticipantHandlers.useCallback[handleAddSessionParticipant]\"], [\n        sessionParticipants,\n        setSessionParticipants\n    ]);\n    const handleBulkAddParticipants = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useParticipantHandlers.useCallback[handleBulkAddParticipants]\": (newParticipants)=>{\n            let currentId = allParticipants.length + 1;\n            const participants = newParticipants.map({\n                \"useParticipantHandlers.useCallback[handleBulkAddParticipants].participants\": (p)=>{\n                    const participant = {\n                        id: currentId.toString(),\n                        ...p,\n                        attendance: {}\n                    };\n                    currentId++;\n                    return participant;\n                }\n            }[\"useParticipantHandlers.useCallback[handleBulkAddParticipants].participants\"]);\n            setAllParticipants([\n                ...allParticipants,\n                ...participants\n            ]);\n        }\n    }[\"useParticipantHandlers.useCallback[handleBulkAddParticipants]\"], [\n        allParticipants,\n        setAllParticipants\n    ]);\n    const handleUpdateParticipant = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useParticipantHandlers.useCallback[handleUpdateParticipant]\": (updatedParticipant)=>{\n            setAllParticipants(allParticipants.map({\n                \"useParticipantHandlers.useCallback[handleUpdateParticipant]\": (p)=>p.id === updatedParticipant.id ? updatedParticipant : p\n            }[\"useParticipantHandlers.useCallback[handleUpdateParticipant]\"]));\n            setSessionParticipants(sessionParticipants.map({\n                \"useParticipantHandlers.useCallback[handleUpdateParticipant]\": (sp)=>sp.participantId === updatedParticipant.id ? {\n                        ...sp,\n                        name: updatedParticipant.name\n                    } : sp\n            }[\"useParticipantHandlers.useCallback[handleUpdateParticipant]\"]));\n        }\n    }[\"useParticipantHandlers.useCallback[handleUpdateParticipant]\"], [\n        allParticipants,\n        sessionParticipants,\n        setAllParticipants,\n        setSessionParticipants\n    ]);\n    const handleUpdateSessionParticipant = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useParticipantHandlers.useCallback[handleUpdateSessionParticipant]\": (updatedSessionParticipant)=>{\n            setSessionParticipants(sessionParticipants.map({\n                \"useParticipantHandlers.useCallback[handleUpdateSessionParticipant]\": (sp)=>sp.id === updatedSessionParticipant.id ? updatedSessionParticipant : sp\n            }[\"useParticipantHandlers.useCallback[handleUpdateSessionParticipant]\"]));\n        }\n    }[\"useParticipantHandlers.useCallback[handleUpdateSessionParticipant]\"], [\n        sessionParticipants,\n        setSessionParticipants\n    ]);\n    const handleDeleteParticipant = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useParticipantHandlers.useCallback[handleDeleteParticipant]\": (participantId)=>{\n            setAllParticipants(allParticipants.filter({\n                \"useParticipantHandlers.useCallback[handleDeleteParticipant]\": (p)=>p.id !== participantId\n            }[\"useParticipantHandlers.useCallback[handleDeleteParticipant]\"]));\n            setSessionParticipants(sessionParticipants.filter({\n                \"useParticipantHandlers.useCallback[handleDeleteParticipant]\": (sp)=>sp.participantId !== participantId\n            }[\"useParticipantHandlers.useCallback[handleDeleteParticipant]\"]));\n            setActivities(activities.map({\n                \"useParticipantHandlers.useCallback[handleDeleteParticipant]\": (activity)=>({\n                        ...activity,\n                        participants: activity.participants.filter({\n                            \"useParticipantHandlers.useCallback[handleDeleteParticipant]\": (p)=>p.id !== participantId\n                        }[\"useParticipantHandlers.useCallback[handleDeleteParticipant]\"])\n                    })\n            }[\"useParticipantHandlers.useCallback[handleDeleteParticipant]\"]));\n        }\n    }[\"useParticipantHandlers.useCallback[handleDeleteParticipant]\"], [\n        allParticipants,\n        sessionParticipants,\n        activities,\n        setAllParticipants,\n        setSessionParticipants,\n        setActivities\n    ]);\n    const handleRemoveFromSession = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useParticipantHandlers.useCallback[handleRemoveFromSession]\": (sessionParticipantId)=>{\n            // 找到要移除的屆別參加者\n            const sessionParticipantToRemove = sessionParticipants.find({\n                \"useParticipantHandlers.useCallback[handleRemoveFromSession].sessionParticipantToRemove\": (sp)=>sp.id === sessionParticipantId\n            }[\"useParticipantHandlers.useCallback[handleRemoveFromSession].sessionParticipantToRemove\"]);\n            if (sessionParticipantToRemove) {\n                // 從屆別參加者列表中移除\n                setSessionParticipants(sessionParticipants.filter({\n                    \"useParticipantHandlers.useCallback[handleRemoveFromSession]\": (sp)=>sp.id !== sessionParticipantId\n                }[\"useParticipantHandlers.useCallback[handleRemoveFromSession]\"]));\n                // 從所有相關活動中移除該參加者\n                setActivities(activities.map({\n                    \"useParticipantHandlers.useCallback[handleRemoveFromSession]\": (activity)=>({\n                            ...activity,\n                            participants: activity.participants.filter({\n                                \"useParticipantHandlers.useCallback[handleRemoveFromSession]\": (p)=>p.id !== sessionParticipantToRemove.participantId\n                            }[\"useParticipantHandlers.useCallback[handleRemoveFromSession]\"])\n                        })\n                }[\"useParticipantHandlers.useCallback[handleRemoveFromSession]\"]));\n            }\n        }\n    }[\"useParticipantHandlers.useCallback[handleRemoveFromSession]\"], [\n        sessionParticipants,\n        activities,\n        setSessionParticipants,\n        setActivities\n    ]);\n    const handleBulkDeleteTitle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useParticipantHandlers.useCallback[handleBulkDeleteTitle]\": (title)=>{\n            // 找出所有使用該職銜的參加者\n            const participantsToUpdate = allParticipants.filter({\n                \"useParticipantHandlers.useCallback[handleBulkDeleteTitle].participantsToUpdate\": (p)=>p.category === title && !p.name.startsWith('職銜佔位符-')\n            }[\"useParticipantHandlers.useCallback[handleBulkDeleteTitle].participantsToUpdate\"]);\n            const placeholders = allParticipants.filter({\n                \"useParticipantHandlers.useCallback[handleBulkDeleteTitle].placeholders\": (p)=>p.category === title && p.name.startsWith('職銜佔位符-')\n            }[\"useParticipantHandlers.useCallback[handleBulkDeleteTitle].placeholders\"]);\n            const sessionParticipantsToUpdate = sessionParticipants.filter({\n                \"useParticipantHandlers.useCallback[handleBulkDeleteTitle].sessionParticipantsToUpdate\": (sp)=>sp.category === title\n            }[\"useParticipantHandlers.useCallback[handleBulkDeleteTitle].sessionParticipantsToUpdate\"]);\n            // 一次性更新所有狀態\n            // 更新全局參加者 - 清除職銜\n            const updatedAllParticipants = allParticipants.map({\n                \"useParticipantHandlers.useCallback[handleBulkDeleteTitle].updatedAllParticipants\": (p)=>{\n                    if (p.category === title && !p.name.startsWith('職銜佔位符-')) {\n                        return {\n                            ...p,\n                            category: \"\"\n                        };\n                    }\n                    return p;\n                }\n            }[\"useParticipantHandlers.useCallback[handleBulkDeleteTitle].updatedAllParticipants\"]).filter({\n                \"useParticipantHandlers.useCallback[handleBulkDeleteTitle].updatedAllParticipants\": (p)=>!p.name.startsWith(\"職銜佔位符-\".concat(title))\n            }[\"useParticipantHandlers.useCallback[handleBulkDeleteTitle].updatedAllParticipants\"]) // 移除佔位符\n            ;\n            // 更新屆別參加者 - 清除職銜\n            const updatedSessionParticipants = sessionParticipants.map({\n                \"useParticipantHandlers.useCallback[handleBulkDeleteTitle].updatedSessionParticipants\": (sp)=>{\n                    if (sp.category === title) {\n                        return {\n                            ...sp,\n                            category: \"\"\n                        };\n                    }\n                    return sp;\n                }\n            }[\"useParticipantHandlers.useCallback[handleBulkDeleteTitle].updatedSessionParticipants\"]);\n            // 從活動中移除佔位符參加者\n            const updatedActivities = activities.map({\n                \"useParticipantHandlers.useCallback[handleBulkDeleteTitle].updatedActivities\": (activity)=>({\n                        ...activity,\n                        participants: activity.participants.filter({\n                            \"useParticipantHandlers.useCallback[handleBulkDeleteTitle].updatedActivities\": (p)=>!placeholders.some({\n                                    \"useParticipantHandlers.useCallback[handleBulkDeleteTitle].updatedActivities\": (placeholder)=>placeholder.id === p.id\n                                }[\"useParticipantHandlers.useCallback[handleBulkDeleteTitle].updatedActivities\"])\n                        }[\"useParticipantHandlers.useCallback[handleBulkDeleteTitle].updatedActivities\"])\n                    })\n            }[\"useParticipantHandlers.useCallback[handleBulkDeleteTitle].updatedActivities\"]);\n            // 批量更新所有狀態\n            setAllParticipants(updatedAllParticipants);\n            setSessionParticipants(updatedSessionParticipants);\n            setActivities(updatedActivities);\n            return {\n                participantsUpdated: participantsToUpdate.length,\n                placeholdersRemoved: placeholders.length,\n                sessionParticipantsUpdated: sessionParticipantsToUpdate.length\n            };\n        }\n    }[\"useParticipantHandlers.useCallback[handleBulkDeleteTitle]\"], [\n        allParticipants,\n        sessionParticipants,\n        activities,\n        setAllParticipants,\n        setSessionParticipants,\n        setActivities\n    ]);\n    return {\n        handleAddParticipant,\n        handleAddSessionParticipant,\n        handleBulkAddParticipants,\n        handleUpdateParticipant,\n        handleUpdateSessionParticipant,\n        handleDeleteParticipant,\n        handleRemoveFromSession,\n        handleBulkDeleteTitle\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/use-participant-handlers.ts\n"));

/***/ })

});