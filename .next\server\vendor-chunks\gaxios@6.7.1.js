"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/gaxios@6.7.1";
exports.ids = ["vendor-chunks/gaxios@6.7.1"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/gaxios@6.7.1/node_modules/gaxios/build/src/common.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/.pnpm/gaxios@6.7.1/node_modules/gaxios/build/src/common.js ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n// Copyright 2018 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nvar _a;\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.GaxiosError = exports.GAXIOS_ERROR_SYMBOL = void 0;\nexports.defaultErrorRedactor = defaultErrorRedactor;\nconst url_1 = __webpack_require__(/*! url */ \"url\");\nconst util_1 = __webpack_require__(/*! ./util */ \"(rsc)/./node_modules/.pnpm/gaxios@6.7.1/node_modules/gaxios/build/src/util.js\");\nconst extend_1 = __importDefault(__webpack_require__(/*! extend */ \"(rsc)/./node_modules/.pnpm/extend@3.0.2/node_modules/extend/index.js\"));\n/**\n * Support `instanceof` operator for `GaxiosError`s in different versions of this library.\n *\n * @see {@link GaxiosError[Symbol.hasInstance]}\n */\nexports.GAXIOS_ERROR_SYMBOL = Symbol.for(`${util_1.pkg.name}-gaxios-error`);\n/* eslint-disable-next-line @typescript-eslint/no-explicit-any */\nclass GaxiosError extends Error {\n    /**\n     * Support `instanceof` operator for `GaxiosError` across builds/duplicated files.\n     *\n     * @see {@link GAXIOS_ERROR_SYMBOL}\n     * @see {@link GaxiosError[GAXIOS_ERROR_SYMBOL]}\n     */\n    static [(_a = exports.GAXIOS_ERROR_SYMBOL, Symbol.hasInstance)](instance) {\n        if (instance &&\n            typeof instance === 'object' &&\n            exports.GAXIOS_ERROR_SYMBOL in instance &&\n            instance[exports.GAXIOS_ERROR_SYMBOL] === util_1.pkg.version) {\n            return true;\n        }\n        // fallback to native\n        return Function.prototype[Symbol.hasInstance].call(GaxiosError, instance);\n    }\n    constructor(message, config, response, error) {\n        var _b;\n        super(message);\n        this.config = config;\n        this.response = response;\n        this.error = error;\n        /**\n         * Support `instanceof` operator for `GaxiosError` across builds/duplicated files.\n         *\n         * @see {@link GAXIOS_ERROR_SYMBOL}\n         * @see {@link GaxiosError[Symbol.hasInstance]}\n         * @see {@link https://github.com/microsoft/TypeScript/issues/13965#issuecomment-278570200}\n         * @see {@link https://stackoverflow.com/questions/46618852/require-and-instanceof}\n         * @see {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function/@@hasInstance#reverting_to_default_instanceof_behavior}\n         */\n        this[_a] = util_1.pkg.version;\n        // deep-copy config as we do not want to mutate\n        // the existing config for future retries/use\n        this.config = (0, extend_1.default)(true, {}, config);\n        if (this.response) {\n            this.response.config = (0, extend_1.default)(true, {}, this.response.config);\n        }\n        if (this.response) {\n            try {\n                this.response.data = translateData(this.config.responseType, (_b = this.response) === null || _b === void 0 ? void 0 : _b.data);\n            }\n            catch (_c) {\n                // best effort - don't throw an error within an error\n                // we could set `this.response.config.responseType = 'unknown'`, but\n                // that would mutate future calls with this config object.\n            }\n            this.status = this.response.status;\n        }\n        if (error && 'code' in error && error.code) {\n            this.code = error.code;\n        }\n        if (config.errorRedactor) {\n            config.errorRedactor({\n                config: this.config,\n                response: this.response,\n            });\n        }\n    }\n}\nexports.GaxiosError = GaxiosError;\nfunction translateData(responseType, data) {\n    switch (responseType) {\n        case 'stream':\n            return data;\n        case 'json':\n            return JSON.parse(JSON.stringify(data));\n        case 'arraybuffer':\n            return JSON.parse(Buffer.from(data).toString('utf8'));\n        case 'blob':\n            return JSON.parse(data.text());\n        default:\n            return data;\n    }\n}\n/**\n * An experimental error redactor.\n *\n * @param config Config to potentially redact properties of\n * @param response Config to potentially redact properties of\n *\n * @experimental\n */\nfunction defaultErrorRedactor(data) {\n    const REDACT = '<<REDACTED> - See `errorRedactor` option in `gaxios` for configuration>.';\n    function redactHeaders(headers) {\n        if (!headers)\n            return;\n        for (const key of Object.keys(headers)) {\n            // any casing of `Authentication`\n            if (/^authentication$/i.test(key)) {\n                headers[key] = REDACT;\n            }\n            // any casing of `Authorization`\n            if (/^authorization$/i.test(key)) {\n                headers[key] = REDACT;\n            }\n            // anything containing secret, such as 'client secret'\n            if (/secret/i.test(key)) {\n                headers[key] = REDACT;\n            }\n        }\n    }\n    function redactString(obj, key) {\n        if (typeof obj === 'object' &&\n            obj !== null &&\n            typeof obj[key] === 'string') {\n            const text = obj[key];\n            if (/grant_type=/i.test(text) ||\n                /assertion=/i.test(text) ||\n                /secret/i.test(text)) {\n                obj[key] = REDACT;\n            }\n        }\n    }\n    function redactObject(obj) {\n        if (typeof obj === 'object' && obj !== null) {\n            if ('grant_type' in obj) {\n                obj['grant_type'] = REDACT;\n            }\n            if ('assertion' in obj) {\n                obj['assertion'] = REDACT;\n            }\n            if ('client_secret' in obj) {\n                obj['client_secret'] = REDACT;\n            }\n        }\n    }\n    if (data.config) {\n        redactHeaders(data.config.headers);\n        redactString(data.config, 'data');\n        redactObject(data.config.data);\n        redactString(data.config, 'body');\n        redactObject(data.config.body);\n        try {\n            const url = new url_1.URL('', data.config.url);\n            if (url.searchParams.has('token')) {\n                url.searchParams.set('token', REDACT);\n            }\n            if (url.searchParams.has('client_secret')) {\n                url.searchParams.set('client_secret', REDACT);\n            }\n            data.config.url = url.toString();\n        }\n        catch (_b) {\n            // ignore error - no need to parse an invalid URL\n        }\n    }\n    if (data.response) {\n        defaultErrorRedactor({ config: data.response.config });\n        redactHeaders(data.response.headers);\n        redactString(data.response, 'data');\n        redactObject(data.response.data);\n    }\n    return data;\n}\n//# sourceMappingURL=common.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/gaxios@6.7.1/node_modules/gaxios/build/src/common.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/gaxios@6.7.1/node_modules/gaxios/build/src/gaxios.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/.pnpm/gaxios@6.7.1/node_modules/gaxios/build/src/gaxios.js ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n// Copyright 2018 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nvar __classPrivateFieldGet = (this && this.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar __classPrivateFieldSet = (this && this.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nvar _Gaxios_instances, _a, _Gaxios_urlMayUseProxy, _Gaxios_applyRequestInterceptors, _Gaxios_applyResponseInterceptors, _Gaxios_prepareRequest, _Gaxios_proxyAgent, _Gaxios_getProxyAgent;\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Gaxios = void 0;\nconst extend_1 = __importDefault(__webpack_require__(/*! extend */ \"(rsc)/./node_modules/.pnpm/extend@3.0.2/node_modules/extend/index.js\"));\nconst https_1 = __webpack_require__(/*! https */ \"https\");\nconst node_fetch_1 = __importDefault(__webpack_require__(/*! node-fetch */ \"(rsc)/./node_modules/.pnpm/node-fetch@2.7.0/node_modules/node-fetch/lib/index.mjs\"));\nconst querystring_1 = __importDefault(__webpack_require__(/*! querystring */ \"querystring\"));\nconst is_stream_1 = __importDefault(__webpack_require__(/*! is-stream */ \"(rsc)/./node_modules/.pnpm/is-stream@2.0.1/node_modules/is-stream/index.js\"));\nconst url_1 = __webpack_require__(/*! url */ \"url\");\nconst common_1 = __webpack_require__(/*! ./common */ \"(rsc)/./node_modules/.pnpm/gaxios@6.7.1/node_modules/gaxios/build/src/common.js\");\nconst retry_1 = __webpack_require__(/*! ./retry */ \"(rsc)/./node_modules/.pnpm/gaxios@6.7.1/node_modules/gaxios/build/src/retry.js\");\nconst stream_1 = __webpack_require__(/*! stream */ \"stream\");\nconst uuid_1 = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/.pnpm/uuid@9.0.1/node_modules/uuid/dist/esm-node/index.js\");\nconst interceptor_1 = __webpack_require__(/*! ./interceptor */ \"(rsc)/./node_modules/.pnpm/gaxios@6.7.1/node_modules/gaxios/build/src/interceptor.js\");\n/* eslint-disable @typescript-eslint/no-explicit-any */\nconst fetch = hasFetch() ? window.fetch : node_fetch_1.default;\nfunction hasWindow() {\n    return typeof window !== 'undefined' && !!window;\n}\nfunction hasFetch() {\n    return hasWindow() && !!window.fetch;\n}\nfunction hasBuffer() {\n    return typeof Buffer !== 'undefined';\n}\nfunction hasHeader(options, header) {\n    return !!getHeader(options, header);\n}\nfunction getHeader(options, header) {\n    header = header.toLowerCase();\n    for (const key of Object.keys((options === null || options === void 0 ? void 0 : options.headers) || {})) {\n        if (header === key.toLowerCase()) {\n            return options.headers[key];\n        }\n    }\n    return undefined;\n}\nclass Gaxios {\n    /**\n     * The Gaxios class is responsible for making HTTP requests.\n     * @param defaults The default set of options to be used for this instance.\n     */\n    constructor(defaults) {\n        _Gaxios_instances.add(this);\n        this.agentCache = new Map();\n        this.defaults = defaults || {};\n        this.interceptors = {\n            request: new interceptor_1.GaxiosInterceptorManager(),\n            response: new interceptor_1.GaxiosInterceptorManager(),\n        };\n    }\n    /**\n     * Perform an HTTP request with the given options.\n     * @param opts Set of HTTP options that will be used for this HTTP request.\n     */\n    async request(opts = {}) {\n        opts = await __classPrivateFieldGet(this, _Gaxios_instances, \"m\", _Gaxios_prepareRequest).call(this, opts);\n        opts = await __classPrivateFieldGet(this, _Gaxios_instances, \"m\", _Gaxios_applyRequestInterceptors).call(this, opts);\n        return __classPrivateFieldGet(this, _Gaxios_instances, \"m\", _Gaxios_applyResponseInterceptors).call(this, this._request(opts));\n    }\n    async _defaultAdapter(opts) {\n        const fetchImpl = opts.fetchImplementation || fetch;\n        const res = (await fetchImpl(opts.url, opts));\n        const data = await this.getResponseData(opts, res);\n        return this.translateResponse(opts, res, data);\n    }\n    /**\n     * Internal, retryable version of the `request` method.\n     * @param opts Set of HTTP options that will be used for this HTTP request.\n     */\n    async _request(opts = {}) {\n        var _b;\n        try {\n            let translatedResponse;\n            if (opts.adapter) {\n                translatedResponse = await opts.adapter(opts, this._defaultAdapter.bind(this));\n            }\n            else {\n                translatedResponse = await this._defaultAdapter(opts);\n            }\n            if (!opts.validateStatus(translatedResponse.status)) {\n                if (opts.responseType === 'stream') {\n                    let response = '';\n                    await new Promise(resolve => {\n                        (translatedResponse === null || translatedResponse === void 0 ? void 0 : translatedResponse.data).on('data', chunk => {\n                            response += chunk;\n                        });\n                        (translatedResponse === null || translatedResponse === void 0 ? void 0 : translatedResponse.data).on('end', resolve);\n                    });\n                    translatedResponse.data = response;\n                }\n                throw new common_1.GaxiosError(`Request failed with status code ${translatedResponse.status}`, opts, translatedResponse);\n            }\n            return translatedResponse;\n        }\n        catch (e) {\n            const err = e instanceof common_1.GaxiosError\n                ? e\n                : new common_1.GaxiosError(e.message, opts, undefined, e);\n            const { shouldRetry, config } = await (0, retry_1.getRetryConfig)(err);\n            if (shouldRetry && config) {\n                err.config.retryConfig.currentRetryAttempt =\n                    config.retryConfig.currentRetryAttempt;\n                // The error's config could be redacted - therefore we only want to\n                // copy the retry state over to the existing config\n                opts.retryConfig = (_b = err.config) === null || _b === void 0 ? void 0 : _b.retryConfig;\n                return this._request(opts);\n            }\n            throw err;\n        }\n    }\n    async getResponseData(opts, res) {\n        switch (opts.responseType) {\n            case 'stream':\n                return res.body;\n            case 'json': {\n                let data = await res.text();\n                try {\n                    data = JSON.parse(data);\n                }\n                catch (_b) {\n                    // continue\n                }\n                return data;\n            }\n            case 'arraybuffer':\n                return res.arrayBuffer();\n            case 'blob':\n                return res.blob();\n            case 'text':\n                return res.text();\n            default:\n                return this.getResponseDataFromContentType(res);\n        }\n    }\n    /**\n     * By default, throw for any non-2xx status code\n     * @param status status code from the HTTP response\n     */\n    validateStatus(status) {\n        return status >= 200 && status < 300;\n    }\n    /**\n     * Encode a set of key/value pars into a querystring format (?foo=bar&baz=boo)\n     * @param params key value pars to encode\n     */\n    paramsSerializer(params) {\n        return querystring_1.default.stringify(params);\n    }\n    translateResponse(opts, res, data) {\n        // headers need to be converted from a map to an obj\n        const headers = {};\n        res.headers.forEach((value, key) => {\n            headers[key] = value;\n        });\n        return {\n            config: opts,\n            data: data,\n            headers,\n            status: res.status,\n            statusText: res.statusText,\n            // XMLHttpRequestLike\n            request: {\n                responseURL: res.url,\n            },\n        };\n    }\n    /**\n     * Attempts to parse a response by looking at the Content-Type header.\n     * @param {FetchResponse} response the HTTP response.\n     * @returns {Promise<any>} a promise that resolves to the response data.\n     */\n    async getResponseDataFromContentType(response) {\n        let contentType = response.headers.get('Content-Type');\n        if (contentType === null) {\n            // Maintain existing functionality by calling text()\n            return response.text();\n        }\n        contentType = contentType.toLowerCase();\n        if (contentType.includes('application/json')) {\n            let data = await response.text();\n            try {\n                data = JSON.parse(data);\n            }\n            catch (_b) {\n                // continue\n            }\n            return data;\n        }\n        else if (contentType.match(/^text\\//)) {\n            return response.text();\n        }\n        else {\n            // If the content type is something not easily handled, just return the raw data (blob)\n            return response.blob();\n        }\n    }\n    /**\n     * Creates an async generator that yields the pieces of a multipart/related request body.\n     * This implementation follows the spec: https://www.ietf.org/rfc/rfc2387.txt. However, recursive\n     * multipart/related requests are not currently supported.\n     *\n     * @param {GaxioMultipartOptions[]} multipartOptions the pieces to turn into a multipart/related body.\n     * @param {string} boundary the boundary string to be placed between each part.\n     */\n    async *getMultipartRequest(multipartOptions, boundary) {\n        const finale = `--${boundary}--`;\n        for (const currentPart of multipartOptions) {\n            const partContentType = currentPart.headers['Content-Type'] || 'application/octet-stream';\n            const preamble = `--${boundary}\\r\\nContent-Type: ${partContentType}\\r\\n\\r\\n`;\n            yield preamble;\n            if (typeof currentPart.content === 'string') {\n                yield currentPart.content;\n            }\n            else {\n                yield* currentPart.content;\n            }\n            yield '\\r\\n';\n        }\n        yield finale;\n    }\n}\nexports.Gaxios = Gaxios;\n_a = Gaxios, _Gaxios_instances = new WeakSet(), _Gaxios_urlMayUseProxy = function _Gaxios_urlMayUseProxy(url, noProxy = []) {\n    var _b, _c;\n    const candidate = new url_1.URL(url);\n    const noProxyList = [...noProxy];\n    const noProxyEnvList = ((_c = ((_b = process.env.NO_PROXY) !== null && _b !== void 0 ? _b : process.env.no_proxy)) === null || _c === void 0 ? void 0 : _c.split(',')) || [];\n    for (const rule of noProxyEnvList) {\n        noProxyList.push(rule.trim());\n    }\n    for (const rule of noProxyList) {\n        // Match regex\n        if (rule instanceof RegExp) {\n            if (rule.test(candidate.toString())) {\n                return false;\n            }\n        }\n        // Match URL\n        else if (rule instanceof url_1.URL) {\n            if (rule.origin === candidate.origin) {\n                return false;\n            }\n        }\n        // Match string regex\n        else if (rule.startsWith('*.') || rule.startsWith('.')) {\n            const cleanedRule = rule.replace(/^\\*\\./, '.');\n            if (candidate.hostname.endsWith(cleanedRule)) {\n                return false;\n            }\n        }\n        // Basic string match\n        else if (rule === candidate.origin ||\n            rule === candidate.hostname ||\n            rule === candidate.href) {\n            return false;\n        }\n    }\n    return true;\n}, _Gaxios_applyRequestInterceptors = \n/**\n * Applies the request interceptors. The request interceptors are applied after the\n * call to prepareRequest is completed.\n *\n * @param {GaxiosOptions} options The current set of options.\n *\n * @returns {Promise<GaxiosOptions>} Promise that resolves to the set of options or response after interceptors are applied.\n */\nasync function _Gaxios_applyRequestInterceptors(options) {\n    let promiseChain = Promise.resolve(options);\n    for (const interceptor of this.interceptors.request.values()) {\n        if (interceptor) {\n            promiseChain = promiseChain.then(interceptor.resolved, interceptor.rejected);\n        }\n    }\n    return promiseChain;\n}, _Gaxios_applyResponseInterceptors = \n/**\n * Applies the response interceptors. The response interceptors are applied after the\n * call to request is made.\n *\n * @param {GaxiosOptions} options The current set of options.\n *\n * @returns {Promise<GaxiosOptions>} Promise that resolves to the set of options or response after interceptors are applied.\n */\nasync function _Gaxios_applyResponseInterceptors(response) {\n    let promiseChain = Promise.resolve(response);\n    for (const interceptor of this.interceptors.response.values()) {\n        if (interceptor) {\n            promiseChain = promiseChain.then(interceptor.resolved, interceptor.rejected);\n        }\n    }\n    return promiseChain;\n}, _Gaxios_prepareRequest = \n/**\n * Validates the options, merges them with defaults, and prepare request.\n *\n * @param options The original options passed from the client.\n * @returns Prepared options, ready to make a request\n */\nasync function _Gaxios_prepareRequest(options) {\n    var _b, _c, _d, _e;\n    const opts = (0, extend_1.default)(true, {}, this.defaults, options);\n    if (!opts.url) {\n        throw new Error('URL is required.');\n    }\n    // baseUrl has been deprecated, remove in 2.0\n    const baseUrl = opts.baseUrl || opts.baseURL;\n    if (baseUrl) {\n        opts.url = baseUrl.toString() + opts.url;\n    }\n    opts.paramsSerializer = opts.paramsSerializer || this.paramsSerializer;\n    if (opts.params && Object.keys(opts.params).length > 0) {\n        let additionalQueryParams = opts.paramsSerializer(opts.params);\n        if (additionalQueryParams.startsWith('?')) {\n            additionalQueryParams = additionalQueryParams.slice(1);\n        }\n        const prefix = opts.url.toString().includes('?') ? '&' : '?';\n        opts.url = opts.url + prefix + additionalQueryParams;\n    }\n    if (typeof options.maxContentLength === 'number') {\n        opts.size = options.maxContentLength;\n    }\n    if (typeof options.maxRedirects === 'number') {\n        opts.follow = options.maxRedirects;\n    }\n    opts.headers = opts.headers || {};\n    if (opts.multipart === undefined && opts.data) {\n        const isFormData = typeof FormData === 'undefined'\n            ? false\n            : (opts === null || opts === void 0 ? void 0 : opts.data) instanceof FormData;\n        if (is_stream_1.default.readable(opts.data)) {\n            opts.body = opts.data;\n        }\n        else if (hasBuffer() && Buffer.isBuffer(opts.data)) {\n            // Do not attempt to JSON.stringify() a Buffer:\n            opts.body = opts.data;\n            if (!hasHeader(opts, 'Content-Type')) {\n                opts.headers['Content-Type'] = 'application/json';\n            }\n        }\n        else if (typeof opts.data === 'object') {\n            // If www-form-urlencoded content type has been set, but data is\n            // provided as an object, serialize the content using querystring:\n            if (!isFormData) {\n                if (getHeader(opts, 'content-type') ===\n                    'application/x-www-form-urlencoded') {\n                    opts.body = opts.paramsSerializer(opts.data);\n                }\n                else {\n                    // } else if (!(opts.data instanceof FormData)) {\n                    if (!hasHeader(opts, 'Content-Type')) {\n                        opts.headers['Content-Type'] = 'application/json';\n                    }\n                    opts.body = JSON.stringify(opts.data);\n                }\n            }\n        }\n        else {\n            opts.body = opts.data;\n        }\n    }\n    else if (opts.multipart && opts.multipart.length > 0) {\n        // note: once the minimum version reaches Node 16,\n        // this can be replaced with randomUUID() function from crypto\n        // and the dependency on UUID removed\n        const boundary = (0, uuid_1.v4)();\n        opts.headers['Content-Type'] = `multipart/related; boundary=${boundary}`;\n        const bodyStream = new stream_1.PassThrough();\n        opts.body = bodyStream;\n        (0, stream_1.pipeline)(this.getMultipartRequest(opts.multipart, boundary), bodyStream, () => { });\n    }\n    opts.validateStatus = opts.validateStatus || this.validateStatus;\n    opts.responseType = opts.responseType || 'unknown';\n    if (!opts.headers['Accept'] && opts.responseType === 'json') {\n        opts.headers['Accept'] = 'application/json';\n    }\n    opts.method = opts.method || 'GET';\n    const proxy = opts.proxy ||\n        ((_b = process === null || process === void 0 ? void 0 : process.env) === null || _b === void 0 ? void 0 : _b.HTTPS_PROXY) ||\n        ((_c = process === null || process === void 0 ? void 0 : process.env) === null || _c === void 0 ? void 0 : _c.https_proxy) ||\n        ((_d = process === null || process === void 0 ? void 0 : process.env) === null || _d === void 0 ? void 0 : _d.HTTP_PROXY) ||\n        ((_e = process === null || process === void 0 ? void 0 : process.env) === null || _e === void 0 ? void 0 : _e.http_proxy);\n    const urlMayUseProxy = __classPrivateFieldGet(this, _Gaxios_instances, \"m\", _Gaxios_urlMayUseProxy).call(this, opts.url, opts.noProxy);\n    if (opts.agent) {\n        // don't do any of the following options - use the user-provided agent.\n    }\n    else if (proxy && urlMayUseProxy) {\n        const HttpsProxyAgent = await __classPrivateFieldGet(_a, _a, \"m\", _Gaxios_getProxyAgent).call(_a);\n        if (this.agentCache.has(proxy)) {\n            opts.agent = this.agentCache.get(proxy);\n        }\n        else {\n            opts.agent = new HttpsProxyAgent(proxy, {\n                cert: opts.cert,\n                key: opts.key,\n            });\n            this.agentCache.set(proxy, opts.agent);\n        }\n    }\n    else if (opts.cert && opts.key) {\n        // Configure client for mTLS\n        if (this.agentCache.has(opts.key)) {\n            opts.agent = this.agentCache.get(opts.key);\n        }\n        else {\n            opts.agent = new https_1.Agent({\n                cert: opts.cert,\n                key: opts.key,\n            });\n            this.agentCache.set(opts.key, opts.agent);\n        }\n    }\n    if (typeof opts.errorRedactor !== 'function' &&\n        opts.errorRedactor !== false) {\n        opts.errorRedactor = common_1.defaultErrorRedactor;\n    }\n    return opts;\n}, _Gaxios_getProxyAgent = async function _Gaxios_getProxyAgent() {\n    __classPrivateFieldSet(this, _a, __classPrivateFieldGet(this, _a, \"f\", _Gaxios_proxyAgent) || (await Promise.resolve().then(() => __importStar(__webpack_require__(/*! https-proxy-agent */ \"(rsc)/./node_modules/.pnpm/https-proxy-agent@7.0.6/node_modules/https-proxy-agent/dist/index.js\")))).HttpsProxyAgent, \"f\", _Gaxios_proxyAgent);\n    return __classPrivateFieldGet(this, _a, \"f\", _Gaxios_proxyAgent);\n};\n/**\n * A cache for the lazily-loaded proxy agent.\n *\n * Should use {@link Gaxios[#getProxyAgent]} to retrieve.\n */\n// using `import` to dynamically import the types here\n_Gaxios_proxyAgent = { value: void 0 };\n//# sourceMappingURL=gaxios.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/gaxios@6.7.1/node_modules/gaxios/build/src/gaxios.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/gaxios@6.7.1/node_modules/gaxios/build/src/index.js":
/*!********************************************************************************!*\
  !*** ./node_modules/.pnpm/gaxios@6.7.1/node_modules/gaxios/build/src/index.js ***!
  \********************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n// Copyright 2018 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.instance = exports.Gaxios = exports.GaxiosError = void 0;\nexports.request = request;\nconst gaxios_1 = __webpack_require__(/*! ./gaxios */ \"(rsc)/./node_modules/.pnpm/gaxios@6.7.1/node_modules/gaxios/build/src/gaxios.js\");\nObject.defineProperty(exports, \"Gaxios\", ({ enumerable: true, get: function () { return gaxios_1.Gaxios; } }));\nvar common_1 = __webpack_require__(/*! ./common */ \"(rsc)/./node_modules/.pnpm/gaxios@6.7.1/node_modules/gaxios/build/src/common.js\");\nObject.defineProperty(exports, \"GaxiosError\", ({ enumerable: true, get: function () { return common_1.GaxiosError; } }));\n__exportStar(__webpack_require__(/*! ./interceptor */ \"(rsc)/./node_modules/.pnpm/gaxios@6.7.1/node_modules/gaxios/build/src/interceptor.js\"), exports);\n/**\n * The default instance used when the `request` method is directly\n * invoked.\n */\nexports.instance = new gaxios_1.Gaxios();\n/**\n * Make an HTTP request using the given options.\n * @param opts Options for the request\n */\nasync function request(opts) {\n    return exports.instance.request(opts);\n}\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/gaxios@6.7.1/node_modules/gaxios/build/src/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/gaxios@6.7.1/node_modules/gaxios/build/src/interceptor.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/.pnpm/gaxios@6.7.1/node_modules/gaxios/build/src/interceptor.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n// Copyright 2024 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.GaxiosInterceptorManager = void 0;\n/**\n * Class to manage collections of GaxiosInterceptors for both requests and responses.\n */\nclass GaxiosInterceptorManager extends Set {\n}\nexports.GaxiosInterceptorManager = GaxiosInterceptorManager;\n//# sourceMappingURL=interceptor.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vZ2F4aW9zQDYuNy4xL25vZGVfbW9kdWxlcy9nYXhpb3MvYnVpbGQvc3JjL2ludGVyY2VwdG9yLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGdDQUFnQztBQUNoQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDO0FBQ2hDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFBDLUxQS1xcRG9jdW1lbnRzXFxIS1VZQVxcYXR0ZW5kYW5jZVxcbm9kZV9tb2R1bGVzXFwucG5wbVxcZ2F4aW9zQDYuNy4xXFxub2RlX21vZHVsZXNcXGdheGlvc1xcYnVpbGRcXHNyY1xcaW50ZXJjZXB0b3IuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vLyBDb3B5cmlnaHQgMjAyNCBHb29nbGUgTExDXG4vLyBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuLy8geW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuLy8gWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4vL1xuLy8gICAgaHR0cDovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4vL1xuLy8gVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuLy8gZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuLy8gV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4vLyBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4vLyBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuR2F4aW9zSW50ZXJjZXB0b3JNYW5hZ2VyID0gdm9pZCAwO1xuLyoqXG4gKiBDbGFzcyB0byBtYW5hZ2UgY29sbGVjdGlvbnMgb2YgR2F4aW9zSW50ZXJjZXB0b3JzIGZvciBib3RoIHJlcXVlc3RzIGFuZCByZXNwb25zZXMuXG4gKi9cbmNsYXNzIEdheGlvc0ludGVyY2VwdG9yTWFuYWdlciBleHRlbmRzIFNldCB7XG59XG5leHBvcnRzLkdheGlvc0ludGVyY2VwdG9yTWFuYWdlciA9IEdheGlvc0ludGVyY2VwdG9yTWFuYWdlcjtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWludGVyY2VwdG9yLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/gaxios@6.7.1/node_modules/gaxios/build/src/interceptor.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/gaxios@6.7.1/node_modules/gaxios/build/src/retry.js":
/*!********************************************************************************!*\
  !*** ./node_modules/.pnpm/gaxios@6.7.1/node_modules/gaxios/build/src/retry.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n// Copyright 2018 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getRetryConfig = getRetryConfig;\nasync function getRetryConfig(err) {\n    let config = getConfig(err);\n    if (!err || !err.config || (!config && !err.config.retry)) {\n        return { shouldRetry: false };\n    }\n    config = config || {};\n    config.currentRetryAttempt = config.currentRetryAttempt || 0;\n    config.retry =\n        config.retry === undefined || config.retry === null ? 3 : config.retry;\n    config.httpMethodsToRetry = config.httpMethodsToRetry || [\n        'GET',\n        'HEAD',\n        'PUT',\n        'OPTIONS',\n        'DELETE',\n    ];\n    config.noResponseRetries =\n        config.noResponseRetries === undefined || config.noResponseRetries === null\n            ? 2\n            : config.noResponseRetries;\n    config.retryDelayMultiplier = config.retryDelayMultiplier\n        ? config.retryDelayMultiplier\n        : 2;\n    config.timeOfFirstRequest = config.timeOfFirstRequest\n        ? config.timeOfFirstRequest\n        : Date.now();\n    config.totalTimeout = config.totalTimeout\n        ? config.totalTimeout\n        : Number.MAX_SAFE_INTEGER;\n    config.maxRetryDelay = config.maxRetryDelay\n        ? config.maxRetryDelay\n        : Number.MAX_SAFE_INTEGER;\n    // If this wasn't in the list of status codes where we want\n    // to automatically retry, return.\n    const retryRanges = [\n        // https://en.wikipedia.org/wiki/List_of_HTTP_status_codes\n        // 1xx - Retry (Informational, request still processing)\n        // 2xx - Do not retry (Success)\n        // 3xx - Do not retry (Redirect)\n        // 4xx - Do not retry (Client errors)\n        // 408 - Retry (\"Request Timeout\")\n        // 429 - Retry (\"Too Many Requests\")\n        // 5xx - Retry (Server errors)\n        [100, 199],\n        [408, 408],\n        [429, 429],\n        [500, 599],\n    ];\n    config.statusCodesToRetry = config.statusCodesToRetry || retryRanges;\n    // Put the config back into the err\n    err.config.retryConfig = config;\n    // Determine if we should retry the request\n    const shouldRetryFn = config.shouldRetry || shouldRetryRequest;\n    if (!(await shouldRetryFn(err))) {\n        return { shouldRetry: false, config: err.config };\n    }\n    const delay = getNextRetryDelay(config);\n    // We're going to retry!  Incremenent the counter.\n    err.config.retryConfig.currentRetryAttempt += 1;\n    // Create a promise that invokes the retry after the backOffDelay\n    const backoff = config.retryBackoff\n        ? config.retryBackoff(err, delay)\n        : new Promise(resolve => {\n            setTimeout(resolve, delay);\n        });\n    // Notify the user if they added an `onRetryAttempt` handler\n    if (config.onRetryAttempt) {\n        config.onRetryAttempt(err);\n    }\n    // Return the promise in which recalls Gaxios to retry the request\n    await backoff;\n    return { shouldRetry: true, config: err.config };\n}\n/**\n * Determine based on config if we should retry the request.\n * @param err The GaxiosError passed to the interceptor.\n */\nfunction shouldRetryRequest(err) {\n    var _a;\n    const config = getConfig(err);\n    // node-fetch raises an AbortError if signaled:\n    // https://github.com/bitinn/node-fetch#request-cancellation-with-abortsignal\n    if (err.name === 'AbortError' || ((_a = err.error) === null || _a === void 0 ? void 0 : _a.name) === 'AbortError') {\n        return false;\n    }\n    // If there's no config, or retries are disabled, return.\n    if (!config || config.retry === 0) {\n        return false;\n    }\n    // Check if this error has no response (ETIMEDOUT, ENOTFOUND, etc)\n    if (!err.response &&\n        (config.currentRetryAttempt || 0) >= config.noResponseRetries) {\n        return false;\n    }\n    // Only retry with configured HttpMethods.\n    if (!err.config.method ||\n        config.httpMethodsToRetry.indexOf(err.config.method.toUpperCase()) < 0) {\n        return false;\n    }\n    // If this wasn't in the list of status codes where we want\n    // to automatically retry, return.\n    if (err.response && err.response.status) {\n        let isInRange = false;\n        for (const [min, max] of config.statusCodesToRetry) {\n            const status = err.response.status;\n            if (status >= min && status <= max) {\n                isInRange = true;\n                break;\n            }\n        }\n        if (!isInRange) {\n            return false;\n        }\n    }\n    // If we are out of retry attempts, return\n    config.currentRetryAttempt = config.currentRetryAttempt || 0;\n    if (config.currentRetryAttempt >= config.retry) {\n        return false;\n    }\n    return true;\n}\n/**\n * Acquire the raxConfig object from an GaxiosError if available.\n * @param err The Gaxios error with a config object.\n */\nfunction getConfig(err) {\n    if (err && err.config && err.config.retryConfig) {\n        return err.config.retryConfig;\n    }\n    return;\n}\n/**\n * Gets the delay to wait before the next retry.\n *\n * @param {RetryConfig} config The current set of retry options\n * @returns {number} the amount of ms to wait before the next retry attempt.\n */\nfunction getNextRetryDelay(config) {\n    var _a;\n    // Calculate time to wait with exponential backoff.\n    // If this is the first retry, look for a configured retryDelay.\n    const retryDelay = config.currentRetryAttempt ? 0 : (_a = config.retryDelay) !== null && _a !== void 0 ? _a : 100;\n    // Formula: retryDelay + ((retryDelayMultiplier^currentRetryAttempt - 1 / 2) * 1000)\n    const calculatedDelay = retryDelay +\n        ((Math.pow(config.retryDelayMultiplier, config.currentRetryAttempt) - 1) /\n            2) *\n            1000;\n    const maxAllowableDelay = config.totalTimeout - (Date.now() - config.timeOfFirstRequest);\n    return Math.min(calculatedDelay, maxAllowableDelay, config.maxRetryDelay);\n}\n//# sourceMappingURL=retry.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/gaxios@6.7.1/node_modules/gaxios/build/src/retry.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/gaxios@6.7.1/node_modules/gaxios/build/src/util.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/.pnpm/gaxios@6.7.1/node_modules/gaxios/build/src/util.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n// Copyright 2023 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.pkg = void 0;\nexports.pkg = __webpack_require__(/*! ../../package.json */ \"(rsc)/./node_modules/.pnpm/gaxios@6.7.1/node_modules/gaxios/package.json\");\n//# sourceMappingURL=util.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vZ2F4aW9zQDYuNy4xL25vZGVfbW9kdWxlcy9nYXhpb3MvYnVpbGQvc3JjL3V0aWwuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsV0FBVztBQUNYLHVJQUEyQztBQUMzQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxQQy1MUEtcXERvY3VtZW50c1xcSEtVWUFcXGF0dGVuZGFuY2VcXG5vZGVfbW9kdWxlc1xcLnBucG1cXGdheGlvc0A2LjcuMVxcbm9kZV9tb2R1bGVzXFxnYXhpb3NcXGJ1aWxkXFxzcmNcXHV0aWwuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vLyBDb3B5cmlnaHQgMjAyMyBHb29nbGUgTExDXG4vLyBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuLy8geW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuLy8gWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4vL1xuLy8gICAgaHR0cDovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4vL1xuLy8gVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuLy8gZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuLy8gV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4vLyBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4vLyBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMucGtnID0gdm9pZCAwO1xuZXhwb3J0cy5wa2cgPSByZXF1aXJlKCcuLi8uLi9wYWNrYWdlLmpzb24nKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXV0aWwuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/gaxios@6.7.1/node_modules/gaxios/build/src/util.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/gaxios@6.7.1/node_modules/gaxios/package.json":
/*!**************************************************************************!*\
  !*** ./node_modules/.pnpm/gaxios@6.7.1/node_modules/gaxios/package.json ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"name":"gaxios","version":"6.7.1","description":"A simple common HTTP client specifically for Google APIs and services.","main":"build/src/index.js","types":"build/src/index.d.ts","files":["build/src"],"scripts":{"lint":"gts check","test":"c8 mocha build/test","presystem-test":"npm run compile","system-test":"mocha build/system-test --timeout 80000","compile":"tsc -p .","fix":"gts fix","prepare":"npm run compile","pretest":"npm run compile","webpack":"webpack","prebrowser-test":"npm run compile","browser-test":"node build/browser-test/browser-test-runner.js","docs":"compodoc src/","docs-test":"linkinator docs","predocs-test":"npm run docs","samples-test":"cd samples/ && npm link ../ && npm test && cd ../","prelint":"cd samples; npm link ../; npm install","clean":"gts clean","precompile":"gts clean"},"repository":"googleapis/gaxios","keywords":["google"],"engines":{"node":">=14"},"author":"Google, LLC","license":"Apache-2.0","devDependencies":{"@babel/plugin-proposal-private-methods":"^7.18.6","@compodoc/compodoc":"1.1.19","@types/cors":"^2.8.6","@types/express":"^4.16.1","@types/extend":"^3.0.1","@types/mocha":"^9.0.0","@types/multiparty":"0.0.36","@types/mv":"^2.1.0","@types/ncp":"^2.0.1","@types/node":"^20.0.0","@types/node-fetch":"^2.5.7","@types/sinon":"^17.0.0","@types/tmp":"0.2.6","@types/uuid":"^10.0.0","abort-controller":"^3.0.0","assert":"^2.0.0","browserify":"^17.0.0","c8":"^8.0.0","cheerio":"1.0.0-rc.10","cors":"^2.8.5","execa":"^5.0.0","express":"^4.16.4","form-data":"^4.0.0","gts":"^5.0.0","is-docker":"^2.0.0","karma":"^6.0.0","karma-chrome-launcher":"^3.0.0","karma-coverage":"^2.0.0","karma-firefox-launcher":"^2.0.0","karma-mocha":"^2.0.0","karma-remap-coverage":"^0.1.5","karma-sourcemap-loader":"^0.4.0","karma-webpack":"5.0.0","linkinator":"^3.0.0","mocha":"^8.0.0","multiparty":"^4.2.1","mv":"^2.1.1","ncp":"^2.0.0","nock":"^13.0.0","null-loader":"^4.0.0","puppeteer":"^19.0.0","sinon":"^18.0.0","stream-browserify":"^3.0.0","tmp":"0.2.3","ts-loader":"^8.0.0","typescript":"^5.1.6","webpack":"^5.35.0","webpack-cli":"^4.0.0"},"dependencies":{"extend":"^3.0.2","https-proxy-agent":"^7.0.1","is-stream":"^2.0.0","node-fetch":"^2.6.9","uuid":"^9.0.1"}}');

/***/ })

};
;