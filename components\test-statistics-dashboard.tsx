'use client'

import React, { useState, useMemo } from 'react'
import { generateTestDataSet, calculateTestDataStats } from '../data/test-data-generator'
import { calculateStatistics } from '../utils/statistics'
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts'

interface TestResult {
  basicStats: any
  systemStats: any
  testData: any
  isConsistent: boolean
}

const TestStatisticsDashboard: React.FC = () => {
  const [testResult, setTestResult] = useState<TestResult | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [participantCount, setParticipantCount] = useState(500)
  const [activityCount, setActivityCount] = useState(50)

  const handleRunTest = async () => {
    setIsLoading(true)
    try {
      // 生成測試數據
      const testData = generateTestDataSet(participantCount, activityCount)
      const { participants, activities } = testData
      
      // 計算基礎統計
      const basicStats = calculateTestDataStats(participants, activities)
      
      // 計算系統統計
      const systemStats = calculateStatistics(activities, participants)
      
      // 驗證一致性
      const isConsistent = Math.abs(basicStats.attendanceRate - systemStats.averageAttendanceRate) < 1
      
      setTestResult({
        basicStats,
        systemStats,
        testData,
        isConsistent
      })
    } catch (error) {
      console.error('測試執行失敗:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const categoryData = useMemo(() => {
    if (!testResult) return []
    
    const { participants, activities } = testResult.testData
    const categoryStats: Record<string, { count: number; registrations: number; attendances: number; possible: number }> = {}
    
    participants.forEach((participant: any) => {
      const category = participant.category
      if (!categoryStats[category]) {
        categoryStats[category] = { count: 0, registrations: 0, attendances: 0, possible: 0 }
      }
      categoryStats[category].count++
      
      activities.forEach((activity: any) => {
        categoryStats[category].possible++
        if (participant.registration[activity.date]) {
          categoryStats[category].registrations++
          if (participant.attendance[activity.date]) {
            categoryStats[category].attendances++
          }
        }
      })
    })
    
    return Object.entries(categoryStats).map(([category, stats]) => ({
      category,
      count: stats.count,
      registrationRate: stats.possible > 0 ? (stats.registrations / stats.possible) * 100 : 0,
      attendanceRate: stats.registrations > 0 ? (stats.attendances / stats.registrations) * 100 : 0,
      noShowRate: stats.registrations > 0 ? ((stats.registrations - stats.attendances) / stats.registrations) * 100 : 0
    }))
  }, [testResult])

  const pieData = useMemo(() => {
    if (!testResult) return []
    
    const { basicStats } = testResult
    return [
      { name: '出席', value: basicStats.totalAttendances, color: '#10b981' },
      { name: '爽約', value: basicStats.totalNoShows, color: '#ef4444' },
      { name: '未報名', value: basicStats.totalPossibleRegistrations - basicStats.totalRegistrations, color: '#6b7280' }
    ]
  }, [testResult])

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">統計功能測試儀表板</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            測試報名率、出席率和爽約率的計算功能
          </p>
        </div>
      </div>

      {/* 測試控制面板 */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
        <div className="p-6 pb-4">
          <h3 className="text-lg font-semibold">測試配置</h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">設定測試參數並執行統計功能測試</p>
        </div>
        <div className="px-6 pb-6">
          <div className="flex items-center space-x-4">
            <div className="flex flex-col space-y-2">
              <label className="text-sm font-medium">參加者數量</label>
              <input
                type="number"
                value={participantCount}
                onChange={(e) => setParticipantCount(Number(e.target.value))}
                className="w-32 px-3 py-2 border rounded-md"
                min="10"
                max="1000"
              />
            </div>
            <div className="flex flex-col space-y-2">
              <label className="text-sm font-medium">活動數量</label>
              <input
                type="number"
                value={activityCount}
                onChange={(e) => setActivityCount(Number(e.target.value))}
                className="w-32 px-3 py-2 border rounded-md"
                min="5"
                max="100"
              />
            </div>
            <button 
              onClick={handleRunTest} 
              disabled={isLoading}
              className="mt-6 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? '生成中...' : '執行測試'}
            </button>
          </div>
        </div>
      </div>

      {testResult && (
        <>
          {/* 整體統計卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
              <div className="p-4 pb-2">
                <h4 className="text-sm font-medium text-gray-600">報名率</h4>
              </div>
              <div className="px-4 pb-4">
                <div className="text-2xl font-bold text-blue-600">
                  {testResult.basicStats.registrationRate}%
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  {testResult.basicStats.totalRegistrations} / {testResult.basicStats.totalPossibleRegistrations}
                </p>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
              <div className="p-4 pb-2">
                <h4 className="text-sm font-medium text-gray-600">出席率</h4>
              </div>
              <div className="px-4 pb-4">
                <div className="text-2xl font-bold text-green-600">
                  {testResult.basicStats.attendanceRate}%
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  {testResult.basicStats.totalAttendances} / {testResult.basicStats.totalRegistrations}
                </p>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
              <div className="p-4 pb-2">
                <h4 className="text-sm font-medium text-gray-600">爽約率</h4>
              </div>
              <div className="px-4 pb-4">
                <div className="text-2xl font-bold text-red-600">
                  {testResult.basicStats.noShowRate}%
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  {testResult.basicStats.totalNoShows} / {testResult.basicStats.totalRegistrations}
                </p>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
              <div className="p-4 pb-2">
                <h4 className="text-sm font-medium text-gray-600">數據一致性</h4>
              </div>
              <div className="px-4 pb-4">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  testResult.isConsistent 
                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' 
                    : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                }`}>
                  {testResult.isConsistent ? '✅ 通過' : '❌ 不一致'}
                </span>
                <p className="text-xs text-gray-500 mt-1">
                  系統統計: {testResult.systemStats.averageAttendanceRate.toFixed(2)}%
                </p>
              </div>
            </div>
          </div>

          {/* 圖表區域 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 類別統計柱狀圖 */}
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
              <div className="p-6 pb-4">
                <h3 className="text-lg font-semibold">各類別統計</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">不同參加者類別的報名率、出席率和爽約率</p>
              </div>
              <div className="px-6 pb-6">
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={categoryData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="category" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="registrationRate" fill="#3b82f6" name="報名率" />
                    <Bar dataKey="attendanceRate" fill="#10b981" name="出席率" />
                    <Bar dataKey="noShowRate" fill="#ef4444" name="爽約率" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>

            {/* 出席分布餅圖 */}
            <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
              <div className="p-6 pb-4">
                <h3 className="text-lg font-semibold">出席分布</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">總體出席、爽約和未報名的分布情況</p>
              </div>
              <div className="px-6 pb-6">
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={pieData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {pieData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>

          {/* 詳細統計表格 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
            <div className="p-6 pb-4">
              <h3 className="text-lg font-semibold">詳細統計數據</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">各類別參加者的詳細統計信息</p>
            </div>
            <div className="px-6 pb-6">
              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="border border-gray-300 px-4 py-2 text-left">類別</th>
                      <th className="border border-gray-300 px-4 py-2 text-center">人數</th>
                      <th className="border border-gray-300 px-4 py-2 text-center">報名率</th>
                      <th className="border border-gray-300 px-4 py-2 text-center">出席率</th>
                      <th className="border border-gray-300 px-4 py-2 text-center">爽約率</th>
                    </tr>
                  </thead>
                  <tbody>
                    {categoryData.map((category) => (
                      <tr key={category.category}>
                        <td className="border border-gray-300 px-4 py-2 font-medium">
                          {category.category}
                        </td>
                        <td className="border border-gray-300 px-4 py-2 text-center">
                          {category.count}
                        </td>
                        <td className="border border-gray-300 px-4 py-2 text-center">
                          <div className="flex items-center space-x-2">
                            <div className="flex-1 bg-gray-200 rounded-full h-2">
                              <div 
                                className="bg-blue-600 h-2 rounded-full" 
                                style={{ width: `${Math.min(category.registrationRate, 100)}%` }}
                              ></div>
                            </div>
                            <span className="text-sm">{category.registrationRate.toFixed(1)}%</span>
                          </div>
                        </td>
                        <td className="border border-gray-300 px-4 py-2 text-center">
                          <div className="flex items-center space-x-2">
                            <div className="flex-1 bg-gray-200 rounded-full h-2">
                              <div 
                                className="bg-green-600 h-2 rounded-full" 
                                style={{ width: `${Math.min(category.attendanceRate, 100)}%` }}
                              ></div>
                            </div>
                            <span className="text-sm">{category.attendanceRate.toFixed(1)}%</span>
                          </div>
                        </td>
                        <td className="border border-gray-300 px-4 py-2 text-center">
                          <div className="flex items-center space-x-2">
                            <div className="flex-1 bg-gray-200 rounded-full h-2">
                              <div 
                                className="bg-red-600 h-2 rounded-full" 
                                style={{ width: `${Math.min(category.noShowRate, 100)}%` }}
                              ></div>
                            </div>
                            <span className="text-sm">{category.noShowRate.toFixed(1)}%</span>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  )
}

export default TestStatisticsDashboard