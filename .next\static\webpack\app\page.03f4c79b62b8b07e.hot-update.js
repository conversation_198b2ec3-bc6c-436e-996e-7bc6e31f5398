"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/participant-management-page.tsx":
/*!****************************************************!*\
  !*** ./components/participant-management-page.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ParticipantManagementPage: () => (/* binding */ ParticipantManagementPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var _universal_bulk_import__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./universal-bulk-import */ \"(app-pages-browser)/./components/universal-bulk-import.tsx\");\n/* harmony import */ var _participant_attendance_history__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./participant-attendance-history */ \"(app-pages-browser)/./components/participant-attendance-history.tsx\");\n/* harmony import */ var _utils_activity_level__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils/activity-level */ \"(app-pages-browser)/./utils/activity-level.ts\");\n/* harmony import */ var _utils_statistics__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../utils/statistics */ \"(app-pages-browser)/./utils/statistics.ts\");\n/* __next_internal_client_entry_do_not_use__ ParticipantManagementPage auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction ParticipantManagementPage(param) {\n    let { allParticipants, sessionParticipants, sessions, selectedSessionId, onAddParticipant, onAddSessionParticipant, onBulkAddParticipants, onUpdateParticipant, onUpdateSessionParticipant, onDeleteParticipant, onRemoveFromSession, onBulkDeleteTitle, onBack, activities, activityLevelSettings = _utils_activity_level__WEBPACK_IMPORTED_MODULE_5__.DEFAULT_ACTIVITY_LEVEL_SETTINGS } = param;\n    _s();\n    const [newParticipant, setNewParticipant] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        category: \"\"\n    });\n    const [editingParticipant, setEditingParticipant] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingSessionParticipant, setEditingSessionParticipant] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [categoryFilter, setCategoryFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [showBulkImport, setShowBulkImport] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"session\");\n    const [viewingAttendanceHistory, setViewingAttendanceHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 職銜管理相關狀態\n    const [showTitleManagement, setShowTitleManagement] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingTitle, setEditingTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [newTitle, setNewTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 添加現有參加者優化相關狀態\n    const [showAddExisting, setShowAddExisting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [addExistingSearchTerm, setAddExistingSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [addExistingCategoryFilter, setAddExistingCategoryFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedParticipants, setSelectedParticipants] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [pageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(20);\n    const [sortField, setSortField] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [sortDirection, setSortDirection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"asc\");\n    // 獲取當前屆別\n    const currentSession = sessions.find((s)=>s.id === selectedSessionId);\n    // 獲取當前屆別的參加者\n    const currentSessionParticipants = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ParticipantManagementPage.useMemo[currentSessionParticipants]\": ()=>{\n            return sessionParticipants.filter({\n                \"ParticipantManagementPage.useMemo[currentSessionParticipants]\": (sp)=>sp.sessionId === selectedSessionId\n            }[\"ParticipantManagementPage.useMemo[currentSessionParticipants]\"]);\n        }\n    }[\"ParticipantManagementPage.useMemo[currentSessionParticipants]\"], [\n        sessionParticipants,\n        selectedSessionId\n    ]);\n    // 獲取當前屆別的所有職銜\n    const currentSessionCategories = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ParticipantManagementPage.useMemo[currentSessionCategories]\": ()=>{\n            const categories = new Set(currentSessionParticipants.map({\n                \"ParticipantManagementPage.useMemo[currentSessionCategories]\": (sp)=>sp.category\n            }[\"ParticipantManagementPage.useMemo[currentSessionCategories]\"]).filter(Boolean));\n            return Array.from(categories);\n        }\n    }[\"ParticipantManagementPage.useMemo[currentSessionCategories]\"], [\n        currentSessionParticipants\n    ]);\n    // 獲取全局所有職銜\n    const allCategories = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ParticipantManagementPage.useMemo[allCategories]\": ()=>{\n            const globalCategories = new Set(allParticipants.map({\n                \"ParticipantManagementPage.useMemo[allCategories]\": (p)=>p.category\n            }[\"ParticipantManagementPage.useMemo[allCategories]\"]).filter(Boolean));\n            const sessionCategories = new Set(sessionParticipants.map({\n                \"ParticipantManagementPage.useMemo[allCategories]\": (sp)=>sp.category\n            }[\"ParticipantManagementPage.useMemo[allCategories]\"]).filter(Boolean));\n            return Array.from(new Set([\n                ...globalCategories,\n                ...sessionCategories\n            ]));\n        }\n    }[\"ParticipantManagementPage.useMemo[allCategories]\"], [\n        allParticipants,\n        sessionParticipants\n    ]);\n    // 計算統計數據\n    const statistics = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ParticipantManagementPage.useMemo[statistics]\": ()=>{\n            return (0,_utils_statistics__WEBPACK_IMPORTED_MODULE_6__.calculateStatistics)(activities, allParticipants, sessions);\n        }\n    }[\"ParticipantManagementPage.useMemo[statistics]\"], [\n        activities,\n        allParticipants,\n        sessions\n    ]);\n    const handleSort = (field)=>{\n        if (sortField === field) {\n            setSortDirection(sortDirection === \"asc\" ? \"desc\" : \"asc\");\n        } else {\n            setSortField(field);\n            setSortDirection(\"asc\");\n        }\n    };\n    // 過濾參加者\n    const filteredSessionParticipants = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ParticipantManagementPage.useMemo[filteredSessionParticipants]\": ()=>{\n            const filtered = currentSessionParticipants.filter({\n                \"ParticipantManagementPage.useMemo[filteredSessionParticipants].filtered\": (sp)=>{\n                    const matchesSearch = sp.name.toLowerCase().includes(searchTerm.toLowerCase());\n                    const matchesCategory = categoryFilter ? sp.category === categoryFilter : true;\n                    const matchesStatus = statusFilter === \"all\" || statusFilter === \"active\" && sp.isActive || statusFilter === \"inactive\" && !sp.isActive;\n                    return matchesSearch && matchesCategory && matchesStatus;\n                }\n            }[\"ParticipantManagementPage.useMemo[filteredSessionParticipants].filtered\"]);\n            // 計算每個參加者的出席率和活躍等級\n            const participantsWithActivityLevel = filtered.map({\n                \"ParticipantManagementPage.useMemo[filteredSessionParticipants].participantsWithActivityLevel\": (sp)=>{\n                    // 獲取該參加者在當前屆別的所有活動\n                    const participantActivities = activities.filter({\n                        \"ParticipantManagementPage.useMemo[filteredSessionParticipants].participantsWithActivityLevel.participantActivities\": (activity)=>activity.sessionId === selectedSessionId\n                    }[\"ParticipantManagementPage.useMemo[filteredSessionParticipants].participantsWithActivityLevel.participantActivities\"]);\n                    // 計算該參加者參與的活動數量和出席數量\n                    let totalParticipated = 0;\n                    let totalAttended = 0;\n                    participantActivities.forEach({\n                        \"ParticipantManagementPage.useMemo[filteredSessionParticipants].participantsWithActivityLevel\": (activity)=>{\n                            // 檢查該參加者是否參與了這個活動\n                            const participantInActivity = activity.participants.find({\n                                \"ParticipantManagementPage.useMemo[filteredSessionParticipants].participantsWithActivityLevel.participantInActivity\": (p)=>p.id === sp.participantId\n                            }[\"ParticipantManagementPage.useMemo[filteredSessionParticipants].participantsWithActivityLevel.participantInActivity\"]);\n                            if (participantInActivity) {\n                                totalParticipated++;\n                                // 檢查出席記錄 - 嘗試多種可能的鍵\n                                const attendanceRecord = participantInActivity.attendance;\n                                let isAttended = false;\n                                // 嘗試不同的鍵格式\n                                if (attendanceRecord[activity.id] === true) {\n                                    isAttended = true;\n                                } else if (attendanceRecord[activity.date] === true) {\n                                    isAttended = true;\n                                } else if (attendanceRecord[\"\".concat(activity.date)] === true) {\n                                    isAttended = true;\n                                }\n                                if (isAttended) {\n                                    totalAttended++;\n                                }\n                            }\n                        }\n                    }[\"ParticipantManagementPage.useMemo[filteredSessionParticipants].participantsWithActivityLevel\"]);\n                    const attendanceRate = totalParticipated > 0 ? totalAttended / totalParticipated * 100 : 0;\n                    const activityLevel = (0,_utils_activity_level__WEBPACK_IMPORTED_MODULE_5__.getActivityLevel)(attendanceRate, activityLevelSettings);\n                    return {\n                        ...sp,\n                        attendanceRate,\n                        activityLevel,\n                        totalParticipated,\n                        totalAttended\n                    };\n                }\n            }[\"ParticipantManagementPage.useMemo[filteredSessionParticipants].participantsWithActivityLevel\"]);\n            // 排序邏輯\n            if (sortField) {\n                participantsWithActivityLevel.sort({\n                    \"ParticipantManagementPage.useMemo[filteredSessionParticipants]\": (a, b)=>{\n                        let aValue = \"\";\n                        let bValue = \"\";\n                        switch(sortField){\n                            case \"name\":\n                                aValue = a.name.toLowerCase();\n                                bValue = b.name.toLowerCase();\n                                break;\n                            case \"category\":\n                                aValue = a.category.toLowerCase();\n                                bValue = b.category.toLowerCase();\n                                break;\n                            case \"joinDate\":\n                                aValue = a.joinDate || \"\";\n                                bValue = b.joinDate || \"\";\n                                break;\n                            case \"status\":\n                                aValue = a.isActive ? \"active\" : \"inactive\";\n                                bValue = b.isActive ? \"active\" : \"inactive\";\n                                break;\n                            case \"attendanceRate\":\n                                aValue = a.attendanceRate;\n                                bValue = b.attendanceRate;\n                                break;\n                            default:\n                                return 0;\n                        }\n                        if (aValue < bValue) return sortDirection === \"asc\" ? -1 : 1;\n                        if (aValue > bValue) return sortDirection === \"asc\" ? 1 : -1;\n                        return 0;\n                    }\n                }[\"ParticipantManagementPage.useMemo[filteredSessionParticipants]\"]);\n            }\n            return participantsWithActivityLevel;\n        }\n    }[\"ParticipantManagementPage.useMemo[filteredSessionParticipants]\"], [\n        currentSessionParticipants,\n        searchTerm,\n        categoryFilter,\n        statusFilter,\n        sortField,\n        sortDirection,\n        activities,\n        selectedSessionId,\n        activityLevelSettings\n    ]);\n    // 獲取不在當前屆別的全局參加者（優化版）\n    const availableGlobalParticipants = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ParticipantManagementPage.useMemo[availableGlobalParticipants]\": ()=>{\n            const sessionParticipantIds = new Set(currentSessionParticipants.map({\n                \"ParticipantManagementPage.useMemo[availableGlobalParticipants]\": (sp)=>sp.participantId\n            }[\"ParticipantManagementPage.useMemo[availableGlobalParticipants]\"]));\n            return allParticipants.filter({\n                \"ParticipantManagementPage.useMemo[availableGlobalParticipants]\": (p)=>!sessionParticipantIds.has(p.id)\n            }[\"ParticipantManagementPage.useMemo[availableGlobalParticipants]\"]);\n        }\n    }[\"ParticipantManagementPage.useMemo[availableGlobalParticipants]\"], [\n        allParticipants,\n        currentSessionParticipants\n    ]);\n    // 過濾和分頁的可用參加者\n    const filteredAvailableParticipants = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ParticipantManagementPage.useMemo[filteredAvailableParticipants]\": ()=>{\n            const filtered = availableGlobalParticipants.filter({\n                \"ParticipantManagementPage.useMemo[filteredAvailableParticipants].filtered\": (participant)=>{\n                    const matchesSearch = participant.name.toLowerCase().includes(addExistingSearchTerm.toLowerCase());\n                    const matchesCategory = addExistingCategoryFilter ? participant.category === addExistingCategoryFilter : true;\n                    return matchesSearch && matchesCategory;\n                }\n            }[\"ParticipantManagementPage.useMemo[filteredAvailableParticipants].filtered\"]);\n            const startIndex = (currentPage - 1) * pageSize;\n            const endIndex = startIndex + pageSize;\n            return {\n                participants: filtered.slice(startIndex, endIndex),\n                total: filtered.length,\n                totalPages: Math.ceil(filtered.length / pageSize)\n            };\n        }\n    }[\"ParticipantManagementPage.useMemo[filteredAvailableParticipants]\"], [\n        availableGlobalParticipants,\n        addExistingSearchTerm,\n        addExistingCategoryFilter,\n        currentPage,\n        pageSize\n    ]);\n    // 處理新增參加者\n    const handleAddParticipant = ()=>{\n        if (newParticipant.name.trim()) {\n            try {\n                // 處理新增職銜的情況\n                let finalCategory = newParticipant.category;\n                if (finalCategory === \"新增職銜\") {\n                    finalCategory = \"\" // 如果選擇了新增職銜但沒有輸入具體職銜，設為空\n                    ;\n                }\n                const participantToAdd = {\n                    name: newParticipant.name.trim(),\n                    category: finalCategory\n                };\n                console.log('Adding participant:', participantToAdd) // 調試日誌\n                ;\n                onAddParticipant(participantToAdd);\n                setNewParticipant({\n                    name: \"\",\n                    category: \"\"\n                });\n                // 顯示成功提示\n                alert(\"成功新增參加者「\".concat(participantToAdd.name, \"」\"));\n            } catch (error) {\n                console.error('新增參加者時發生錯誤:', error);\n                alert(\"新增參加者時發生錯誤: \".concat(error instanceof Error ? error.message : '未知錯誤'));\n            }\n        }\n    };\n    // 處理更新參加者\n    const handleUpdateParticipant = ()=>{\n        if (editingParticipant) {\n            onUpdateParticipant(editingParticipant);\n            setEditingParticipant(null);\n        }\n    };\n    // 處理更新屆別參加者\n    const handleUpdateSessionParticipant = ()=>{\n        if (editingSessionParticipant) {\n            onUpdateSessionParticipant(editingSessionParticipant);\n            setEditingSessionParticipant(null);\n        }\n    };\n    // 處理批量導入\n    const handleBulkImport = (participants)=>{\n        onBulkAddParticipants(participants);\n        setShowBulkImport(false);\n    };\n    // 處理添加現有參加者到當前屆別\n    const handleAddToSession = (participant, category)=>{\n        if (selectedSessionId) {\n            onAddSessionParticipant({\n                participantId: participant.id,\n                sessionId: selectedSessionId,\n                name: participant.name,\n                category: category,\n                joinDate: new Date().toISOString().split(\"T\")[0],\n                isActive: true\n            });\n        }\n    };\n    // 批量添加選中的參加者\n    const handleBatchAddToSession = ()=>{\n        if (selectedSessionId && selectedParticipants.size > 0) {\n            selectedParticipants.forEach((participantId)=>{\n                const participant = allParticipants.find((p)=>p.id === participantId);\n                if (participant) {\n                    onAddSessionParticipant({\n                        participantId: participant.id,\n                        sessionId: selectedSessionId,\n                        name: participant.name,\n                        category: participant.category || \"\",\n                        joinDate: new Date().toISOString().split(\"T\")[0],\n                        isActive: true\n                    });\n                }\n            });\n            setSelectedParticipants(new Set());\n            setShowAddExisting(false);\n        }\n    };\n    // 處理職銜編輯\n    const handleUpdateTitle = (oldTitle, newTitle)=>{\n        if (newTitle.trim() && newTitle !== oldTitle) {\n            try {\n                // 檢查新職銜是否已存在（不區分大小寫）\n                if (allCategories.some((category)=>category.toLowerCase() === newTitle.trim().toLowerCase() && category.toLowerCase() !== oldTitle.toLowerCase())) {\n                    alert(\"職銜「\".concat(newTitle.trim(), \"」已存在\"));\n                    setEditingTitle(\"\");\n                    return;\n                }\n                // 找出所有使用該職銜的參加者\n                const participantsToUpdate = allParticipants.filter((p)=>p.category === oldTitle && !p.name.startsWith('職銜佔位符-') // 排除佔位符參加者\n                );\n                // 找出佔位符參加者（如果有）\n                const placeholders = allParticipants.filter((p)=>p.category === oldTitle && p.name.startsWith('職銜佔位符-'));\n                // 找出所有使用該職銜的屆別參加者\n                const sessionParticipantsToUpdate = sessionParticipants.filter((sp)=>sp.category === oldTitle);\n                // 顯示進度提示\n                const totalUpdates = participantsToUpdate.length;\n                const hasPlaceholders = placeholders.length > 0;\n                // 確認是否繼續\n                if (totalUpdates > 0 || hasPlaceholders || sessionParticipantsToUpdate.length > 0) {\n                    if (!window.confirm(\"將更新職銜從「\".concat(oldTitle, \"」到「\").concat(newTitle, \"」，\").concat(totalUpdates > 0 ? \"影響 \".concat(totalUpdates, \" 位參加者\") : '', \"。\\n\\n確定繼續嗎？\"))) {\n                        setEditingTitle(\"\");\n                        return;\n                    }\n                    // 批量更新全局參加者\n                    if (participantsToUpdate.length > 0) {\n                        const batchUpdatePromises = participantsToUpdate.map((participant)=>{\n                            const updatedParticipant = {\n                                ...participant,\n                                category: newTitle\n                            };\n                            return new Promise((resolve)=>{\n                                onUpdateParticipant(updatedParticipant);\n                                resolve(null);\n                            });\n                        });\n                        Promise.all(batchUpdatePromises).catch((err)=>{\n                            console.error('批量更新參加者時發生錯誤:', err);\n                        });\n                    }\n                    // 批量更新佔位符參加者\n                    if (placeholders.length > 0) {\n                        const batchUpdatePlaceholderPromises = placeholders.map((placeholder)=>{\n                            const updatedPlaceholder = {\n                                ...placeholder,\n                                name: \"職銜佔位符-\".concat(newTitle),\n                                category: newTitle\n                            };\n                            return new Promise((resolve)=>{\n                                onUpdateParticipant(updatedPlaceholder);\n                                resolve(null);\n                            });\n                        });\n                        Promise.all(batchUpdatePlaceholderPromises).catch((err)=>{\n                            console.error('批量更新佔位符時發生錯誤:', err);\n                        });\n                    }\n                    // 批量更新屆別參加者\n                    if (sessionParticipantsToUpdate.length > 0) {\n                        const batchUpdateSessionPromises = sessionParticipantsToUpdate.map((sessionParticipant)=>{\n                            if ('category' in sessionParticipant) {\n                                const updatedSessionParticipant = {\n                                    ...sessionParticipant,\n                                    category: newTitle\n                                };\n                                return new Promise((resolve)=>{\n                                    onUpdateSessionParticipant(updatedSessionParticipant);\n                                    resolve(null);\n                                });\n                            }\n                            return Promise.resolve(null);\n                        });\n                        Promise.all(batchUpdateSessionPromises).catch((err)=>{\n                            console.error('批量更新屆別參加者時發生錯誤:', err);\n                        });\n                    }\n                    // 顯示完成提示\n                    if (totalUpdates > 0) {\n                        alert(\"成功更新 \".concat(totalUpdates, \" 位參加者的職銜從「\").concat(oldTitle, \"」到「\").concat(newTitle, \"」\"));\n                    } else if (hasPlaceholders) {\n                        alert(\"成功更新職銜「\".concat(oldTitle, \"」為「\").concat(newTitle, \"」\"));\n                    } else {\n                        alert(\"成功更新職銜「\".concat(oldTitle, \"」為「\").concat(newTitle, \"」，沒有參加者使用此職銜\"));\n                    }\n                } else {\n                    alert(\"沒有找到使用「\".concat(oldTitle, \"」職銜的參加者\"));\n                }\n            } catch (error) {\n                console.error('更新職銜時發生錯誤:', error);\n                alert(\"更新職銜時發生錯誤: \".concat(error instanceof Error ? error.message : '未知錯誤'));\n            }\n        }\n        setEditingTitle(\"\");\n    };\n    // 處理職銜刪除\n    const handleDeleteTitle = (title)=>{\n        try {\n            // 找出所有使用該職銜的實際參加者（排除佔位符）\n            const participantsToUpdate = allParticipants.filter((p)=>p.category === title && !p.name.startsWith('職銜佔位符-'));\n            // 找出使用該職銜的佔位符參加者\n            const placeholders = allParticipants.filter((p)=>p.category === title && p.name.startsWith('職銜佔位符-'));\n            // 找出所有使用該職銜的屆別參加者\n            const sessionParticipantsToUpdate = sessionParticipants.filter((sp)=>sp.category === title);\n            const totalUpdates = participantsToUpdate.length;\n            const hasPlaceholders = placeholders.length > 0;\n            let confirmMessage = '確定要刪除職銜 \"'.concat(title, '\" 嗎？');\n            if (totalUpdates > 0) {\n                confirmMessage += \"\\n\\n這將會：\";\n                confirmMessage += \"\\n• 清除 \".concat(totalUpdates, \" 位成員的職銜\");\n                confirmMessage += \"\\n• 將他們的職銜設為空白\";\n            }\n            if (hasPlaceholders) {\n                confirmMessage += totalUpdates > 0 ? \"\\n• 移除職銜佔位符\" : \"\\n\\n這將移除職銜佔位符。\";\n            }\n            if (totalUpdates === 0 && !hasPlaceholders) {\n                confirmMessage += \"\\n\\n沒有成員使用此職銜，將直接移除。\";\n            }\n            if (window.confirm(confirmMessage)) {\n                try {\n                    // 使用新的批量刪除函數\n                    const result = onBulkDeleteTitle(title);\n                    // 顯示完成提示\n                    let successMessage = '成功刪除職銜 \"'.concat(title, '\"');\n                    if (result.participantsUpdated > 0) {\n                        successMessage += \"\\n\\n已完成：\";\n                        successMessage += \"\\n• 清除了 \".concat(result.participantsUpdated, \" 位成員的職銜\");\n                        successMessage += \"\\n• 這些成員的職銜現在為空白\";\n                    }\n                    if (result.placeholdersRemoved > 0) {\n                        successMessage += result.participantsUpdated > 0 ? \"\\n• 移除了職銜佔位符\" : \"\\n\\n已移除職銜佔位符。\";\n                    }\n                    if (result.participantsUpdated === 0 && result.placeholdersRemoved === 0) {\n                        successMessage += \"\\n\\n沒有成員使用此職銜，已直接移除。\";\n                    }\n                    alert(successMessage);\n                } catch (error) {\n                    console.error('刪除職銜時發生錯誤:', error);\n                    alert(\"刪除職銜時發生錯誤: \".concat(error instanceof Error ? error.message : '未知錯誤'));\n                }\n            }\n        } catch (error) {\n            console.error('刪除職銜時發生錯誤:', error);\n            alert(\"刪除職銜時發生錯誤: \".concat(error instanceof Error ? error.message : '未知錯誤'));\n        }\n    };\n    // 獲取參加者的歷史屆別信息\n    const getParticipantHistory = (participantId)=>{\n        return sessionParticipants.filter((sp)=>sp.participantId === participantId).map((sp)=>{\n            var _sessions_find;\n            return {\n                ...sp,\n                sessionName: ((_sessions_find = sessions.find((s)=>s.id === sp.sessionId)) === null || _sessions_find === void 0 ? void 0 : _sessions_find.name) || \"未知屆別\"\n            };\n        }).sort((a, b)=>b.sessionId.localeCompare(a.sessionId));\n    };\n    const sortedAllParticipants = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ParticipantManagementPage.useMemo[sortedAllParticipants]\": ()=>{\n            if (!sortField) return allParticipants;\n            return [\n                ...allParticipants\n            ].sort({\n                \"ParticipantManagementPage.useMemo[sortedAllParticipants]\": (a, b)=>{\n                    let aValue = \"\";\n                    let bValue = \"\";\n                    switch(sortField){\n                        case \"name\":\n                            aValue = a.name.toLowerCase();\n                            bValue = b.name.toLowerCase();\n                            break;\n                        default:\n                            return 0;\n                    }\n                    if (aValue < bValue) return sortDirection === \"asc\" ? -1 : 1;\n                    if (aValue > bValue) return sortDirection === \"asc\" ? 1 : -1;\n                    return 0;\n                }\n            }[\"ParticipantManagementPage.useMemo[sortedAllParticipants]\"]);\n        }\n    }[\"ParticipantManagementPage.useMemo[sortedAllParticipants]\"], [\n        allParticipants,\n        sortField,\n        sortDirection\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onBack,\n                        className: \"px-3 py-1 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors text-sm\",\n                        children: \"← 返回\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 553,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-gray-900 dark:text-white\",\n                        children: \"參加者管理\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 559,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowTitleManagement(true),\n                                className: \"px-3 py-1 bg-purple-500 text-white rounded-md hover:bg-purple-600 transition-colors text-sm flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        className: \"h-4 w-4 mr-1\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        stroke: \"currentColor\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 572,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 578,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 565,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"職銜管理\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 561,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowBulkImport(true),\n                                className: \"px-3 py-1 bg-indigo-500 text-white rounded-md hover:bg-indigo-600 transition-colors text-sm flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        className: \"h-4 w-4 mr-1\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        stroke: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                            lineNumber: 593,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 586,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"批量導入\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 582,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 560,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                lineNumber: 552,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm text-blue-800 dark:text-blue-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: \"當前活躍設定：\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                            lineNumber: 608,\n                            columnNumber: 11\n                        }, this),\n                        \"非常活躍 ≥ \",\n                        activityLevelSettings.veryActive,\n                        \"%， 活躍 \",\n                        activityLevelSettings.active,\n                        \"%-\",\n                        activityLevelSettings.veryActive - 1,\n                        \"%， 不活躍 < \",\n                        activityLevelSettings.active,\n                        \"%\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                    lineNumber: 607,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                lineNumber: 606,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-700 rounded-lg shadow p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-md font-medium text-gray-900 dark:text-white mb-4\",\n                        children: \"參與度統計\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 616,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-medium text-blue-700 dark:text-blue-300\",\n                                        children: \"整體平均參與度\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 619,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-blue-600 dark:text-blue-400\",\n                                        children: [\n                                            statistics.participationStats.averageParticipationRate.toFixed(1),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 620,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-blue-600 dark:text-blue-400 mt-1\",\n                                        children: \"成員參與活動的平均比例\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 623,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 618,\n                                columnNumber: 11\n                            }, this),\n                            selectedSessionId && statistics.participationStats.sessionParticipationStats[selectedSessionId] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 dark:bg-green-900/20 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-medium text-green-700 dark:text-green-300\",\n                                        children: \"當前屆別參與度\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 630,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-green-600 dark:text-green-400\",\n                                        children: [\n                                            statistics.participationStats.sessionParticipationStats[selectedSessionId].averageParticipationRate.toFixed(1),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 631,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-green-600 dark:text-green-400 mt-1\",\n                                        children: statistics.participationStats.sessionParticipationStats[selectedSessionId].sessionName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 634,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 629,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-medium text-purple-700 dark:text-purple-300\",\n                                        children: \"活躍成員數\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 641,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-purple-600 dark:text-purple-400\",\n                                        children: selectedSessionId && statistics.participationStats.sessionParticipationStats[selectedSessionId] ? statistics.participationStats.sessionParticipationStats[selectedSessionId].activeParticipants : statistics.participantStats.filter((p)=>p.participationRate > 0).length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 642,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-purple-600 dark:text-purple-400 mt-1\",\n                                        children: \"參與過活動的成員\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 647,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 640,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-medium text-orange-700 dark:text-orange-300\",\n                                        children: \"高參與度成員\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 653,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-orange-600 dark:text-orange-400\",\n                                        children: statistics.participationStats.highParticipationStats.count\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 654,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-orange-600 dark:text-orange-400 mt-1\",\n                                        children: [\n                                            \"≥\",\n                                            statistics.participationStats.highParticipationStats.threshold,\n                                            \"% (\",\n                                            statistics.participationStats.highParticipationStats.percentage.toFixed(1),\n                                            \"%)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 657,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 652,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 617,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                lineNumber: 615,\n                columnNumber: 7\n            }, this),\n            showTitleManagement && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 dark:text-white\",\n                                        children: \"職銜管理\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 670,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowTitleManagement(false),\n                                        className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M6 18L18 6M6 6l12 12\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 676,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                            lineNumber: 675,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 671,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 669,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                        children: \"新增職銜\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 683,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: newTitle,\n                                                onChange: (e)=>setNewTitle(e.target.value),\n                                                placeholder: \"輸入新職銜名稱\",\n                                                className: \"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 685,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    if (newTitle.trim()) {\n                                                        try {\n                                                            // 檢查職銜是否已存在（不區分大小寫）\n                                                            if (allCategories.some((category)=>category.toLowerCase() === newTitle.trim().toLowerCase())) {\n                                                                alert(\"職銜「\".concat(newTitle.trim(), \"」已存在\"));\n                                                                return;\n                                                            }\n                                                            // 新增職銜 - 由於職銜是從參加者資料中提取的，\n                                                            // 我們創建一個使用這個職銜的佔位符參加者\n                                                            const newParticipantWithTitle = {\n                                                                id: \"temp-\".concat(Date.now()),\n                                                                name: \"職銜佔位符-\".concat(newTitle.trim()),\n                                                                category: newTitle.trim(),\n                                                                isActive: true\n                                                            };\n                                                            // 添加到全局參加者列表\n                                                            onAddParticipant(newParticipantWithTitle);\n                                                            alert(\"成功新增職銜「\".concat(newTitle.trim(), \"」\"));\n                                                            setNewTitle(\"\");\n                                                        } catch (error) {\n                                                            console.error('新增職銜時發生錯誤:', error);\n                                                            alert(\"新增職銜時發生錯誤: \".concat(error instanceof Error ? error.message : '未知錯誤'));\n                                                        }\n                                                    }\n                                                },\n                                                className: \"px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors\",\n                                                disabled: !newTitle.trim(),\n                                                children: \"新增\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 692,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 684,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 682,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-md font-medium text-gray-900 dark:text-white mb-3\",\n                                        children: \"現有職銜\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 734,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            allCategories.map((title)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-md\",\n                                                    children: editingTitle === title ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 flex space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                defaultValue: title,\n                                                                className: \"flex-1 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-600 dark:text-white\",\n                                                                onKeyDown: (e)=>{\n                                                                    if (e.key === \"Enter\") {\n                                                                        handleUpdateTitle(title, e.currentTarget.value);\n                                                                    } else if (e.key === \"Escape\") {\n                                                                        setEditingTitle(\"\");\n                                                                    }\n                                                                },\n                                                                autoFocus: true\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 743,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setEditingTitle(\"\"),\n                                                                className: \"px-2 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600\",\n                                                                children: \"取消\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 756,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 742,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-900 dark:text-white\",\n                                                                children: title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 765,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>setEditingTitle(title),\n                                                                        className: \"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm\",\n                                                                        children: \"編輯\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 767,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleDeleteTitle(title),\n                                                                        className: \"text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 text-sm\",\n                                                                        children: \"刪除\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 773,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 766,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true)\n                                                }, title, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 737,\n                                                    columnNumber: 21\n                                                }, this)),\n                                            allCategories.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-500 dark:text-gray-400 text-center py-4\",\n                                                children: \"暫無職銜\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 785,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 735,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 733,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 668,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                    lineNumber: 667,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                lineNumber: 666,\n                columnNumber: 9\n            }, this),\n            showBulkImport && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_universal_bulk_import__WEBPACK_IMPORTED_MODULE_3__.UniversalBulkImport, {\n                        dataType: \"participants\",\n                        sessions: sessions,\n                        selectedSessionId: selectedSessionId,\n                        onImport: handleBulkImport,\n                        onCancel: ()=>setShowBulkImport(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 798,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                    lineNumber: 797,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                lineNumber: 796,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-700 rounded-lg shadow p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-md font-medium text-gray-900 dark:text-white\",\n                                    children: \"當前屆別\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                    lineNumber: 813,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full text-sm font-medium\",\n                                    children: (currentSession === null || currentSession === void 0 ? void 0 : currentSession.name) || \"未選擇屆別\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                    lineNumber: 814,\n                                    columnNumber: 13\n                                }, this),\n                                currentSession && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                    children: [\n                                        currentSession.startDate,\n                                        \" - \",\n                                        currentSession.endDate\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                    lineNumber: 818,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                            lineNumber: 812,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                            children: [\n                                \"屆別參加者: \",\n                                currentSessionParticipants.length,\n                                \" 人\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                    lineNumber: 825,\n                                    columnNumber: 13\n                                }, this),\n                                \"所有成員: \",\n                                allParticipants.length,\n                                \" 人\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                            lineNumber: 823,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                    lineNumber: 811,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                lineNumber: 810,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-700 rounded-lg shadow p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setViewMode(\"session\"),\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-4 py-2 rounded-md transition-colors\", viewMode === \"session\" ? \"bg-blue-500 text-white\" : \"bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-500\"),\n                                    children: \"屆別參加者\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                    lineNumber: 835,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setViewMode(\"global\"),\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-4 py-2 rounded-md transition-colors\", viewMode === \"global\" ? \"bg-blue-500 text-white\" : \"bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-500\"),\n                                    children: \"所有成員\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                    lineNumber: 846,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                            lineNumber: 834,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                            children: viewMode === \"session\" ? \"顯示 \".concat(filteredSessionParticipants.length, \" / \").concat(currentSessionParticipants.length, \" 位屆別參加者\") : \"顯示 \".concat(allParticipants.length, \" 位所有成員\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                            lineNumber: 858,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                    lineNumber: 833,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                lineNumber: 832,\n                columnNumber: 7\n            }, this),\n            viewMode === \"session\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                        children: \"搜索參加者\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 871,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        placeholder: \"輸入參加者姓名\",\n                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 872,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 870,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                        children: \"職銜過濾\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 881,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: categoryFilter,\n                                        onChange: (e)=>setCategoryFilter(e.target.value),\n                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"全部職銜\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 887,\n                                                columnNumber: 17\n                                            }, this),\n                                            currentSessionCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: category,\n                                                    children: category\n                                                }, category, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 889,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 882,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 880,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                        children: \"狀態過濾\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 896,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: statusFilter,\n                                        onChange: (e)=>setStatusFilter(e.target.value),\n                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"all\",\n                                                children: \"全部狀態\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 902,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"active\",\n                                                children: \"活躍\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 903,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"inactive\",\n                                                children: \"非活躍\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 904,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 897,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 895,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 869,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-700 rounded-lg shadow overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-6 py-4 border-b border-gray-200 dark:border-gray-600\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 dark:text-white\",\n                                    children: [\n                                        currentSession === null || currentSession === void 0 ? void 0 : currentSession.name,\n                                        \" 參加者列表 (\",\n                                        filteredSessionParticipants.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                    lineNumber: 912,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 911,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"min-w-full divide-y divide-gray-200 dark:divide-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            className: \"bg-gray-50 dark:bg-gray-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleSort(\"name\"),\n                                                            className: \"flex items-center space-x-1 hover:bg-gray-100 dark:hover:bg-gray-700 px-2 py-1 rounded transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"姓名\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 925,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: sortField === \"name\" ? sortDirection === \"asc\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M5 15l7-7 7 7\",\n                                                                        className: \"text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 929,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M19 9l-7 7-7-7\",\n                                                                        className: \"text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 937,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M8 9l4-4 4 4m0 6l-4 4-4-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 946,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 926,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 921,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 920,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleSort(\"category\"),\n                                                            className: \"flex items-center space-x-1 hover:bg-gray-100 dark:hover:bg-gray-700 px-2 py-1 rounded transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"職銜\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 961,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: sortField === \"category\" ? sortDirection === \"asc\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M5 15l7-7 7 7\",\n                                                                        className: \"text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 965,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M19 9l-7 7-7-7\",\n                                                                        className: \"text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 973,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M8 9l4-4 4 4m0 6l-4 4-4-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 982,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 962,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 957,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 956,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleSort(\"joinDate\"),\n                                                            className: \"flex items-center space-x-1 hover:bg-gray-100 dark:hover:bg-gray-700 px-2 py-1 rounded transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"加入日期\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 997,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: sortField === \"joinDate\" ? sortDirection === \"asc\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M5 15l7-7 7 7\",\n                                                                        className: \"text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1001,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M19 9l-7 7-7-7\",\n                                                                        className: \"text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1009,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M8 9l4-4 4 4m0 6l-4 4-4-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1018,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 998,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 993,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 992,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleSort(\"attendanceRate\"),\n                                                            className: \"flex items-center space-x-1 hover:bg-gray-100 dark:hover:bg-gray-700 px-2 py-1 rounded transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"活躍狀態\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1033,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: sortField === \"attendanceRate\" ? sortDirection === \"asc\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M5 15l7-7 7 7\",\n                                                                        className: \"text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1037,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M19 9l-7 7-7-7\",\n                                                                        className: \"text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1045,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M8 9l4-4 4 4m0 6l-4 4-4-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1054,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1034,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1029,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1028,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                                                        children: \"參與度詳情\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1064,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                                                        children: \"歷史屆別\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1067,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                                                        children: \"操作\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1070,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 919,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                            lineNumber: 918,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            className: \"bg-white dark:bg-gray-700 divide-y divide-gray-200 dark:divide-gray-600\",\n                                            children: filteredSessionParticipants.length > 0 ? filteredSessionParticipants.map((sessionParticipant)=>{\n                                                var _sessionParticipant_attendanceRate;\n                                                const history = getParticipantHistory(sessionParticipant.participantId);\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"hover:bg-gray-50 dark:hover:bg-gray-650\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\",\n                                                            children: sessionParticipant.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1081,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded\",\n                                                                children: sessionParticipant.category || \"無職銜\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 1085,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1084,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\",\n                                                            children: sessionParticipant.joinDate || \"-\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1089,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-col space-y-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-2 py-1 text-xs rounded font-medium\", (0,_utils_activity_level__WEBPACK_IMPORTED_MODULE_5__.getActivityLevelColor)(sessionParticipant.activityLevel)),\n                                                                        children: (0,_utils_activity_level__WEBPACK_IMPORTED_MODULE_5__.getActivityLevelText)(sessionParticipant.activityLevel)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1094,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: [\n                                                                            \"出席率: \",\n                                                                            ((_sessionParticipant_attendanceRate = sessionParticipant.attendanceRate) === null || _sessionParticipant_attendanceRate === void 0 ? void 0 : _sessionParticipant_attendanceRate.toFixed(1)) || 0,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1102,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: [\n                                                                            \"(\",\n                                                                            sessionParticipant.totalAttended || 0,\n                                                                            \"/\",\n                                                                            sessionParticipant.totalParticipated || 0,\n                                                                            \")\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1105,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 1093,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1092,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 text-sm text-gray-900 dark:text-white\",\n                                                            children: (()=>{\n                                                                const participantStat = statistics.participantStats.find((p)=>p.id === sessionParticipant.participantId);\n                                                                if (!participantStat) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                    children: \"無資料\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1113,\n                                                                    columnNumber: 60\n                                                                }, this);\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-col space-y-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs font-medium text-gray-700 dark:text-gray-300\",\n                                                                                    children: [\n                                                                                        \"總體: \",\n                                                                                        participantStat.participationDetails.participationRatio\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                                    lineNumber: 1118,\n                                                                                    columnNumber: 37\n                                                                                }, this),\n                                                                                participantStat.isHighParticipation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"px-1 py-0.5 bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 text-xs rounded\",\n                                                                                    children: \"高參與\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                                    lineNumber: 1122,\n                                                                                    columnNumber: 39\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                            lineNumber: 1117,\n                                                                            columnNumber: 35\n                                                                        }, this),\n                                                                        selectedSessionId && participantStat.sessionParticipationDetails[selectedSessionId] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-blue-600 dark:text-blue-400\",\n                                                                            children: [\n                                                                                \"本屆: \",\n                                                                                participantStat.sessionParticipationDetails[selectedSessionId].participationRatio\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                            lineNumber: 1128,\n                                                                            columnNumber: 37\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                            children: [\n                                                                                \"參與率: \",\n                                                                                participantStat.participationRate.toFixed(1),\n                                                                                \"%\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                            lineNumber: 1132,\n                                                                            columnNumber: 35\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1116,\n                                                                    columnNumber: 33\n                                                                }, this);\n                                                            })()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1110,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 text-sm text-gray-900 dark:text-white\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-wrap gap-1\",\n                                                                children: [\n                                                                    history.slice(0, 3).map((h)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-1 py-0.5 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 text-xs rounded\",\n                                                                            title: \"\".concat(h.sessionName, \": \").concat(h.category),\n                                                                            children: h.sessionName\n                                                                        }, h.id, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                            lineNumber: 1142,\n                                                                            columnNumber: 33\n                                                                        }, this)),\n                                                                    history.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: [\n                                                                            \"+\",\n                                                                            history.length - 3\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1151,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 1140,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1139,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>{\n                                                                        const globalParticipant = allParticipants.find((p)=>p.id === sessionParticipant.participantId);\n                                                                        if (globalParticipant) {\n                                                                            setViewingAttendanceHistory(globalParticipant);\n                                                                        }\n                                                                    },\n                                                                    className: \"text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 mr-4\",\n                                                                    children: \"出席記錄\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1156,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>setEditingSessionParticipant(sessionParticipant),\n                                                                    className: \"text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 mr-4\",\n                                                                    children: \"編輯\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1169,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>{\n                                                                        if (window.confirm('確定要將 \"'.concat(sessionParticipant.name, '\" 從當前屆別中移除嗎？'))) {\n                                                                            onRemoveFromSession(sessionParticipant.id);\n                                                                        }\n                                                                    },\n                                                                    className: \"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300\",\n                                                                    children: \"移除\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1175,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1155,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, sessionParticipant.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 1080,\n                                                    columnNumber: 25\n                                                }, this);\n                                            }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    colSpan: 6,\n                                                    className: \"px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400\",\n                                                    children: \"沒有找到符合條件的參加者\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 1191,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1190,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                            lineNumber: 1075,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                    lineNumber: 917,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 916,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 910,\n                        columnNumber: 11\n                    }, this),\n                    availableGlobalParticipants.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-700 rounded-lg shadow p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 dark:text-white\",\n                                        children: [\n                                            \"添加現有參加者到 \",\n                                            currentSession === null || currentSession === void 0 ? void 0 : currentSession.name\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1205,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowAddExisting(!showAddExisting),\n                                        className: \"px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                className: \"h-4 w-4 mr-2\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M12 4v16m8-8H4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 1219,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1212,\n                                                columnNumber: 19\n                                            }, this),\n                                            showAddExisting ? \"收起\" : \"添加成員 (\".concat(availableGlobalParticipants.length, \")\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1208,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1204,\n                                columnNumber: 15\n                            }, this),\n                            showAddExisting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                                        children: \"搜索成員\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1230,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: addExistingSearchTerm,\n                                                        onChange: (e)=>{\n                                                            setAddExistingSearchTerm(e.target.value);\n                                                            setCurrentPage(1);\n                                                        },\n                                                        placeholder: \"輸入成員姓名\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1233,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1229,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                                        children: \"職銜篩選\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1245,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: addExistingCategoryFilter,\n                                                        onChange: (e)=>{\n                                                            setAddExistingCategoryFilter(e.target.value);\n                                                            setCurrentPage(1);\n                                                        },\n                                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"全部職銜\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 1256,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            allCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: category,\n                                                                    children: category\n                                                                }, category, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1258,\n                                                                    columnNumber: 27\n                                                                }, this))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1248,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1244,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1228,\n                                        columnNumber: 19\n                                    }, this),\n                                    selectedParticipants.size > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-blue-800 dark:text-blue-200\",\n                                                children: [\n                                                    \"已選擇 \",\n                                                    selectedParticipants.size,\n                                                    \" 位成員\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1269,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setSelectedParticipants(new Set()),\n                                                        className: \"px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600\",\n                                                        children: \"清除選擇\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1273,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleBatchAddToSession,\n                                                        className: \"px-3 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600\",\n                                                        children: \"批量添加\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1279,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1272,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1268,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3\",\n                                        children: filteredAvailableParticipants.participants.map((participant)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-3 border rounded-md cursor-pointer transition-colors\", selectedParticipants.has(participant.id) ? \"border-blue-500 bg-blue-50 dark:bg-blue-900/20\" : \"border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-650\"),\n                                                onClick: ()=>{\n                                                    const newSelected = new Set(selectedParticipants);\n                                                    if (newSelected.has(participant.id)) {\n                                                        newSelected.delete(participant.id);\n                                                    } else {\n                                                        newSelected.add(participant.id);\n                                                    }\n                                                    setSelectedParticipants(newSelected);\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-gray-900 dark:text-white\",\n                                                                    children: participant.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1312,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                participant.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                    children: [\n                                                                        \"職銜: \",\n                                                                        participant.category\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1314,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                (()=>{\n                                                                    const participantStat = statistics.participantStats.find((p)=>p.id === participant.id);\n                                                                    if (!participantStat) return null;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mt-1 space-y-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                                                                        children: [\n                                                                                            \"總體: \",\n                                                                                            participantStat.participationDetails.participationRatio\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                                        lineNumber: 1323,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    participantStat.isHighParticipation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"px-1 py-0.5 bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 text-xs rounded\",\n                                                                                        children: \"高參與\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                                        lineNumber: 1327,\n                                                                                        columnNumber: 39\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                                lineNumber: 1322,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            selectedSessionId && participantStat.sessionParticipationDetails[selectedSessionId] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs text-blue-600 dark:text-blue-400\",\n                                                                                children: [\n                                                                                    \"本屆: \",\n                                                                                    participantStat.sessionParticipationDetails[selectedSessionId].participationRatio\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                                lineNumber: 1333,\n                                                                                columnNumber: 37\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1321,\n                                                                        columnNumber: 33\n                                                                    }, this);\n                                                                })()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1311,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: selectedParticipants.has(participant.id),\n                                                            onChange: ()=>{},\n                                                            className: \"ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1341,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 1310,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, participant.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1292,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1290,\n                                        columnNumber: 19\n                                    }, this),\n                                    filteredAvailableParticipants.totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                children: [\n                                                    \"顯示 \",\n                                                    (currentPage - 1) * pageSize + 1,\n                                                    \" -\",\n                                                    \" \",\n                                                    Math.min(currentPage * pageSize, filteredAvailableParticipants.total),\n                                                    \" /\",\n                                                    \" \",\n                                                    filteredAvailableParticipants.total,\n                                                    \" 位成員\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1355,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setCurrentPage(Math.max(1, currentPage - 1)),\n                                                        disabled: currentPage === 1,\n                                                        className: \"px-3 py-1 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded text-sm hover:bg-gray-300 dark:hover:bg-gray-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                        children: \"上一頁\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1361,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-3 py-1 text-sm text-gray-700 dark:text-gray-300\",\n                                                        children: [\n                                                            currentPage,\n                                                            \" / \",\n                                                            filteredAvailableParticipants.totalPages\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1368,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setCurrentPage(Math.min(filteredAvailableParticipants.totalPages, currentPage + 1)),\n                                                        disabled: currentPage === filteredAvailableParticipants.totalPages,\n                                                        className: \"px-3 py-1 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded text-sm hover:bg-gray-300 dark:hover:bg-gray-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                        children: \"下一頁\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1371,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1360,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1354,\n                                        columnNumber: 21\n                                    }, this),\n                                    filteredAvailableParticipants.participants.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500 dark:text-gray-400\",\n                                        children: availableGlobalParticipants.length > 0 ? \"沒有找到符合篩選條件的成員\" : \"沒有可添加的成員\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1385,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1226,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 1203,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true) : /* 全局參加者視圖 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-700 rounded-lg shadow overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-4 border-b border-gray-200 dark:border-gray-600\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 dark:text-white\",\n                            children: [\n                                \"所有成員列表 (\",\n                                allParticipants.length,\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                            lineNumber: 1398,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 1397,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"min-w-full divide-y divide-gray-200 dark:divide-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    className: \"bg-gray-50 dark:bg-gray-800\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleSort(\"name\"),\n                                                    className: \"flex items-center space-x-1 hover:bg-gray-100 dark:hover:bg-gray-700 px-2 py-1 rounded transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"姓名\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1411,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: sortField === \"name\" ? sortDirection === \"asc\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M5 15l7-7 7 7\",\n                                                                className: \"text-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 1415,\n                                                                columnNumber: 29\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M19 9l-7 7-7-7\",\n                                                                className: \"text-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 1423,\n                                                                columnNumber: 29\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M8 9l4-4 4 4m0 6l-4 4-4-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 1432,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1412,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 1407,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1406,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                                                children: \"參與屆別\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1442,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                                                children: \"操作\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1445,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1405,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                    lineNumber: 1404,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    className: \"bg-white dark:bg-gray-700 divide-y divide-gray-200 dark:divide-gray-600\",\n                                    children: sortedAllParticipants.map((participant)=>{\n                                        const history = getParticipantHistory(participant.id);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"hover:bg-gray-50 dark:hover:bg-gray-650\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\",\n                                                    children: participant.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 1455,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 text-sm text-gray-900 dark:text-white\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-1\",\n                                                        children: [\n                                                            history.map((h)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded\",\n                                                                    title: \"\".concat(h.sessionName, \": \").concat(h.category),\n                                                                    children: h.sessionName\n                                                                }, h.id, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1461,\n                                                                    columnNumber: 29\n                                                                }, this)),\n                                                            history.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                children: \"未參與任何屆別\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 1470,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1459,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 1458,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setViewingAttendanceHistory(participant),\n                                                            className: \"text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 mr-4\",\n                                                            children: \"出席記錄\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1475,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setEditingParticipant(participant),\n                                                            className: \"text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 mr-4\",\n                                                            children: \"編輯\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1481,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                if (window.confirm('確定要刪除參加者 \"'.concat(participant.name, '\" 嗎？這將刪除所有相關數據。'))) {\n                                                                    onDeleteParticipant(participant.id);\n                                                                }\n                                                            },\n                                                            className: \"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300\",\n                                                            children: \"刪除\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1487,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 1474,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, participant.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                            lineNumber: 1454,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                    lineNumber: 1450,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                            lineNumber: 1403,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 1402,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                lineNumber: 1396,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-700 rounded-lg shadow p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 dark:text-white mb-4\",\n                        children: editingParticipant ? \"編輯成員\" : editingSessionParticipant ? \"編輯屆別參加者\" : \"新增參加者\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 1509,\n                        columnNumber: 9\n                    }, this),\n                    editingSessionParticipant ? /* 編輯屆別參加者表單 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                        children: \"姓名\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1517,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: editingSessionParticipant.name,\n                                        onChange: (e)=>setEditingSessionParticipant({\n                                                ...editingSessionParticipant,\n                                                name: e.target.value\n                                            }),\n                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1518,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1516,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                        children: \"職銜\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1526,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: editingSessionParticipant.category,\n                                        onChange: (e)=>setEditingSessionParticipant({\n                                                ...editingSessionParticipant,\n                                                category: e.target.value\n                                            }),\n                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"選擇職銜\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1534,\n                                                columnNumber: 17\n                                            }, this),\n                                            allCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: category,\n                                                    children: category\n                                                }, category, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 1536,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1527,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1525,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                        children: \"加入日期\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1543,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"date\",\n                                        value: editingSessionParticipant.joinDate || \"\",\n                                        onChange: (e)=>setEditingSessionParticipant({\n                                                ...editingSessionParticipant,\n                                                joinDate: e.target.value\n                                            }),\n                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1544,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1542,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: editingSessionParticipant.isActive,\n                                            onChange: (e)=>setEditingSessionParticipant({\n                                                    ...editingSessionParticipant,\n                                                    isActive: e.target.checked\n                                                }),\n                                            className: \"mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                            lineNumber: 1555,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                            children: \"在此屆別中活躍\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                            lineNumber: 1563,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                    lineNumber: 1554,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1553,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 1515,\n                        columnNumber: 11\n                    }, this) : /* 新增/編輯全局參加者表單 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                        children: \"姓名\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1571,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: editingParticipant ? editingParticipant.name : newParticipant.name,\n                                        onChange: (e)=>editingParticipant ? setEditingParticipant({\n                                                ...editingParticipant,\n                                                name: e.target.value\n                                            }) : setNewParticipant({\n                                                ...newParticipant,\n                                                name: e.target.value\n                                            }),\n                                        placeholder: \"輸入參加者姓名\",\n                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1572,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1570,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                        children: \"默認職銜\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1585,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: editingParticipant ? editingParticipant.category || \"\" : newParticipant.category,\n                                        onChange: (e)=>editingParticipant ? setEditingParticipant({\n                                                ...editingParticipant,\n                                                category: e.target.value\n                                            }) : setNewParticipant({\n                                                ...newParticipant,\n                                                category: e.target.value\n                                            }),\n                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"選擇職銜\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1595,\n                                                columnNumber: 17\n                                            }, this),\n                                            allCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: category,\n                                                    children: category\n                                                }, category, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 1597,\n                                                    columnNumber: 19\n                                                }, this)),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"新增職銜\",\n                                                children: \"+ 新增職銜\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1601,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1586,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1584,\n                                columnNumber: 13\n                            }, this),\n                            ((editingParticipant === null || editingParticipant === void 0 ? void 0 : editingParticipant.category) === \"新增職銜\" || newParticipant.category === \"新增職銜\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"md:col-span-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                        children: \"新職銜名稱\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1606,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"輸入新職銜名稱\",\n                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\",\n                                        onChange: (e)=>editingParticipant ? setEditingParticipant({\n                                                ...editingParticipant,\n                                                category: e.target.value\n                                            }) : setNewParticipant({\n                                                ...newParticipant,\n                                                category: e.target.value\n                                            })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1607,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1605,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 1569,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 flex justify-end space-x-3\",\n                        children: [\n                            (editingParticipant || editingSessionParticipant) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setEditingParticipant(null);\n                                    setEditingSessionParticipant(null);\n                                },\n                                className: \"px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors\",\n                                children: \"取消\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1624,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: editingSessionParticipant ? handleUpdateSessionParticipant : editingParticipant ? handleUpdateParticipant : handleAddParticipant,\n                                className: \"px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors\",\n                                disabled: editingSessionParticipant ? !editingSessionParticipant.name.trim() || !editingSessionParticipant.category : editingParticipant ? !editingParticipant.name.trim() : !newParticipant.name.trim(),\n                                children: editingSessionParticipant ? \"更新屆別參加者\" : editingParticipant ? \"更新成員\" : \"新增參加者\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1634,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 1622,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                lineNumber: 1508,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-700 rounded-lg shadow p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 dark:text-white mb-4\",\n                        children: \"統計信息\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 1658,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-blue-600 dark:text-blue-400\",\n                                        children: allParticipants.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1661,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                        children: \"所有成員總數\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1662,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1660,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-green-600 dark:text-green-400\",\n                                        children: currentSessionParticipants.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1665,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                        children: \"當前屆別參加者\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1666,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1664,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-purple-600 dark:text-purple-400\",\n                                        children: currentSessionParticipants.filter((sp)=>sp.isActive).length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1669,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                        children: \"活躍參加者\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1672,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1668,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-orange-600 dark:text-orange-400\",\n                                        children: currentSessionCategories.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1675,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                        children: \"當前屆別職銜數\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1676,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1674,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 1659,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                lineNumber: 1657,\n                columnNumber: 7\n            }, this),\n            viewingAttendanceHistory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_participant_attendance_history__WEBPACK_IMPORTED_MODULE_4__.ParticipantAttendanceHistory, {\n                participant: viewingAttendanceHistory,\n                activities: activities,\n                sessionParticipants: sessionParticipants,\n                sessions: sessions,\n                onClose: ()=>setViewingAttendanceHistory(null)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                lineNumber: 1683,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n        lineNumber: 551,\n        columnNumber: 5\n    }, this);\n}\n_s(ParticipantManagementPage, \"AVpChwcRS+JLhHGNJ2GcEeCSn00=\");\n_c = ParticipantManagementPage;\nvar _c;\n$RefreshReg$(_c, \"ParticipantManagementPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvcGFydGljaXBhbnQtbWFuYWdlbWVudC1wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUV5QztBQUNUO0FBRTZCO0FBQ2tCO0FBTS9DO0FBQ3lCO0FBb0JsRCxTQUFTVSwwQkFBMEIsS0FnQlQ7UUFoQlMsRUFDeENDLGVBQWUsRUFDZkMsbUJBQW1CLEVBQ25CQyxRQUFRLEVBQ1JDLGlCQUFpQixFQUNqQkMsZ0JBQWdCLEVBQ2hCQyx1QkFBdUIsRUFDdkJDLHFCQUFxQixFQUNyQkMsbUJBQW1CLEVBQ25CQywwQkFBMEIsRUFDMUJDLG1CQUFtQixFQUNuQkMsbUJBQW1CLEVBQ25CQyxpQkFBaUIsRUFDakJDLE1BQU0sRUFDTkMsVUFBVSxFQUNWQyx3QkFBd0JqQixrRkFBK0IsRUFDeEIsR0FoQlM7O0lBaUJ4QyxNQUFNLENBQUNrQixnQkFBZ0JDLGtCQUFrQixHQUFHM0IsK0NBQVFBLENBQUM7UUFDbkQ0QixNQUFNO1FBQ05DLFVBQVU7SUFDWjtJQUNBLE1BQU0sQ0FBQ0Msb0JBQW9CQyxzQkFBc0IsR0FBRy9CLCtDQUFRQSxDQUFxQjtJQUNqRixNQUFNLENBQUNnQywyQkFBMkJDLDZCQUE2QixHQUFHakMsK0NBQVFBLENBQTRCO0lBQ3RHLE1BQU0sQ0FBQ2tDLFlBQVlDLGNBQWMsR0FBR25DLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ29DLGdCQUFnQkMsa0JBQWtCLEdBQUdyQywrQ0FBUUEsQ0FBQztJQUNyRCxNQUFNLENBQUNzQyxjQUFjQyxnQkFBZ0IsR0FBR3ZDLCtDQUFRQSxDQUFnQztJQUNoRixNQUFNLENBQUN3QyxnQkFBZ0JDLGtCQUFrQixHQUFHekMsK0NBQVFBLENBQUM7SUFDckQsTUFBTSxDQUFDMEMsVUFBVUMsWUFBWSxHQUFHM0MsK0NBQVFBLENBQXVCO0lBQy9ELE1BQU0sQ0FBQzRDLDBCQUEwQkMsNEJBQTRCLEdBQUc3QywrQ0FBUUEsQ0FBcUI7SUFFN0YsV0FBVztJQUNYLE1BQU0sQ0FBQzhDLHFCQUFxQkMsdUJBQXVCLEdBQUcvQywrQ0FBUUEsQ0FBQztJQUMvRCxNQUFNLENBQUNnRCxjQUFjQyxnQkFBZ0IsR0FBR2pELCtDQUFRQSxDQUFTO0lBQ3pELE1BQU0sQ0FBQ2tELFVBQVVDLFlBQVksR0FBR25ELCtDQUFRQSxDQUFDO0lBRXpDLGdCQUFnQjtJQUNoQixNQUFNLENBQUNvRCxpQkFBaUJDLG1CQUFtQixHQUFHckQsK0NBQVFBLENBQUM7SUFDdkQsTUFBTSxDQUFDc0QsdUJBQXVCQyx5QkFBeUIsR0FBR3ZELCtDQUFRQSxDQUFDO0lBQ25FLE1BQU0sQ0FBQ3dELDJCQUEyQkMsNkJBQTZCLEdBQUd6RCwrQ0FBUUEsQ0FBQztJQUMzRSxNQUFNLENBQUMwRCxzQkFBc0JDLHdCQUF3QixHQUFHM0QsK0NBQVFBLENBQWMsSUFBSTREO0lBQ2xGLE1BQU0sQ0FBQ0MsYUFBYUMsZUFBZSxHQUFHOUQsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDK0QsU0FBUyxHQUFHL0QsK0NBQVFBLENBQUM7SUFFNUIsTUFBTSxDQUFDZ0UsV0FBV0MsYUFBYSxHQUFHakUsK0NBQVFBLENBQVM7SUFDbkQsTUFBTSxDQUFDa0UsZUFBZUMsaUJBQWlCLEdBQUduRSwrQ0FBUUEsQ0FBaUI7SUFFbkUsU0FBUztJQUNULE1BQU1vRSxpQkFBaUJ2RCxTQUFTd0QsSUFBSSxDQUFDLENBQUNDLElBQU1BLEVBQUVDLEVBQUUsS0FBS3pEO0lBRXJELGFBQWE7SUFDYixNQUFNMEQsNkJBQTZCdkUsOENBQU9BO3lFQUFDO1lBQ3pDLE9BQU9XLG9CQUFvQjZELE1BQU07aUZBQUMsQ0FBQ0MsS0FBT0EsR0FBR0MsU0FBUyxLQUFLN0Q7O1FBQzdEO3dFQUFHO1FBQUNGO1FBQXFCRTtLQUFrQjtJQUUzQyxjQUFjO0lBQ2QsTUFBTThELDJCQUEyQjNFLDhDQUFPQTt1RUFBQztZQUN2QyxNQUFNNEUsYUFBYSxJQUFJakIsSUFBSVksMkJBQTJCTSxHQUFHOytFQUFDLENBQUNKLEtBQU9BLEdBQUc3QyxRQUFROzhFQUFFNEMsTUFBTSxDQUFDTTtZQUN0RixPQUFPQyxNQUFNQyxJQUFJLENBQUNKO1FBQ3BCO3NFQUFHO1FBQUNMO0tBQTJCO0lBRS9CLFdBQVc7SUFDWCxNQUFNVSxnQkFBZ0JqRiw4Q0FBT0E7NERBQUM7WUFDNUIsTUFBTWtGLG1CQUFtQixJQUFJdkIsSUFBSWpELGdCQUFnQm1FLEdBQUc7b0VBQUMsQ0FBQ00sSUFBTUEsRUFBRXZELFFBQVE7bUVBQUU0QyxNQUFNLENBQUNNO1lBQy9FLE1BQU1NLG9CQUFvQixJQUFJekIsSUFBSWhELG9CQUFvQmtFLEdBQUc7b0VBQUMsQ0FBQ0osS0FBT0EsR0FBRzdDLFFBQVE7bUVBQUU0QyxNQUFNLENBQUNNO1lBQ3RGLE9BQU9DLE1BQU1DLElBQUksQ0FBQyxJQUFJckIsSUFBSTttQkFBSXVCO21CQUFxQkU7YUFBa0I7UUFDdkU7MkRBQUc7UUFBQzFFO1FBQWlCQztLQUFvQjtJQUV6QyxTQUFTO0lBQ1QsTUFBTTBFLGFBQWFyRiw4Q0FBT0E7eURBQUM7WUFDekIsT0FBT1Esc0VBQW1CQSxDQUFDZSxZQUFZYixpQkFBaUJFO1FBQzFEO3dEQUFHO1FBQUNXO1FBQVliO1FBQWlCRTtLQUFTO0lBRTFDLE1BQU0wRSxhQUFhLENBQUNDO1FBQ2xCLElBQUl4QixjQUFjd0IsT0FBTztZQUN2QnJCLGlCQUFpQkQsa0JBQWtCLFFBQVEsU0FBUztRQUN0RCxPQUFPO1lBQ0xELGFBQWF1QjtZQUNickIsaUJBQWlCO1FBQ25CO0lBQ0Y7SUFFQSxRQUFRO0lBQ1IsTUFBTXNCLDhCQUE4QnhGLDhDQUFPQTswRUFBQztZQUMxQyxNQUFNeUYsV0FBV2xCLDJCQUEyQkMsTUFBTTsyRkFBQyxDQUFDQztvQkFDbEQsTUFBTWlCLGdCQUFnQmpCLEdBQUc5QyxJQUFJLENBQUNnRSxXQUFXLEdBQUdDLFFBQVEsQ0FBQzNELFdBQVcwRCxXQUFXO29CQUMzRSxNQUFNRSxrQkFBa0IxRCxpQkFBaUJzQyxHQUFHN0MsUUFBUSxLQUFLTyxpQkFBaUI7b0JBQzFFLE1BQU0yRCxnQkFDSnpELGlCQUFpQixTQUNoQkEsaUJBQWlCLFlBQVlvQyxHQUFHc0IsUUFBUSxJQUN4QzFELGlCQUFpQixjQUFjLENBQUNvQyxHQUFHc0IsUUFBUTtvQkFDOUMsT0FBT0wsaUJBQWlCRyxtQkFBbUJDO2dCQUM3Qzs7WUFFQSxtQkFBbUI7WUFDbkIsTUFBTUUsZ0NBQWdDUCxTQUFTWixHQUFHO2dIQUFDLENBQUNKO29CQUNsRCxtQkFBbUI7b0JBQ25CLE1BQU13Qix3QkFBd0IxRSxXQUFXaUQsTUFBTTs4SUFBQyxDQUFDMEIsV0FBYUEsU0FBU3hCLFNBQVMsS0FBSzdEOztvQkFFckYscUJBQXFCO29CQUNyQixJQUFJc0Ysb0JBQW9CO29CQUN4QixJQUFJQyxnQkFBZ0I7b0JBRXBCSCxzQkFBc0JJLE9BQU87d0hBQUMsQ0FBQ0g7NEJBQzdCLGtCQUFrQjs0QkFDbEIsTUFBTUksd0JBQXdCSixTQUFTSyxZQUFZLENBQUNuQyxJQUFJO3NKQUFDLENBQUNlLElBQU1BLEVBQUViLEVBQUUsS0FBS0csR0FBRytCLGFBQWE7OzRCQUN6RixJQUFJRix1QkFBdUI7Z0NBQ3pCSDtnQ0FFQSxvQkFBb0I7Z0NBQ3BCLE1BQU1NLG1CQUFtQkgsc0JBQXNCSSxVQUFVO2dDQUN6RCxJQUFJQyxhQUFhO2dDQUVqQixXQUFXO2dDQUNYLElBQUlGLGdCQUFnQixDQUFDUCxTQUFTNUIsRUFBRSxDQUFDLEtBQUssTUFBTTtvQ0FDMUNxQyxhQUFhO2dDQUNmLE9BQU8sSUFBSUYsZ0JBQWdCLENBQUNQLFNBQVNVLElBQUksQ0FBQyxLQUFLLE1BQU07b0NBQ25ERCxhQUFhO2dDQUNmLE9BQU8sSUFBSUYsZ0JBQWdCLENBQUMsR0FBaUIsT0FBZFAsU0FBU1UsSUFBSSxFQUFHLEtBQUssTUFBTTtvQ0FDeERELGFBQWE7Z0NBQ2Y7Z0NBRUEsSUFBSUEsWUFBWTtvQ0FDZFA7Z0NBQ0Y7NEJBQ0Y7d0JBQ0Y7O29CQUVBLE1BQU1TLGlCQUFpQlYsb0JBQW9CLElBQUksZ0JBQWlCQSxvQkFBcUIsTUFBTTtvQkFDM0YsTUFBTVcsZ0JBQWdCMUcsdUVBQWdCQSxDQUFDeUcsZ0JBQWdCckY7b0JBRXZELE9BQU87d0JBQ0wsR0FBR2lELEVBQUU7d0JBQ0xvQzt3QkFDQUM7d0JBQ0FYO3dCQUNBQztvQkFDRjtnQkFDRjs7WUFFQSxPQUFPO1lBQ1AsSUFBSXJDLFdBQVc7Z0JBQ2JpQyw4QkFBOEJlLElBQUk7c0ZBQUMsQ0FBQ0MsR0FBR0M7d0JBQ3JDLElBQUlDLFNBQWM7d0JBQ2xCLElBQUlDLFNBQWM7d0JBRWxCLE9BQVFwRDs0QkFDTixLQUFLO2dDQUNIbUQsU0FBU0YsRUFBRXJGLElBQUksQ0FBQ2dFLFdBQVc7Z0NBQzNCd0IsU0FBU0YsRUFBRXRGLElBQUksQ0FBQ2dFLFdBQVc7Z0NBQzNCOzRCQUNGLEtBQUs7Z0NBQ0h1QixTQUFTRixFQUFFcEYsUUFBUSxDQUFDK0QsV0FBVztnQ0FDL0J3QixTQUFTRixFQUFFckYsUUFBUSxDQUFDK0QsV0FBVztnQ0FDL0I7NEJBQ0YsS0FBSztnQ0FDSHVCLFNBQVNGLEVBQUVJLFFBQVEsSUFBSTtnQ0FDdkJELFNBQVNGLEVBQUVHLFFBQVEsSUFBSTtnQ0FDdkI7NEJBQ0YsS0FBSztnQ0FDSEYsU0FBU0YsRUFBRWpCLFFBQVEsR0FBRyxXQUFXO2dDQUNqQ29CLFNBQVNGLEVBQUVsQixRQUFRLEdBQUcsV0FBVztnQ0FDakM7NEJBQ0YsS0FBSztnQ0FDSG1CLFNBQVNGLEVBQUVILGNBQWM7Z0NBQ3pCTSxTQUFTRixFQUFFSixjQUFjO2dDQUN6Qjs0QkFDRjtnQ0FDRSxPQUFPO3dCQUNYO3dCQUVBLElBQUlLLFNBQVNDLFFBQVEsT0FBT2xELGtCQUFrQixRQUFRLENBQUMsSUFBSTt3QkFDM0QsSUFBSWlELFNBQVNDLFFBQVEsT0FBT2xELGtCQUFrQixRQUFRLElBQUksQ0FBQzt3QkFDM0QsT0FBTztvQkFDVDs7WUFDRjtZQUVBLE9BQU8rQjtRQUNUO3lFQUFHO1FBQ0R6QjtRQUNBdEM7UUFDQUU7UUFDQUU7UUFDQTBCO1FBQ0FFO1FBQ0ExQztRQUNBVjtRQUNBVztLQUNEO0lBRUQsc0JBQXNCO0lBQ3RCLE1BQU02Riw4QkFBOEJySCw4Q0FBT0E7MEVBQUM7WUFDMUMsTUFBTXNILHdCQUF3QixJQUFJM0QsSUFBSVksMkJBQTJCTSxHQUFHO2tGQUFDLENBQUNKLEtBQU9BLEdBQUcrQixhQUFhOztZQUM3RixPQUFPOUYsZ0JBQWdCOEQsTUFBTTtrRkFBQyxDQUFDVyxJQUFNLENBQUNtQyxzQkFBc0JDLEdBQUcsQ0FBQ3BDLEVBQUViLEVBQUU7O1FBQ3RFO3lFQUFHO1FBQUM1RDtRQUFpQjZEO0tBQTJCO0lBRWhELGNBQWM7SUFDZCxNQUFNaUQsZ0NBQWdDeEgsOENBQU9BOzRFQUFDO1lBQzVDLE1BQU15RixXQUFXNEIsNEJBQTRCN0MsTUFBTTs2RkFBQyxDQUFDaUQ7b0JBQ25ELE1BQU0vQixnQkFBZ0IrQixZQUFZOUYsSUFBSSxDQUFDZ0UsV0FBVyxHQUFHQyxRQUFRLENBQUN2QyxzQkFBc0JzQyxXQUFXO29CQUMvRixNQUFNRSxrQkFBa0J0Qyw0QkFBNEJrRSxZQUFZN0YsUUFBUSxLQUFLMkIsNEJBQTRCO29CQUN6RyxPQUFPbUMsaUJBQWlCRztnQkFDMUI7O1lBRUEsTUFBTTZCLGFBQWEsQ0FBQzlELGNBQWMsS0FBS0U7WUFDdkMsTUFBTTZELFdBQVdELGFBQWE1RDtZQUM5QixPQUFPO2dCQUNMeUMsY0FBY2QsU0FBU21DLEtBQUssQ0FBQ0YsWUFBWUM7Z0JBQ3pDRSxPQUFPcEMsU0FBU3FDLE1BQU07Z0JBQ3RCQyxZQUFZQyxLQUFLQyxJQUFJLENBQUN4QyxTQUFTcUMsTUFBTSxHQUFHaEU7WUFDMUM7UUFDRjsyRUFBRztRQUFDdUQ7UUFBNkJoRTtRQUF1QkU7UUFBMkJLO1FBQWFFO0tBQVM7SUFFekcsVUFBVTtJQUNWLE1BQU1vRSx1QkFBdUI7UUFDM0IsSUFBSXpHLGVBQWVFLElBQUksQ0FBQ3dHLElBQUksSUFBSTtZQUM5QixJQUFJO2dCQUNGLFlBQVk7Z0JBQ1osSUFBSUMsZ0JBQWdCM0csZUFBZUcsUUFBUTtnQkFDM0MsSUFBSXdHLGtCQUFrQixRQUFRO29CQUM1QkEsZ0JBQWdCLEdBQUcseUJBQXlCOztnQkFDOUM7Z0JBRUEsTUFBTUMsbUJBQW1CO29CQUN2QjFHLE1BQU1GLGVBQWVFLElBQUksQ0FBQ3dHLElBQUk7b0JBQzlCdkcsVUFBVXdHO2dCQUNaO2dCQUVBRSxRQUFRQyxHQUFHLENBQUMsdUJBQXVCRixrQkFBa0IsT0FBTzs7Z0JBQzVEdkgsaUJBQWlCdUg7Z0JBQ2pCM0csa0JBQWtCO29CQUFFQyxNQUFNO29CQUFJQyxVQUFVO2dCQUFHO2dCQUUzQyxTQUFTO2dCQUNUNEcsTUFBTSxXQUFpQyxPQUF0QkgsaUJBQWlCMUcsSUFBSSxFQUFDO1lBQ3pDLEVBQUUsT0FBTzhHLE9BQU87Z0JBQ2RILFFBQVFHLEtBQUssQ0FBQyxlQUFlQTtnQkFDN0JELE1BQU0sZUFBK0QsT0FBaERDLGlCQUFpQkMsUUFBUUQsTUFBTUUsT0FBTyxHQUFHO1lBQ2hFO1FBQ0Y7SUFDRjtJQUVBLFVBQVU7SUFDVixNQUFNQywwQkFBMEI7UUFDOUIsSUFBSS9HLG9CQUFvQjtZQUN0Qlosb0JBQW9CWTtZQUNwQkMsc0JBQXNCO1FBQ3hCO0lBQ0Y7SUFFQSxZQUFZO0lBQ1osTUFBTStHLGlDQUFpQztRQUNyQyxJQUFJOUcsMkJBQTJCO1lBQzdCYiwyQkFBMkJhO1lBQzNCQyw2QkFBNkI7UUFDL0I7SUFDRjtJQUVBLFNBQVM7SUFDVCxNQUFNOEcsbUJBQW1CLENBQUN2QztRQUN4QnZGLHNCQUFzQnVGO1FBQ3RCL0Qsa0JBQWtCO0lBQ3BCO0lBRUEsaUJBQWlCO0lBQ2pCLE1BQU11RyxxQkFBcUIsQ0FBQ3RCLGFBQTBCN0Y7UUFDcEQsSUFBSWYsbUJBQW1CO1lBQ3JCRSx3QkFBd0I7Z0JBQ3RCeUYsZUFBZWlCLFlBQVluRCxFQUFFO2dCQUM3QkksV0FBVzdEO2dCQUNYYyxNQUFNOEYsWUFBWTlGLElBQUk7Z0JBQ3RCQyxVQUFVQTtnQkFDVndGLFVBQVUsSUFBSTRCLE9BQU9DLFdBQVcsR0FBR0MsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFO2dCQUNoRG5ELFVBQVU7WUFDWjtRQUNGO0lBQ0Y7SUFFQSxhQUFhO0lBQ2IsTUFBTW9ELDBCQUEwQjtRQUM5QixJQUFJdEkscUJBQXFCNEMscUJBQXFCMkYsSUFBSSxHQUFHLEdBQUc7WUFDdEQzRixxQkFBcUI0QyxPQUFPLENBQUMsQ0FBQ0c7Z0JBQzVCLE1BQU1pQixjQUFjL0csZ0JBQWdCMEQsSUFBSSxDQUFDLENBQUNlLElBQU1BLEVBQUViLEVBQUUsS0FBS2tDO2dCQUN6RCxJQUFJaUIsYUFBYTtvQkFDZjFHLHdCQUF3Qjt3QkFDdEJ5RixlQUFlaUIsWUFBWW5ELEVBQUU7d0JBQzdCSSxXQUFXN0Q7d0JBQ1hjLE1BQU04RixZQUFZOUYsSUFBSTt3QkFDdEJDLFVBQVU2RixZQUFZN0YsUUFBUSxJQUFJO3dCQUNsQ3dGLFVBQVUsSUFBSTRCLE9BQU9DLFdBQVcsR0FBR0MsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFO3dCQUNoRG5ELFVBQVU7b0JBQ1o7Z0JBQ0Y7WUFDRjtZQUNBckMsd0JBQXdCLElBQUlDO1lBQzVCUCxtQkFBbUI7UUFDckI7SUFDRjtJQUVBLFNBQVM7SUFDVCxNQUFNaUcsb0JBQW9CLENBQUNDLFVBQWtCckc7UUFDM0MsSUFBSUEsU0FBU2tGLElBQUksTUFBTWxGLGFBQWFxRyxVQUFVO1lBQzVDLElBQUk7Z0JBQ0YscUJBQXFCO2dCQUNyQixJQUFJckUsY0FBY3NFLElBQUksQ0FBQzNILENBQUFBLFdBQ3JCQSxTQUFTK0QsV0FBVyxPQUFPMUMsU0FBU2tGLElBQUksR0FBR3hDLFdBQVcsTUFDdEQvRCxTQUFTK0QsV0FBVyxPQUFPMkQsU0FBUzNELFdBQVcsS0FDOUM7b0JBQ0Q2QyxNQUFNLE1BQXNCLE9BQWhCdkYsU0FBU2tGLElBQUksSUFBRztvQkFDNUJuRixnQkFBZ0I7b0JBQ2hCO2dCQUNGO2dCQUVBLGdCQUFnQjtnQkFDaEIsTUFBTXdHLHVCQUF1QjlJLGdCQUFnQjhELE1BQU0sQ0FBQ1csQ0FBQUEsSUFDbERBLEVBQUV2RCxRQUFRLEtBQUswSCxZQUNmLENBQUNuRSxFQUFFeEQsSUFBSSxDQUFDOEgsVUFBVSxDQUFDLFVBQVUsV0FBVzs7Z0JBRzFDLGdCQUFnQjtnQkFDaEIsTUFBTUMsZUFBZWhKLGdCQUFnQjhELE1BQU0sQ0FBQ1csQ0FBQUEsSUFDMUNBLEVBQUV2RCxRQUFRLEtBQUswSCxZQUNmbkUsRUFBRXhELElBQUksQ0FBQzhILFVBQVUsQ0FBQztnQkFHcEIsa0JBQWtCO2dCQUNsQixNQUFNRSw4QkFBOEJoSixvQkFBb0I2RCxNQUFNLENBQUNDLENBQUFBLEtBQzdEQSxHQUFHN0MsUUFBUSxLQUFLMEg7Z0JBR2xCLFNBQVM7Z0JBQ1QsTUFBTU0sZUFBZUoscUJBQXFCMUIsTUFBTTtnQkFDaEQsTUFBTStCLGtCQUFrQkgsYUFBYTVCLE1BQU0sR0FBRztnQkFFOUMsU0FBUztnQkFDVCxJQUFJOEIsZUFBZSxLQUFLQyxtQkFBbUJGLDRCQUE0QjdCLE1BQU0sR0FBRyxHQUFHO29CQUNqRixJQUFJLENBQUNnQyxPQUFPQyxPQUFPLENBQUMsVUFBd0I5RyxPQUFkcUcsVUFBUyxPQUFrQk0sT0FBYjNHLFVBQVMsTUFBc0QsT0FBbEQyRyxlQUFlLElBQUksTUFBbUIsT0FBYkEsY0FBYSxXQUFTLElBQUcsaUJBQWU7d0JBQ3hINUcsZ0JBQWdCO3dCQUNoQjtvQkFDRjtvQkFFQSxZQUFZO29CQUNaLElBQUl3RyxxQkFBcUIxQixNQUFNLEdBQUcsR0FBRzt3QkFDbkMsTUFBTWtDLHNCQUFzQlIscUJBQXFCM0UsR0FBRyxDQUFDNEMsQ0FBQUE7NEJBQ25ELE1BQU13QyxxQkFBcUI7Z0NBQUUsR0FBR3hDLFdBQVc7Z0NBQUU3RixVQUFVcUI7NEJBQVM7NEJBQ2hFLE9BQU8sSUFBSWlILFFBQVFDLENBQUFBO2dDQUNqQmxKLG9CQUFvQmdKO2dDQUNwQkUsUUFBUTs0QkFDVjt3QkFDRjt3QkFFQUQsUUFBUUUsR0FBRyxDQUFDSixxQkFBcUJLLEtBQUssQ0FBQ0MsQ0FBQUE7NEJBQ3JDaEMsUUFBUUcsS0FBSyxDQUFDLGlCQUFpQjZCO3dCQUNqQztvQkFDRjtvQkFFQSxhQUFhO29CQUNiLElBQUlaLGFBQWE1QixNQUFNLEdBQUcsR0FBRzt3QkFDM0IsTUFBTXlDLGlDQUFpQ2IsYUFBYTdFLEdBQUcsQ0FBQzJGLENBQUFBOzRCQUN0RCxNQUFNQyxxQkFBcUI7Z0NBQ3pCLEdBQUdELFdBQVc7Z0NBQ2Q3SSxNQUFNLFNBQWtCLE9BQVRzQjtnQ0FDZnJCLFVBQVVxQjs0QkFDWjs0QkFDQSxPQUFPLElBQUlpSCxRQUFRQyxDQUFBQTtnQ0FDakJsSixvQkFBb0J3SjtnQ0FDcEJOLFFBQVE7NEJBQ1Y7d0JBQ0Y7d0JBRUFELFFBQVFFLEdBQUcsQ0FBQ0csZ0NBQWdDRixLQUFLLENBQUNDLENBQUFBOzRCQUNoRGhDLFFBQVFHLEtBQUssQ0FBQyxpQkFBaUI2Qjt3QkFDakM7b0JBQ0Y7b0JBRUEsWUFBWTtvQkFDWixJQUFJWCw0QkFBNEI3QixNQUFNLEdBQUcsR0FBRzt3QkFDMUMsTUFBTTRDLDZCQUE2QmYsNEJBQTRCOUUsR0FBRyxDQUFDOEYsQ0FBQUE7NEJBQ2pFLElBQUksY0FBY0Esb0JBQW9CO2dDQUNwQyxNQUFNQyw0QkFBNEI7b0NBQUUsR0FBR0Qsa0JBQWtCO29DQUFFL0ksVUFBVXFCO2dDQUFTO2dDQUM5RSxPQUFPLElBQUlpSCxRQUFRQyxDQUFBQTtvQ0FDakJqSiwyQkFBMkIwSjtvQ0FDM0JULFFBQVE7Z0NBQ1Y7NEJBQ0Y7NEJBQ0EsT0FBT0QsUUFBUUMsT0FBTyxDQUFDO3dCQUN6Qjt3QkFFQUQsUUFBUUUsR0FBRyxDQUFDTSw0QkFBNEJMLEtBQUssQ0FBQ0MsQ0FBQUE7NEJBQzVDaEMsUUFBUUcsS0FBSyxDQUFDLG1CQUFtQjZCO3dCQUNuQztvQkFDRjtvQkFFQSxTQUFTO29CQUNULElBQUlWLGVBQWUsR0FBRzt3QkFDcEJwQixNQUFNLFFBQWlDYyxPQUF6Qk0sY0FBYSxjQUEwQjNHLE9BQWRxRyxVQUFTLE9BQWMsT0FBVHJHLFVBQVM7b0JBQ2hFLE9BQU8sSUFBSTRHLGlCQUFpQjt3QkFDMUJyQixNQUFNLFVBQXdCdkYsT0FBZHFHLFVBQVMsT0FBYyxPQUFUckcsVUFBUztvQkFDekMsT0FBTzt3QkFDTHVGLE1BQU0sVUFBd0J2RixPQUFkcUcsVUFBUyxPQUFjLE9BQVRyRyxVQUFTO29CQUN6QztnQkFDRixPQUFPO29CQUNMdUYsTUFBTSxVQUFtQixPQUFUYyxVQUFTO2dCQUMzQjtZQUNGLEVBQUUsT0FBT2IsT0FBTztnQkFDZEgsUUFBUUcsS0FBSyxDQUFDLGNBQWNBO2dCQUM1QkQsTUFBTSxjQUE4RCxPQUFoREMsaUJBQWlCQyxRQUFRRCxNQUFNRSxPQUFPLEdBQUc7WUFDL0Q7UUFDRjtRQUNBM0YsZ0JBQWdCO0lBQ2xCO0lBRUEsU0FBUztJQUNULE1BQU02SCxvQkFBb0IsQ0FBQ0M7UUFDekIsSUFBSTtZQUNGLHlCQUF5QjtZQUN6QixNQUFNdEIsdUJBQXVCOUksZ0JBQWdCOEQsTUFBTSxDQUFDVyxDQUFBQSxJQUNsREEsRUFBRXZELFFBQVEsS0FBS2tKLFNBQ2YsQ0FBQzNGLEVBQUV4RCxJQUFJLENBQUM4SCxVQUFVLENBQUM7WUFHckIsaUJBQWlCO1lBQ2pCLE1BQU1DLGVBQWVoSixnQkFBZ0I4RCxNQUFNLENBQUNXLENBQUFBLElBQzFDQSxFQUFFdkQsUUFBUSxLQUFLa0osU0FDZjNGLEVBQUV4RCxJQUFJLENBQUM4SCxVQUFVLENBQUM7WUFHcEIsa0JBQWtCO1lBQ2xCLE1BQU1FLDhCQUE4QmhKLG9CQUFvQjZELE1BQU0sQ0FBQ0MsQ0FBQUEsS0FDN0RBLEdBQUc3QyxRQUFRLEtBQUtrSjtZQUdsQixNQUFNbEIsZUFBZUoscUJBQXFCMUIsTUFBTTtZQUNoRCxNQUFNK0Isa0JBQWtCSCxhQUFhNUIsTUFBTSxHQUFHO1lBRTlDLElBQUlpRCxpQkFBaUIsWUFBa0IsT0FBTkQsT0FBTTtZQUV2QyxJQUFJbEIsZUFBZSxHQUFHO2dCQUNwQm1CLGtCQUFtQjtnQkFDbkJBLGtCQUFrQixVQUF1QixPQUFibkIsY0FBYTtnQkFDekNtQixrQkFBbUI7WUFDckI7WUFFQSxJQUFJbEIsaUJBQWlCO2dCQUNuQmtCLGtCQUFrQm5CLGVBQWUsSUFBSyxnQkFBZ0I7WUFDeEQ7WUFFQSxJQUFJQSxpQkFBaUIsS0FBSyxDQUFDQyxpQkFBaUI7Z0JBQzFDa0Isa0JBQW1CO1lBQ3JCO1lBRUEsSUFBSWpCLE9BQU9DLE9BQU8sQ0FBQ2dCLGlCQUFpQjtnQkFDbEMsSUFBSTtvQkFDRixhQUFhO29CQUNiLE1BQU1DLFNBQVMzSixrQkFBa0J5SjtvQkFFakMsU0FBUztvQkFDVCxJQUFJRyxpQkFBaUIsV0FBaUIsT0FBTkgsT0FBTTtvQkFFdEMsSUFBSUUsT0FBT0UsbUJBQW1CLEdBQUcsR0FBRzt3QkFDbENELGtCQUFtQjt3QkFDbkJBLGtCQUFrQixXQUFzQyxPQUEzQkQsT0FBT0UsbUJBQW1CLEVBQUM7d0JBQ3hERCxrQkFBbUI7b0JBQ3JCO29CQUVBLElBQUlELE9BQU9HLG1CQUFtQixHQUFHLEdBQUc7d0JBQ2xDRixrQkFBa0JELE9BQU9FLG1CQUFtQixHQUFHLElBQUssaUJBQWlCO29CQUN2RTtvQkFFQSxJQUFJRixPQUFPRSxtQkFBbUIsS0FBSyxLQUFLRixPQUFPRyxtQkFBbUIsS0FBSyxHQUFHO3dCQUN4RUYsa0JBQW1CO29CQUNyQjtvQkFFQXpDLE1BQU15QztnQkFDUixFQUFFLE9BQU94QyxPQUFPO29CQUNkSCxRQUFRRyxLQUFLLENBQUMsY0FBY0E7b0JBQzVCRCxNQUFNLGNBQThELE9BQWhEQyxpQkFBaUJDLFFBQVFELE1BQU1FLE9BQU8sR0FBRztnQkFDL0Q7WUFDRjtRQUNGLEVBQUUsT0FBT0YsT0FBTztZQUNkSCxRQUFRRyxLQUFLLENBQUMsY0FBY0E7WUFDNUJELE1BQU0sY0FBOEQsT0FBaERDLGlCQUFpQkMsUUFBUUQsTUFBTUUsT0FBTyxHQUFHO1FBQy9EO0lBQ0Y7SUFFQSxlQUFlO0lBQ2YsTUFBTXlDLHdCQUF3QixDQUFDNUU7UUFDN0IsT0FBTzdGLG9CQUNKNkQsTUFBTSxDQUFDLENBQUNDLEtBQU9BLEdBQUcrQixhQUFhLEtBQUtBLGVBQ3BDM0IsR0FBRyxDQUFDLENBQUNKO2dCQUVTN0Q7bUJBRkQ7Z0JBQ1osR0FBRzZELEVBQUU7Z0JBQ0w0RyxhQUFhekssRUFBQUEsaUJBQUFBLFNBQVN3RCxJQUFJLENBQUMsQ0FBQ0MsSUFBTUEsRUFBRUMsRUFBRSxLQUFLRyxHQUFHQyxTQUFTLGVBQTFDOUQscUNBQUFBLGVBQTZDZSxJQUFJLEtBQUk7WUFDcEU7V0FDQ29GLElBQUksQ0FBQyxDQUFDQyxHQUFHQyxJQUFNQSxFQUFFdkMsU0FBUyxDQUFDNEcsYUFBYSxDQUFDdEUsRUFBRXRDLFNBQVM7SUFDekQ7SUFFQSxNQUFNNkcsd0JBQXdCdkwsOENBQU9BO29FQUFDO1lBQ3BDLElBQUksQ0FBQytELFdBQVcsT0FBT3JEO1lBRXZCLE9BQU87bUJBQUlBO2FBQWdCLENBQUNxRyxJQUFJOzRFQUFDLENBQUNDLEdBQUdDO29CQUNuQyxJQUFJQyxTQUFjO29CQUNsQixJQUFJQyxTQUFjO29CQUVsQixPQUFRcEQ7d0JBQ04sS0FBSzs0QkFDSG1ELFNBQVNGLEVBQUVyRixJQUFJLENBQUNnRSxXQUFXOzRCQUMzQndCLFNBQVNGLEVBQUV0RixJQUFJLENBQUNnRSxXQUFXOzRCQUMzQjt3QkFDRjs0QkFDRSxPQUFPO29CQUNYO29CQUVBLElBQUl1QixTQUFTQyxRQUFRLE9BQU9sRCxrQkFBa0IsUUFBUSxDQUFDLElBQUk7b0JBQzNELElBQUlpRCxTQUFTQyxRQUFRLE9BQU9sRCxrQkFBa0IsUUFBUSxJQUFJLENBQUM7b0JBQzNELE9BQU87Z0JBQ1Q7O1FBQ0Y7bUVBQUc7UUFBQ3ZEO1FBQWlCcUQ7UUFBV0U7S0FBYztJQUU5QyxxQkFDRSw4REFBQ3VIO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNDO3dCQUNDQyxTQUFTcks7d0JBQ1RtSyxXQUFVO2tDQUNYOzs7Ozs7a0NBR0QsOERBQUNHO3dCQUFHSCxXQUFVO2tDQUFrRDs7Ozs7O2tDQUNoRSw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDQztnQ0FDQ0MsU0FBUyxJQUFNN0ksdUJBQXVCO2dDQUN0QzJJLFdBQVU7O2tEQUVWLDhEQUFDSTt3Q0FDQ0MsT0FBTTt3Q0FDTkwsV0FBVTt3Q0FDVk0sTUFBSzt3Q0FDTEMsU0FBUTt3Q0FDUkMsUUFBTzs7MERBRVAsOERBQUNDO2dEQUNDQyxlQUFjO2dEQUNkQyxnQkFBZTtnREFDZkMsYUFBYTtnREFDYkMsR0FBRTs7Ozs7OzBEQUVKLDhEQUFDSjtnREFBS0MsZUFBYztnREFBUUMsZ0JBQWU7Z0RBQVFDLGFBQWE7Z0RBQUdDLEdBQUU7Ozs7Ozs7Ozs7OztvQ0FDakU7Ozs7Ozs7MENBR1IsOERBQUNaO2dDQUNDQyxTQUFTLElBQU1uSixrQkFBa0I7Z0NBQ2pDaUosV0FBVTs7a0RBRVYsOERBQUNJO3dDQUNDQyxPQUFNO3dDQUNOTCxXQUFVO3dDQUNWTSxNQUFLO3dDQUNMQyxTQUFRO3dDQUNSQyxRQUFPO2tEQUVQLDRFQUFDQzs0Q0FDQ0MsZUFBYzs0Q0FDZEMsZ0JBQWU7NENBQ2ZDLGFBQWE7NENBQ2JDLEdBQUU7Ozs7Ozs7Ozs7O29DQUVBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU9aLDhEQUFDZDtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDYztzQ0FBTzs7Ozs7O3dCQUFnQjt3QkFDaEIvSyxzQkFBc0JnTCxVQUFVO3dCQUFDO3dCQUFPaEwsc0JBQXNCaUwsTUFBTTt3QkFBQzt3QkFDNUVqTCxzQkFBc0JnTCxVQUFVLEdBQUc7d0JBQUU7d0JBQWFoTCxzQkFBc0JpTCxNQUFNO3dCQUFDOzs7Ozs7Ozs7Ozs7MEJBS3BGLDhEQUFDakI7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDaUI7d0JBQUdqQixXQUFVO2tDQUF5RDs7Ozs7O2tDQUN2RSw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNrQjt3Q0FBR2xCLFdBQVU7a0RBQXVEOzs7Ozs7a0RBQ3JFLDhEQUFDdEc7d0NBQUVzRyxXQUFVOzs0Q0FDVnBHLFdBQVd1SCxrQkFBa0IsQ0FBQ0Msd0JBQXdCLENBQUNDLE9BQU8sQ0FBQzs0Q0FBRzs7Ozs7OztrREFFckUsOERBQUMzSDt3Q0FBRXNHLFdBQVU7a0RBQWdEOzs7Ozs7Ozs7Ozs7NEJBSzlENUsscUJBQXFCd0UsV0FBV3VILGtCQUFrQixDQUFDRyx5QkFBeUIsQ0FBQ2xNLGtCQUFrQixrQkFDOUYsOERBQUMySztnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNrQjt3Q0FBR2xCLFdBQVU7a0RBQXlEOzs7Ozs7a0RBQ3ZFLDhEQUFDdEc7d0NBQUVzRyxXQUFVOzs0Q0FDVnBHLFdBQVd1SCxrQkFBa0IsQ0FBQ0cseUJBQXlCLENBQUNsTSxrQkFBa0IsQ0FBQ2dNLHdCQUF3QixDQUFDQyxPQUFPLENBQUM7NENBQUc7Ozs7Ozs7a0RBRWxILDhEQUFDM0g7d0NBQUVzRyxXQUFVO2tEQUNWcEcsV0FBV3VILGtCQUFrQixDQUFDRyx5QkFBeUIsQ0FBQ2xNLGtCQUFrQixDQUFDd0ssV0FBVzs7Ozs7Ozs7Ozs7OzBDQUs3Riw4REFBQ0c7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDa0I7d0NBQUdsQixXQUFVO2tEQUEyRDs7Ozs7O2tEQUN6RSw4REFBQ3RHO3dDQUFFc0csV0FBVTtrREFDVjVLLHFCQUFxQndFLFdBQVd1SCxrQkFBa0IsQ0FBQ0cseUJBQXlCLENBQUNsTSxrQkFBa0IsR0FDNUZ3RSxXQUFXdUgsa0JBQWtCLENBQUNHLHlCQUF5QixDQUFDbE0sa0JBQWtCLENBQUNtTSxrQkFBa0IsR0FDN0YzSCxXQUFXNEgsZ0JBQWdCLENBQUN6SSxNQUFNLENBQUNXLENBQUFBLElBQUtBLEVBQUUrSCxpQkFBaUIsR0FBRyxHQUFHcEYsTUFBTTs7Ozs7O2tEQUU3RSw4REFBQzNDO3dDQUFFc0csV0FBVTtrREFBb0Q7Ozs7Ozs7Ozs7OzswQ0FLbkUsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ2tCO3dDQUFHbEIsV0FBVTtrREFBMkQ7Ozs7OztrREFDekUsOERBQUN0Rzt3Q0FBRXNHLFdBQVU7a0RBQ1ZwRyxXQUFXdUgsa0JBQWtCLENBQUNPLHNCQUFzQixDQUFDQyxLQUFLOzs7Ozs7a0RBRTdELDhEQUFDakk7d0NBQUVzRyxXQUFVOzs0Q0FBb0Q7NENBQzdEcEcsV0FBV3VILGtCQUFrQixDQUFDTyxzQkFBc0IsQ0FBQ0UsU0FBUzs0Q0FBQzs0Q0FBSWhJLFdBQVd1SCxrQkFBa0IsQ0FBQ08sc0JBQXNCLENBQUNHLFVBQVUsQ0FBQ1IsT0FBTyxDQUFDOzRDQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBT3ZKaksscUNBQ0MsOERBQUMySTtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDaUI7d0NBQUdqQixXQUFVO2tEQUFvRDs7Ozs7O2tEQUNsRSw4REFBQ0M7d0NBQ0NDLFNBQVMsSUFBTTdJLHVCQUF1Qjt3Q0FDdEMySSxXQUFVO2tEQUVWLDRFQUFDSTs0Q0FBSUosV0FBVTs0Q0FBVU0sTUFBSzs0Q0FBT0UsUUFBTzs0Q0FBZUQsU0FBUTtzREFDakUsNEVBQUNFO2dEQUFLQyxlQUFjO2dEQUFRQyxnQkFBZTtnREFBUUMsYUFBYTtnREFBR0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FNM0UsOERBQUNkO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQzhCO3dDQUFNOUIsV0FBVTtrREFBa0U7Ozs7OztrREFDbkYsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQytCO2dEQUNDQyxNQUFLO2dEQUNMQyxPQUFPeks7Z0RBQ1AwSyxVQUFVLENBQUNDLElBQU0xSyxZQUFZMEssRUFBRUMsTUFBTSxDQUFDSCxLQUFLO2dEQUMzQ2xELGFBQVk7Z0RBQ1ppQixXQUFVOzs7Ozs7MERBRVosOERBQUNDO2dEQUNDQyxTQUFTO29EQUNQLElBQUkxSSxTQUFTa0YsSUFBSSxJQUFJO3dEQUNuQixJQUFJOzREQUNGLG9CQUFvQjs0REFDcEIsSUFBSWxELGNBQWNzRSxJQUFJLENBQUMzSCxDQUFBQSxXQUNyQkEsU0FBUytELFdBQVcsT0FBTzFDLFNBQVNrRixJQUFJLEdBQUd4QyxXQUFXLEtBQ3JEO2dFQUNENkMsTUFBTSxNQUFzQixPQUFoQnZGLFNBQVNrRixJQUFJLElBQUc7Z0VBQzVCOzREQUNGOzREQUVBLDBCQUEwQjs0REFDMUIsc0JBQXNCOzREQUN0QixNQUFNMkYsMEJBQTBCO2dFQUM5QnhKLElBQUksUUFBbUIsT0FBWDBFLEtBQUsrRSxHQUFHO2dFQUNwQnBNLE1BQU0sU0FBeUIsT0FBaEJzQixTQUFTa0YsSUFBSTtnRUFDNUJ2RyxVQUFVcUIsU0FBU2tGLElBQUk7Z0VBQ3ZCcEMsVUFBVTs0REFDWjs0REFFQSxhQUFhOzREQUNiakYsaUJBQWlCZ047NERBRWpCdEYsTUFBTSxVQUEwQixPQUFoQnZGLFNBQVNrRixJQUFJLElBQUc7NERBQ2hDakYsWUFBWTt3REFDZCxFQUFFLE9BQU91RixPQUFPOzREQUNkSCxRQUFRRyxLQUFLLENBQUMsY0FBY0E7NERBQzVCRCxNQUFNLGNBQThELE9BQWhEQyxpQkFBaUJDLFFBQVFELE1BQU1FLE9BQU8sR0FBRzt3REFDL0Q7b0RBQ0Y7Z0RBQ0Y7Z0RBQ0E4QyxXQUFVO2dEQUNWdUMsVUFBVSxDQUFDL0ssU0FBU2tGLElBQUk7MERBQ3pCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBT0wsOERBQUNxRDs7a0RBQ0MsOERBQUNtQjt3Q0FBR2xCLFdBQVU7a0RBQXlEOzs7Ozs7a0RBQ3ZFLDhEQUFDRDt3Q0FBSUMsV0FBVTs7NENBQ1p4RyxjQUFjSixHQUFHLENBQUMsQ0FBQ2lHLHNCQUNsQiw4REFBQ1U7b0RBRUNDLFdBQVU7OERBRVQxSSxpQkFBaUIrSCxzQkFDaEIsOERBQUNVO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQytCO2dFQUNDQyxNQUFLO2dFQUNMUSxjQUFjbkQ7Z0VBQ2RXLFdBQVU7Z0VBQ1Z5QyxXQUFXLENBQUNOO29FQUNWLElBQUlBLEVBQUVPLEdBQUcsS0FBSyxTQUFTO3dFQUNyQjlFLGtCQUFrQnlCLE9BQU84QyxFQUFFUSxhQUFhLENBQUNWLEtBQUs7b0VBQ2hELE9BQU8sSUFBSUUsRUFBRU8sR0FBRyxLQUFLLFVBQVU7d0VBQzdCbkwsZ0JBQWdCO29FQUNsQjtnRUFDRjtnRUFDQXFMLFNBQVM7Ozs7OzswRUFFWCw4REFBQzNDO2dFQUNDQyxTQUFTLElBQU0zSSxnQkFBZ0I7Z0VBQy9CeUksV0FBVTswRUFDWDs7Ozs7Ozs7Ozs7NkVBS0g7OzBFQUNFLDhEQUFDNkM7Z0VBQUs3QyxXQUFVOzBFQUFpQ1g7Ozs7OzswRUFDakQsOERBQUNVO2dFQUFJQyxXQUFVOztrRkFDYiw4REFBQ0M7d0VBQ0NDLFNBQVMsSUFBTTNJLGdCQUFnQjhIO3dFQUMvQlcsV0FBVTtrRkFDWDs7Ozs7O2tGQUdELDhEQUFDQzt3RUFDQ0MsU0FBUyxJQUFNZCxrQkFBa0JDO3dFQUNqQ1csV0FBVTtrRkFDWDs7Ozs7Ozs7Ozs7Ozs7bURBdENGWDs7Ozs7NENBOENSN0YsY0FBYzZDLE1BQU0sS0FBSyxtQkFDeEIsOERBQUMzQztnREFBRXNHLFdBQVU7MERBQW9EOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBVTlFbEosZ0NBQ0MsOERBQUNpSjtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUN2TCx1RUFBbUJBO3dCQUNsQnFPLFVBQVM7d0JBQ1QzTixVQUFVQTt3QkFDVkMsbUJBQW1CQTt3QkFDbkIyTixVQUFVMUY7d0JBQ1YyRixVQUFVLElBQU1qTSxrQkFBa0I7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTzFDLDhEQUFDZ0o7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDaUI7b0NBQUdqQixXQUFVOzhDQUFvRDs7Ozs7OzhDQUNsRSw4REFBQzZDO29DQUFLN0MsV0FBVTs4Q0FDYnRILENBQUFBLDJCQUFBQSxxQ0FBQUEsZUFBZ0J4QyxJQUFJLEtBQUk7Ozs7OztnQ0FFMUJ3QyxnQ0FDQyw4REFBQ21LO29DQUFLN0MsV0FBVTs7d0NBQ2J0SCxlQUFldUssU0FBUzt3Q0FBQzt3Q0FBSXZLLGVBQWV3SyxPQUFPOzs7Ozs7Ozs7Ozs7O3NDQUkxRCw4REFBQ25EOzRCQUFJQyxXQUFVOztnQ0FBMkM7Z0NBQ2hEbEgsMkJBQTJCdUQsTUFBTTtnQ0FBQzs4Q0FDMUMsOERBQUM4Rzs7Ozs7Z0NBQUs7Z0NBQ0NsTyxnQkFBZ0JvSCxNQUFNO2dDQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTXBDLDhEQUFDMEQ7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDQztvQ0FDQ0MsU0FBUyxJQUFNakosWUFBWTtvQ0FDM0IrSSxXQUFXeEwsOENBQUVBLENBQ1gsMENBQ0F3QyxhQUFhLFlBQ1QsMkJBQ0E7OENBRVA7Ozs7Ozs4Q0FHRCw4REFBQ2lKO29DQUNDQyxTQUFTLElBQU1qSixZQUFZO29DQUMzQitJLFdBQVd4TCw4Q0FBRUEsQ0FDWCwwQ0FDQXdDLGFBQWEsV0FDVCwyQkFDQTs4Q0FFUDs7Ozs7Ozs7Ozs7O3NDQUlILDhEQUFDK0k7NEJBQUlDLFdBQVU7c0NBQ1poSixhQUFhLFlBQ1YsTUFBOEM4QixPQUF4Q2lCLDRCQUE0QnNDLE1BQU0sRUFBQyxPQUF1QyxPQUFsQ3ZELDJCQUEyQnVELE1BQU0sRUFBQyxhQUNoRixNQUE2QixPQUF2QnBILGdCQUFnQm9ILE1BQU0sRUFBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFLdENyRixhQUFhLDBCQUNaOztrQ0FFRSw4REFBQytJO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7O2tEQUNDLDhEQUFDK0I7d0NBQU05QixXQUFVO2tEQUFrRTs7Ozs7O2tEQUNuRiw4REFBQytCO3dDQUNDQyxNQUFLO3dDQUNMQyxPQUFPekw7d0NBQ1AwTCxVQUFVLENBQUNDLElBQU0xTCxjQUFjMEwsRUFBRUMsTUFBTSxDQUFDSCxLQUFLO3dDQUM3Q2xELGFBQVk7d0NBQ1ppQixXQUFVOzs7Ozs7Ozs7Ozs7MENBR2QsOERBQUNEOztrREFDQyw4REFBQytCO3dDQUFNOUIsV0FBVTtrREFBa0U7Ozs7OztrREFDbkYsOERBQUNvRDt3Q0FDQ25CLE9BQU92TDt3Q0FDUHdMLFVBQVUsQ0FBQ0MsSUFBTXhMLGtCQUFrQndMLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSzt3Q0FDakRqQyxXQUFVOzswREFFViw4REFBQ3FEO2dEQUFPcEIsT0FBTTswREFBRzs7Ozs7OzRDQUNoQi9JLHlCQUF5QkUsR0FBRyxDQUFDLENBQUNqRCx5QkFDN0IsOERBQUNrTjtvREFBc0JwQixPQUFPOUw7OERBQzNCQTttREFEVUE7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQU1uQiw4REFBQzRKOztrREFDQyw4REFBQytCO3dDQUFNOUIsV0FBVTtrREFBa0U7Ozs7OztrREFDbkYsOERBQUNvRDt3Q0FDQ25CLE9BQU9yTDt3Q0FDUHNMLFVBQVUsQ0FBQ0MsSUFBTXRMLGdCQUFnQnNMLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSzt3Q0FDL0NqQyxXQUFVOzswREFFViw4REFBQ3FEO2dEQUFPcEIsT0FBTTswREFBTTs7Ozs7OzBEQUNwQiw4REFBQ29CO2dEQUFPcEIsT0FBTTswREFBUzs7Ozs7OzBEQUN2Qiw4REFBQ29CO2dEQUFPcEIsT0FBTTswREFBVzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU0vQiw4REFBQ2xDO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNpQjtvQ0FBR2pCLFdBQVU7O3dDQUNYdEgsMkJBQUFBLHFDQUFBQSxlQUFnQnhDLElBQUk7d0NBQUM7d0NBQVM2RCw0QkFBNEJzQyxNQUFNO3dDQUFDOzs7Ozs7Ozs7Ozs7MENBR3RFLDhEQUFDMEQ7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNzRDtvQ0FBTXRELFdBQVU7O3NEQUNmLDhEQUFDdUQ7NENBQU12RCxXQUFVO3NEQUNmLDRFQUFDd0Q7O2tFQUNDLDhEQUFDQzt3REFBR3pELFdBQVU7a0VBQ1osNEVBQUNDOzREQUNDQyxTQUFTLElBQU1yRyxXQUFXOzREQUMxQm1HLFdBQVU7OzhFQUVWLDhEQUFDNkM7OEVBQUs7Ozs7Ozs4RUFDTiw4REFBQ3pDO29FQUFJSixXQUFVO29FQUFVTSxNQUFLO29FQUFPRSxRQUFPO29FQUFlRCxTQUFROzhFQUNoRWpJLGNBQWMsU0FDYkUsa0JBQWtCLHNCQUNoQiw4REFBQ2lJO3dFQUNDQyxlQUFjO3dFQUNkQyxnQkFBZTt3RUFDZkMsYUFBYTt3RUFDYkMsR0FBRTt3RUFDRmIsV0FBVTs7Ozs7NkZBR1osOERBQUNTO3dFQUNDQyxlQUFjO3dFQUNkQyxnQkFBZTt3RUFDZkMsYUFBYTt3RUFDYkMsR0FBRTt3RUFDRmIsV0FBVTs7Ozs7NkZBSWQsOERBQUNTO3dFQUNDQyxlQUFjO3dFQUNkQyxnQkFBZTt3RUFDZkMsYUFBYTt3RUFDYkMsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrRUFNWiw4REFBQzRDO3dEQUFHekQsV0FBVTtrRUFDWiw0RUFBQ0M7NERBQ0NDLFNBQVMsSUFBTXJHLFdBQVc7NERBQzFCbUcsV0FBVTs7OEVBRVYsOERBQUM2Qzs4RUFBSzs7Ozs7OzhFQUNOLDhEQUFDekM7b0VBQUlKLFdBQVU7b0VBQVVNLE1BQUs7b0VBQU9FLFFBQU87b0VBQWVELFNBQVE7OEVBQ2hFakksY0FBYyxhQUNiRSxrQkFBa0Isc0JBQ2hCLDhEQUFDaUk7d0VBQ0NDLGVBQWM7d0VBQ2RDLGdCQUFlO3dFQUNmQyxhQUFhO3dFQUNiQyxHQUFFO3dFQUNGYixXQUFVOzs7Ozs2RkFHWiw4REFBQ1M7d0VBQ0NDLGVBQWM7d0VBQ2RDLGdCQUFlO3dFQUNmQyxhQUFhO3dFQUNiQyxHQUFFO3dFQUNGYixXQUFVOzs7Ozs2RkFJZCw4REFBQ1M7d0VBQ0NDLGVBQWM7d0VBQ2RDLGdCQUFlO3dFQUNmQyxhQUFhO3dFQUNiQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tFQU1aLDhEQUFDNEM7d0RBQUd6RCxXQUFVO2tFQUNaLDRFQUFDQzs0REFDQ0MsU0FBUyxJQUFNckcsV0FBVzs0REFDMUJtRyxXQUFVOzs4RUFFViw4REFBQzZDOzhFQUFLOzs7Ozs7OEVBQ04sOERBQUN6QztvRUFBSUosV0FBVTtvRUFBVU0sTUFBSztvRUFBT0UsUUFBTztvRUFBZUQsU0FBUTs4RUFDaEVqSSxjQUFjLGFBQ2JFLGtCQUFrQixzQkFDaEIsOERBQUNpSTt3RUFDQ0MsZUFBYzt3RUFDZEMsZ0JBQWU7d0VBQ2ZDLGFBQWE7d0VBQ2JDLEdBQUU7d0VBQ0ZiLFdBQVU7Ozs7OzZGQUdaLDhEQUFDUzt3RUFDQ0MsZUFBYzt3RUFDZEMsZ0JBQWU7d0VBQ2ZDLGFBQWE7d0VBQ2JDLEdBQUU7d0VBQ0ZiLFdBQVU7Ozs7OzZGQUlkLDhEQUFDUzt3RUFDQ0MsZUFBYzt3RUFDZEMsZ0JBQWU7d0VBQ2ZDLGFBQWE7d0VBQ2JDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBTVosOERBQUM0Qzt3REFBR3pELFdBQVU7a0VBQ1osNEVBQUNDOzREQUNDQyxTQUFTLElBQU1yRyxXQUFXOzREQUMxQm1HLFdBQVU7OzhFQUVWLDhEQUFDNkM7OEVBQUs7Ozs7Ozs4RUFDTiw4REFBQ3pDO29FQUFJSixXQUFVO29FQUFVTSxNQUFLO29FQUFPRSxRQUFPO29FQUFlRCxTQUFROzhFQUNoRWpJLGNBQWMsbUJBQ2JFLGtCQUFrQixzQkFDaEIsOERBQUNpSTt3RUFDQ0MsZUFBYzt3RUFDZEMsZ0JBQWU7d0VBQ2ZDLGFBQWE7d0VBQ2JDLEdBQUU7d0VBQ0ZiLFdBQVU7Ozs7OzZGQUdaLDhEQUFDUzt3RUFDQ0MsZUFBYzt3RUFDZEMsZ0JBQWU7d0VBQ2ZDLGFBQWE7d0VBQ2JDLEdBQUU7d0VBQ0ZiLFdBQVU7Ozs7OzZGQUlkLDhEQUFDUzt3RUFDQ0MsZUFBYzt3RUFDZEMsZ0JBQWU7d0VBQ2ZDLGFBQWE7d0VBQ2JDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBTVosOERBQUM0Qzt3REFBR3pELFdBQVU7a0VBQW9HOzs7Ozs7a0VBR2xILDhEQUFDeUQ7d0RBQUd6RCxXQUFVO2tFQUFvRzs7Ozs7O2tFQUdsSCw4REFBQ3lEO3dEQUFHekQsV0FBVTtrRUFBcUc7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQUt2SCw4REFBQzBEOzRDQUFNMUQsV0FBVTtzREFDZGpHLDRCQUE0QnNDLE1BQU0sR0FBRyxJQUNwQ3RDLDRCQUE0QlgsR0FBRyxDQUFDLENBQUM4RjtvREEwQmZBO2dEQXpCaEIsTUFBTXlFLFVBQVVoRSxzQkFBc0JULG1CQUFtQm5FLGFBQWE7Z0RBQ3RFLHFCQUNFLDhEQUFDeUk7b0RBQStCeEQsV0FBVTs7c0VBQ3hDLDhEQUFDNEQ7NERBQUc1RCxXQUFVO3NFQUNYZCxtQkFBbUJoSixJQUFJOzs7Ozs7c0VBRTFCLDhEQUFDME47NERBQUc1RCxXQUFVO3NFQUNaLDRFQUFDNkM7Z0VBQUs3QyxXQUFVOzBFQUNiZCxtQkFBbUIvSSxRQUFRLElBQUk7Ozs7Ozs7Ozs7O3NFQUdwQyw4REFBQ3lOOzREQUFHNUQsV0FBVTtzRUFDWGQsbUJBQW1CdkQsUUFBUSxJQUFJOzs7Ozs7c0VBRWxDLDhEQUFDaUk7NERBQUc1RCxXQUFVO3NFQUNaLDRFQUFDRDtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUM2Qzt3RUFDQzdDLFdBQVd4TCw4Q0FBRUEsQ0FDWCx5Q0FDQUssNEVBQXFCQSxDQUFDcUssbUJBQW1CN0QsYUFBYTtrRkFHdkR6RywyRUFBb0JBLENBQUNzSyxtQkFBbUI3RCxhQUFhOzs7Ozs7a0ZBRXhELDhEQUFDd0g7d0VBQUs3QyxXQUFVOzs0RUFBMkM7NEVBQ25EZCxFQUFBQSxxQ0FBQUEsbUJBQW1COUQsY0FBYyxjQUFqQzhELHlEQUFBQSxtQ0FBbUNtQyxPQUFPLENBQUMsT0FBTTs0RUFBRTs7Ozs7OztrRkFFM0QsOERBQUN3Qjt3RUFBSzdDLFdBQVU7OzRFQUEyQzs0RUFDdkRkLG1CQUFtQnZFLGFBQWEsSUFBSTs0RUFBRTs0RUFBRXVFLG1CQUFtQnhFLGlCQUFpQixJQUFJOzRFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0VBSTFGLDhEQUFDa0o7NERBQUc1RCxXQUFVO3NFQUNYLENBQUM7Z0VBQ0EsTUFBTTZELGtCQUFrQmpLLFdBQVc0SCxnQkFBZ0IsQ0FBQzdJLElBQUksQ0FBQ2UsQ0FBQUEsSUFBS0EsRUFBRWIsRUFBRSxLQUFLcUcsbUJBQW1CbkUsYUFBYTtnRUFDdkcsSUFBSSxDQUFDOEksaUJBQWlCLHFCQUFPLDhEQUFDaEI7b0VBQUs3QyxXQUFVOzhFQUEyQzs7Ozs7O2dFQUV4RixxQkFDRSw4REFBQ0Q7b0VBQUlDLFdBQVU7O3NGQUNiLDhEQUFDRDs0RUFBSUMsV0FBVTs7OEZBQ2IsOERBQUM2QztvRkFBSzdDLFdBQVU7O3dGQUF1RDt3RkFDaEU2RCxnQkFBZ0JDLG9CQUFvQixDQUFDQyxrQkFBa0I7Ozs7Ozs7Z0ZBRTdERixnQkFBZ0JHLG1CQUFtQixrQkFDbEMsOERBQUNuQjtvRkFBSzdDLFdBQVU7OEZBQW9HOzs7Ozs7Ozs7Ozs7d0VBS3ZINUsscUJBQXFCeU8sZ0JBQWdCSSwyQkFBMkIsQ0FBQzdPLGtCQUFrQixrQkFDbEYsOERBQUN5Tjs0RUFBSzdDLFdBQVU7O2dGQUEyQztnRkFDcEQ2RCxnQkFBZ0JJLDJCQUEyQixDQUFDN08sa0JBQWtCLENBQUMyTyxrQkFBa0I7Ozs7Ozs7c0ZBRzFGLDhEQUFDbEI7NEVBQUs3QyxXQUFVOztnRkFBMkM7Z0ZBQ25ENkQsZ0JBQWdCcEMsaUJBQWlCLENBQUNKLE9BQU8sQ0FBQztnRkFBRzs7Ozs7Ozs7Ozs7Ozs0REFJM0Q7Ozs7OztzRUFFRiw4REFBQ3VDOzREQUFHNUQsV0FBVTtzRUFDWiw0RUFBQ0Q7Z0VBQUlDLFdBQVU7O29FQUNaMkQsUUFBUXhILEtBQUssQ0FBQyxHQUFHLEdBQUcvQyxHQUFHLENBQUMsQ0FBQzhLLGtCQUN4Qiw4REFBQ3JCOzRFQUVDN0MsV0FBVTs0RUFDVlgsT0FBTyxHQUFxQjZFLE9BQWxCQSxFQUFFdEUsV0FBVyxFQUFDLE1BQWUsT0FBWHNFLEVBQUUvTixRQUFRO3NGQUVyQytOLEVBQUV0RSxXQUFXOzJFQUpUc0UsRUFBRXJMLEVBQUU7Ozs7O29FQU9aOEssUUFBUXRILE1BQU0sR0FBRyxtQkFDaEIsOERBQUN3Rzt3RUFBSzdDLFdBQVU7OzRFQUEyQzs0RUFBRTJELFFBQVF0SCxNQUFNLEdBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7OztzRUFJcEYsOERBQUN1SDs0REFBRzVELFdBQVU7OzhFQUNaLDhEQUFDQztvRUFDQ0MsU0FBUzt3RUFDUCxNQUFNaUUsb0JBQW9CbFAsZ0JBQWdCMEQsSUFBSSxDQUM1QyxDQUFDZSxJQUFNQSxFQUFFYixFQUFFLEtBQUtxRyxtQkFBbUJuRSxhQUFhO3dFQUVsRCxJQUFJb0osbUJBQW1COzRFQUNyQmhOLDRCQUE0QmdOO3dFQUM5QjtvRUFDRjtvRUFDQW5FLFdBQVU7OEVBQ1g7Ozs7Ozs4RUFHRCw4REFBQ0M7b0VBQ0NDLFNBQVMsSUFBTTNKLDZCQUE2QjJJO29FQUM1Q2MsV0FBVTs4RUFDWDs7Ozs7OzhFQUdELDhEQUFDQztvRUFDQ0MsU0FBUzt3RUFDUCxJQUFJN0IsT0FBT0MsT0FBTyxDQUFDLFNBQWlDLE9BQXhCWSxtQkFBbUJoSixJQUFJLEVBQUMsa0JBQWdCOzRFQUNsRVAsb0JBQW9CdUosbUJBQW1CckcsRUFBRTt3RUFDM0M7b0VBQ0Y7b0VBQ0FtSCxXQUFVOzhFQUNYOzs7Ozs7Ozs7Ozs7O21EQXRHSWQsbUJBQW1CckcsRUFBRTs7Ozs7NENBNEdsQyxtQkFFQSw4REFBQzJLOzBEQUNDLDRFQUFDSTtvREFBR1EsU0FBUztvREFBR3BFLFdBQVU7OERBQWlFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7b0JBV3RHcEUsNEJBQTRCUyxNQUFNLEdBQUcsbUJBQ3BDLDhEQUFDMEQ7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNpQjt3Q0FBR2pCLFdBQVU7OzRDQUFvRDs0Q0FDdER0SCwyQkFBQUEscUNBQUFBLGVBQWdCeEMsSUFBSTs7Ozs7OztrREFFaEMsOERBQUMrSjt3Q0FDQ0MsU0FBUyxJQUFNdkksbUJBQW1CLENBQUNEO3dDQUNuQ3NJLFdBQVU7OzBEQUVWLDhEQUFDSTtnREFDQ0MsT0FBTTtnREFDTkwsV0FBVTtnREFDVk0sTUFBSztnREFDTEMsU0FBUTtnREFDUkMsUUFBTzswREFFUCw0RUFBQ0M7b0RBQUtDLGVBQWM7b0RBQVFDLGdCQUFlO29EQUFRQyxhQUFhO29EQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs0Q0FFdEVuSixrQkFBa0IsT0FBTyxTQUE0QyxPQUFuQ2tFLDRCQUE0QlMsTUFBTSxFQUFDOzs7Ozs7Ozs7Ozs7OzRCQUl6RTNFLGlDQUNDLDhEQUFDcUk7Z0NBQUlDLFdBQVU7O2tEQUViLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEOztrRUFDQyw4REFBQytCO3dEQUFNOUIsV0FBVTtrRUFBa0U7Ozs7OztrRUFHbkYsOERBQUMrQjt3REFDQ0MsTUFBSzt3REFDTEMsT0FBT3JLO3dEQUNQc0ssVUFBVSxDQUFDQzs0REFDVHRLLHlCQUF5QnNLLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSzs0REFDdkM3SixlQUFlO3dEQUNqQjt3REFDQTJHLGFBQVk7d0RBQ1ppQixXQUFVOzs7Ozs7Ozs7Ozs7MERBR2QsOERBQUNEOztrRUFDQyw4REFBQytCO3dEQUFNOUIsV0FBVTtrRUFBa0U7Ozs7OztrRUFHbkYsOERBQUNvRDt3REFDQ25CLE9BQU9uSzt3REFDUG9LLFVBQVUsQ0FBQ0M7NERBQ1RwSyw2QkFBNkJvSyxFQUFFQyxNQUFNLENBQUNILEtBQUs7NERBQzNDN0osZUFBZTt3REFDakI7d0RBQ0E0SCxXQUFVOzswRUFFViw4REFBQ3FEO2dFQUFPcEIsT0FBTTswRUFBRzs7Ozs7OzREQUNoQnpJLGNBQWNKLEdBQUcsQ0FBQyxDQUFDakQseUJBQ2xCLDhEQUFDa047b0VBQXNCcEIsT0FBTzlMOzhFQUMzQkE7bUVBRFVBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztvQ0FTcEI2QixxQkFBcUIyRixJQUFJLEdBQUcsbUJBQzNCLDhEQUFDb0M7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDNkM7Z0RBQUs3QyxXQUFVOztvREFBMkM7b0RBQ3BEaEkscUJBQXFCMkYsSUFBSTtvREFBQzs7Ozs7OzswREFFakMsOERBQUNvQztnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNDO3dEQUNDQyxTQUFTLElBQU1qSSx3QkFBd0IsSUFBSUM7d0RBQzNDOEgsV0FBVTtrRUFDWDs7Ozs7O2tFQUdELDhEQUFDQzt3REFDQ0MsU0FBU3hDO3dEQUNUc0MsV0FBVTtrRUFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQVFQLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDWmpFLDhCQUE4QmpCLFlBQVksQ0FBQzFCLEdBQUcsQ0FBQyxDQUFDNEMsNEJBQy9DLDhEQUFDK0Q7Z0RBRUNDLFdBQVd4TCw4Q0FBRUEsQ0FDWCwwREFDQXdELHFCQUFxQjhELEdBQUcsQ0FBQ0UsWUFBWW5ELEVBQUUsSUFDbkMsbURBQ0E7Z0RBRU5xSCxTQUFTO29EQUNQLE1BQU1tRSxjQUFjLElBQUluTSxJQUFJRjtvREFDNUIsSUFBSXFNLFlBQVl2SSxHQUFHLENBQUNFLFlBQVluRCxFQUFFLEdBQUc7d0RBQ25Dd0wsWUFBWUMsTUFBTSxDQUFDdEksWUFBWW5ELEVBQUU7b0RBQ25DLE9BQU87d0RBQ0x3TCxZQUFZRSxHQUFHLENBQUN2SSxZQUFZbkQsRUFBRTtvREFDaEM7b0RBQ0FaLHdCQUF3Qm9NO2dEQUMxQjswREFFQSw0RUFBQ3RFO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0Q7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDdEc7b0VBQUVzRyxXQUFVOzhFQUFxRGhFLFlBQVk5RixJQUFJOzs7Ozs7Z0VBQ2pGOEYsWUFBWTdGLFFBQVEsa0JBQ25CLDhEQUFDdUQ7b0VBQUVzRyxXQUFVOzt3RUFBMkM7d0VBQUtoRSxZQUFZN0YsUUFBUTs7Ozs7OztnRUFFakY7b0VBQ0EsTUFBTTBOLGtCQUFrQmpLLFdBQVc0SCxnQkFBZ0IsQ0FBQzdJLElBQUksQ0FBQ2UsQ0FBQUEsSUFBS0EsRUFBRWIsRUFBRSxLQUFLbUQsWUFBWW5ELEVBQUU7b0VBQ3JGLElBQUksQ0FBQ2dMLGlCQUFpQixPQUFPO29FQUU3QixxQkFDRSw4REFBQzlEO3dFQUFJQyxXQUFVOzswRkFDYiw4REFBQ0Q7Z0ZBQUlDLFdBQVU7O2tHQUNiLDhEQUFDNkM7d0ZBQUs3QyxXQUFVOzs0RkFBMkM7NEZBQ3BENkQsZ0JBQWdCQyxvQkFBb0IsQ0FBQ0Msa0JBQWtCOzs7Ozs7O29GQUU3REYsZ0JBQWdCRyxtQkFBbUIsa0JBQ2xDLDhEQUFDbkI7d0ZBQUs3QyxXQUFVO2tHQUFvRzs7Ozs7Ozs7Ozs7OzRFQUt2SDVLLHFCQUFxQnlPLGdCQUFnQkksMkJBQTJCLENBQUM3TyxrQkFBa0Isa0JBQ2xGLDhEQUFDeU47Z0ZBQUs3QyxXQUFVOztvRkFBMkM7b0ZBQ3BENkQsZ0JBQWdCSSwyQkFBMkIsQ0FBQzdPLGtCQUFrQixDQUFDMk8sa0JBQWtCOzs7Ozs7Ozs7Ozs7O2dFQUtoRzs7Ozs7OztzRUFFRiw4REFBQ2hDOzREQUNDQyxNQUFLOzREQUNMd0MsU0FBU3hNLHFCQUFxQjhELEdBQUcsQ0FBQ0UsWUFBWW5ELEVBQUU7NERBQ2hEcUosVUFBVSxLQUFPOzREQUNqQmxDLFdBQVU7Ozs7Ozs7Ozs7OzsrQ0FwRFRoRSxZQUFZbkQsRUFBRTs7Ozs7Ozs7OztvQ0E0RHhCa0QsOEJBQThCTyxVQUFVLEdBQUcsbUJBQzFDLDhEQUFDeUQ7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTs7b0RBQTJDO29EQUNuRDdILENBQUFBLGNBQWMsS0FBS0UsV0FBVztvREFBRTtvREFBRztvREFDdkNrRSxLQUFLa0ksR0FBRyxDQUFDdE0sY0FBY0UsVUFBVTBELDhCQUE4QkssS0FBSztvREFBRTtvREFBRztvREFDekVMLDhCQUE4QkssS0FBSztvREFBQzs7Ozs7OzswREFFdkMsOERBQUMyRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNDO3dEQUNDQyxTQUFTLElBQU05SCxlQUFlbUUsS0FBS21JLEdBQUcsQ0FBQyxHQUFHdk0sY0FBYzt3REFDeERvSyxVQUFVcEssZ0JBQWdCO3dEQUMxQjZILFdBQVU7a0VBQ1g7Ozs7OztrRUFHRCw4REFBQzZDO3dEQUFLN0MsV0FBVTs7NERBQ2I3SDs0REFBWTs0REFBSTRELDhCQUE4Qk8sVUFBVTs7Ozs7OztrRUFFM0QsOERBQUMyRDt3REFDQ0MsU0FBUyxJQUNQOUgsZUFBZW1FLEtBQUtrSSxHQUFHLENBQUMxSSw4QkFBOEJPLFVBQVUsRUFBRW5FLGNBQWM7d0RBRWxGb0ssVUFBVXBLLGdCQUFnQjRELDhCQUE4Qk8sVUFBVTt3REFDbEUwRCxXQUFVO2tFQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7b0NBT05qRSw4QkFBOEJqQixZQUFZLENBQUN1QixNQUFNLEtBQUssbUJBQ3JELDhEQUFDMEQ7d0NBQUlDLFdBQVU7a0RBQ1pwRSw0QkFBNEJTLE1BQU0sR0FBRyxJQUFJLGtCQUFrQjs7Ozs7Ozs7Ozs7Ozs7Ozs7OzsrQkFTMUUsV0FBVyxpQkFDWCw4REFBQzBEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNpQjs0QkFBR2pCLFdBQVU7O2dDQUFvRDtnQ0FDdkQvSyxnQkFBZ0JvSCxNQUFNO2dDQUFDOzs7Ozs7Ozs7Ozs7a0NBR3BDLDhEQUFDMEQ7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNzRDs0QkFBTXRELFdBQVU7OzhDQUNmLDhEQUFDdUQ7b0NBQU12RCxXQUFVOzhDQUNmLDRFQUFDd0Q7OzBEQUNDLDhEQUFDQztnREFBR3pELFdBQVU7MERBQ1osNEVBQUNDO29EQUNDQyxTQUFTLElBQU1yRyxXQUFXO29EQUMxQm1HLFdBQVU7O3NFQUVWLDhEQUFDNkM7c0VBQUs7Ozs7OztzRUFDTiw4REFBQ3pDOzREQUFJSixXQUFVOzREQUFVTSxNQUFLOzREQUFPRSxRQUFPOzREQUFlRCxTQUFRO3NFQUNoRWpJLGNBQWMsU0FDYkUsa0JBQWtCLHNCQUNoQiw4REFBQ2lJO2dFQUNDQyxlQUFjO2dFQUNkQyxnQkFBZTtnRUFDZkMsYUFBYTtnRUFDYkMsR0FBRTtnRUFDRmIsV0FBVTs7Ozs7cUZBR1osOERBQUNTO2dFQUNDQyxlQUFjO2dFQUNkQyxnQkFBZTtnRUFDZkMsYUFBYTtnRUFDYkMsR0FBRTtnRUFDRmIsV0FBVTs7Ozs7cUZBSWQsOERBQUNTO2dFQUNDQyxlQUFjO2dFQUNkQyxnQkFBZTtnRUFDZkMsYUFBYTtnRUFDYkMsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFNWiw4REFBQzRDO2dEQUFHekQsV0FBVTswREFBb0c7Ozs7OzswREFHbEgsOERBQUN5RDtnREFBR3pELFdBQVU7MERBQXFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FLdkgsOERBQUMwRDtvQ0FBTTFELFdBQVU7OENBQ2RGLHNCQUFzQjFHLEdBQUcsQ0FBQyxDQUFDNEM7d0NBQzFCLE1BQU0ySCxVQUFVaEUsc0JBQXNCM0QsWUFBWW5ELEVBQUU7d0NBQ3BELHFCQUNFLDhEQUFDMks7NENBQXdCeEQsV0FBVTs7OERBQ2pDLDhEQUFDNEQ7b0RBQUc1RCxXQUFVOzhEQUNYaEUsWUFBWTlGLElBQUk7Ozs7Ozs4REFFbkIsOERBQUMwTjtvREFBRzVELFdBQVU7OERBQ1osNEVBQUNEO3dEQUFJQyxXQUFVOzs0REFDWjJELFFBQVF2SyxHQUFHLENBQUMsQ0FBQzhLLGtCQUNaLDhEQUFDckI7b0VBRUM3QyxXQUFVO29FQUNWWCxPQUFPLEdBQXFCNkUsT0FBbEJBLEVBQUV0RSxXQUFXLEVBQUMsTUFBZSxPQUFYc0UsRUFBRS9OLFFBQVE7OEVBRXJDK04sRUFBRXRFLFdBQVc7bUVBSlRzRSxFQUFFckwsRUFBRTs7Ozs7NERBT1o4SyxRQUFRdEgsTUFBTSxLQUFLLG1CQUNsQiw4REFBQ3dHO2dFQUFLN0MsV0FBVTswRUFBMkM7Ozs7Ozs7Ozs7Ozs7Ozs7OzhEQUlqRSw4REFBQzREO29EQUFHNUQsV0FBVTs7c0VBQ1osOERBQUNDOzREQUNDQyxTQUFTLElBQU0vSSw0QkFBNEI2RTs0REFDM0NnRSxXQUFVO3NFQUNYOzs7Ozs7c0VBR0QsOERBQUNDOzREQUNDQyxTQUFTLElBQU03SixzQkFBc0IyRjs0REFDckNnRSxXQUFVO3NFQUNYOzs7Ozs7c0VBR0QsOERBQUNDOzREQUNDQyxTQUFTO2dFQUNQLElBQUk3QixPQUFPQyxPQUFPLENBQUMsYUFBOEIsT0FBakJ0QyxZQUFZOUYsSUFBSSxFQUFDLHFCQUFtQjtvRUFDbEVSLG9CQUFvQnNHLFlBQVluRCxFQUFFO2dFQUNwQzs0REFDRjs0REFDQW1ILFdBQVU7c0VBQ1g7Ozs7Ozs7Ozs7Ozs7MkNBeENJaEUsWUFBWW5ELEVBQUU7Ozs7O29DQThDM0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVFWLDhEQUFDa0g7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDaUI7d0JBQUdqQixXQUFVO2tDQUNYNUoscUJBQXFCLFNBQVNFLDRCQUE0QixZQUFZOzs7Ozs7b0JBR3hFQSw0QkFDQyxhQUFhLGlCQUNiLDhEQUFDeUo7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDs7a0RBQ0MsOERBQUMrQjt3Q0FBTTlCLFdBQVU7a0RBQWtFOzs7Ozs7a0RBQ25GLDhEQUFDK0I7d0NBQ0NDLE1BQUs7d0NBQ0xDLE9BQU8zTCwwQkFBMEJKLElBQUk7d0NBQ3JDZ00sVUFBVSxDQUFDQyxJQUFNNUwsNkJBQTZCO2dEQUFFLEdBQUdELHlCQUF5QjtnREFBRUosTUFBTWlNLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSzs0Q0FBQzt3Q0FDbkdqQyxXQUFVOzs7Ozs7Ozs7Ozs7MENBR2QsOERBQUNEOztrREFDQyw4REFBQytCO3dDQUFNOUIsV0FBVTtrREFBa0U7Ozs7OztrREFDbkYsOERBQUNvRDt3Q0FDQ25CLE9BQU8zTCwwQkFBMEJILFFBQVE7d0NBQ3pDK0wsVUFBVSxDQUFDQyxJQUNUNUwsNkJBQTZCO2dEQUFFLEdBQUdELHlCQUF5QjtnREFBRUgsVUFBVWdNLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSzs0Q0FBQzt3Q0FFeEZqQyxXQUFVOzswREFFViw4REFBQ3FEO2dEQUFPcEIsT0FBTTswREFBRzs7Ozs7OzRDQUNoQnpJLGNBQWNKLEdBQUcsQ0FBQyxDQUFDakQseUJBQ2xCLDhEQUFDa047b0RBQXNCcEIsT0FBTzlMOzhEQUMzQkE7bURBRFVBOzs7Ozs7Ozs7Ozs7Ozs7OzswQ0FNbkIsOERBQUM0Sjs7a0RBQ0MsOERBQUMrQjt3Q0FBTTlCLFdBQVU7a0RBQWtFOzs7Ozs7a0RBQ25GLDhEQUFDK0I7d0NBQ0NDLE1BQUs7d0NBQ0xDLE9BQU8zTCwwQkFBMEJxRixRQUFRLElBQUk7d0NBQzdDdUcsVUFBVSxDQUFDQyxJQUNUNUwsNkJBQTZCO2dEQUFFLEdBQUdELHlCQUF5QjtnREFBRXFGLFVBQVV3RyxFQUFFQyxNQUFNLENBQUNILEtBQUs7NENBQUM7d0NBRXhGakMsV0FBVTs7Ozs7Ozs7Ozs7OzBDQUdkLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQzhCO29DQUFNOUIsV0FBVTs7c0RBQ2YsOERBQUMrQjs0Q0FDQ0MsTUFBSzs0Q0FDTHdDLFNBQVNsTywwQkFBMEJnRSxRQUFROzRDQUMzQzRILFVBQVUsQ0FBQ0MsSUFDVDVMLDZCQUE2QjtvREFBRSxHQUFHRCx5QkFBeUI7b0RBQUVnRSxVQUFVNkgsRUFBRUMsTUFBTSxDQUFDb0MsT0FBTztnREFBQzs0Q0FFMUZ4RSxXQUFVOzs7Ozs7c0RBRVosOERBQUM2Qzs0Q0FBSzdDLFdBQVU7c0RBQTJDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OytCQUtqRSxnQkFBZ0IsaUJBQ2hCLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEOztrREFDQyw4REFBQytCO3dDQUFNOUIsV0FBVTtrREFBa0U7Ozs7OztrREFDbkYsOERBQUMrQjt3Q0FDQ0MsTUFBSzt3Q0FDTEMsT0FBTzdMLHFCQUFxQkEsbUJBQW1CRixJQUFJLEdBQUdGLGVBQWVFLElBQUk7d0NBQ3pFZ00sVUFBVSxDQUFDQyxJQUNUL0wscUJBQ0lDLHNCQUFzQjtnREFBRSxHQUFHRCxrQkFBa0I7Z0RBQUVGLE1BQU1pTSxFQUFFQyxNQUFNLENBQUNILEtBQUs7NENBQUMsS0FDcEVoTSxrQkFBa0I7Z0RBQUUsR0FBR0QsY0FBYztnREFBRUUsTUFBTWlNLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSzs0Q0FBQzt3Q0FFbEVsRCxhQUFZO3dDQUNaaUIsV0FBVTs7Ozs7Ozs7Ozs7OzBDQUdkLDhEQUFDRDs7a0RBQ0MsOERBQUMrQjt3Q0FBTTlCLFdBQVU7a0RBQWtFOzs7Ozs7a0RBQ25GLDhEQUFDb0Q7d0NBQ0NuQixPQUFPN0wscUJBQXFCQSxtQkFBbUJELFFBQVEsSUFBSSxLQUFLSCxlQUFlRyxRQUFRO3dDQUN2RitMLFVBQVUsQ0FBQ0MsSUFDVC9MLHFCQUNJQyxzQkFBc0I7Z0RBQUUsR0FBR0Qsa0JBQWtCO2dEQUFFRCxVQUFVZ00sRUFBRUMsTUFBTSxDQUFDSCxLQUFLOzRDQUFDLEtBQ3hFaE0sa0JBQWtCO2dEQUFFLEdBQUdELGNBQWM7Z0RBQUVHLFVBQVVnTSxFQUFFQyxNQUFNLENBQUNILEtBQUs7NENBQUM7d0NBRXRFakMsV0FBVTs7MERBRVYsOERBQUNxRDtnREFBT3BCLE9BQU07MERBQUc7Ozs7Ozs0Q0FDaEJ6SSxjQUFjSixHQUFHLENBQUMsQ0FBQ2pELHlCQUNsQiw4REFBQ2tOO29EQUFzQnBCLE9BQU85TDs4REFDM0JBO21EQURVQTs7Ozs7MERBSWYsOERBQUNrTjtnREFBT3BCLE9BQU07MERBQU87Ozs7Ozs7Ozs7Ozs7Ozs7Ozs0QkFHdkI3TCxDQUFBQSxDQUFBQSwrQkFBQUEseUNBQUFBLG1CQUFvQkQsUUFBUSxNQUFLLFVBQVVILGVBQWVHLFFBQVEsS0FBSyxNQUFLLG1CQUM1RSw4REFBQzRKO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQzhCO3dDQUFNOUIsV0FBVTtrREFBa0U7Ozs7OztrREFDbkYsOERBQUMrQjt3Q0FDQ0MsTUFBSzt3Q0FDTGpELGFBQVk7d0NBQ1ppQixXQUFVO3dDQUNWa0MsVUFBVSxDQUFDQyxJQUNUL0wscUJBQ0lDLHNCQUFzQjtnREFBRSxHQUFHRCxrQkFBa0I7Z0RBQUVELFVBQVVnTSxFQUFFQyxNQUFNLENBQUNILEtBQUs7NENBQUMsS0FDeEVoTSxrQkFBa0I7Z0RBQUUsR0FBR0QsY0FBYztnREFBRUcsVUFBVWdNLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSzs0Q0FBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQVFoRiw4REFBQ2xDO3dCQUFJQyxXQUFVOzs0QkFDWDVKLENBQUFBLHNCQUFzQkUseUJBQXdCLG1CQUM5Qyw4REFBQzJKO2dDQUNDQyxTQUFTO29DQUNQN0osc0JBQXNCO29DQUN0QkUsNkJBQTZCO2dDQUMvQjtnQ0FDQXlKLFdBQVU7MENBQ1g7Ozs7OzswQ0FJSCw4REFBQ0M7Z0NBQ0NDLFNBQ0U1Siw0QkFDSThHLGlDQUNBaEgscUJBQ0UrRywwQkFDQVY7Z0NBRVJ1RCxXQUFVO2dDQUNWdUMsVUFDRWpNLDRCQUNJLENBQUNBLDBCQUEwQkosSUFBSSxDQUFDd0csSUFBSSxNQUFNLENBQUNwRywwQkFBMEJILFFBQVEsR0FDN0VDLHFCQUNFLENBQUNBLG1CQUFtQkYsSUFBSSxDQUFDd0csSUFBSSxLQUM3QixDQUFDMUcsZUFBZUUsSUFBSSxDQUFDd0csSUFBSTswQ0FHaENwRyw0QkFBNEIsWUFBWUYscUJBQXFCLFNBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFNN0UsOERBQUMySjtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNpQjt3QkFBR2pCLFdBQVU7a0NBQXlEOzs7Ozs7a0NBQ3ZFLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ3RHO3dDQUFFc0csV0FBVTtrREFBdUQvSyxnQkFBZ0JvSCxNQUFNOzs7Ozs7a0RBQzFGLDhEQUFDM0M7d0NBQUVzRyxXQUFVO2tEQUEyQzs7Ozs7Ozs7Ozs7OzBDQUUxRCw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDdEc7d0NBQUVzRyxXQUFVO2tEQUF5RGxILDJCQUEyQnVELE1BQU07Ozs7OztrREFDdkcsOERBQUMzQzt3Q0FBRXNHLFdBQVU7a0RBQTJDOzs7Ozs7Ozs7Ozs7MENBRTFELDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUN0Rzt3Q0FBRXNHLFdBQVU7a0RBQ1ZsSCwyQkFBMkJDLE1BQU0sQ0FBQyxDQUFDQyxLQUFPQSxHQUFHc0IsUUFBUSxFQUFFK0IsTUFBTTs7Ozs7O2tEQUVoRSw4REFBQzNDO3dDQUFFc0csV0FBVTtrREFBMkM7Ozs7Ozs7Ozs7OzswQ0FFMUQsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ3RHO3dDQUFFc0csV0FBVTtrREFBMkQ5Ryx5QkFBeUJtRCxNQUFNOzs7Ozs7a0RBQ3ZHLDhEQUFDM0M7d0NBQUVzRyxXQUFVO2tEQUEyQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBTTdEOUksMENBQ0MsOERBQUN4Qyx5RkFBNEJBO2dCQUMzQnNILGFBQWE5RTtnQkFDYnBCLFlBQVlBO2dCQUNaWixxQkFBcUJBO2dCQUNyQkMsVUFBVUE7Z0JBQ1Z3UCxTQUFTLElBQU14Tiw0QkFBNEI7Ozs7Ozs7Ozs7OztBQUtyRDtHQTNuRGdCbkM7S0FBQUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcUEMtTFBLXFxEb2N1bWVudHNcXEhLVVlBXFxhdHRlbmRhbmNlXFxjb21wb25lbnRzXFxwYXJ0aWNpcGFudC1tYW5hZ2VtZW50LXBhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VNZW1vIH0gZnJvbSBcInJlYWN0XCJcbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcbmltcG9ydCB0eXBlIHsgUGFydGljaXBhbnQsIFNlc3Npb25QYXJ0aWNpcGFudCwgU2Vzc2lvbiwgQWN0aXZpdHksIEFjdGl2aXR5TGV2ZWxTZXR0aW5ncyB9IGZyb20gXCIuLi90eXBlc1wiXG5pbXBvcnQgeyBVbml2ZXJzYWxCdWxrSW1wb3J0IH0gZnJvbSBcIi4vdW5pdmVyc2FsLWJ1bGstaW1wb3J0XCJcbmltcG9ydCB7IFBhcnRpY2lwYW50QXR0ZW5kYW5jZUhpc3RvcnkgfSBmcm9tIFwiLi9wYXJ0aWNpcGFudC1hdHRlbmRhbmNlLWhpc3RvcnlcIlxuaW1wb3J0IHtcbiAgZ2V0QWN0aXZpdHlMZXZlbCxcbiAgZ2V0QWN0aXZpdHlMZXZlbFRleHQsXG4gIGdldEFjdGl2aXR5TGV2ZWxDb2xvcixcbiAgREVGQVVMVF9BQ1RJVklUWV9MRVZFTF9TRVRUSU5HUyxcbn0gZnJvbSBcIi4uL3V0aWxzL2FjdGl2aXR5LWxldmVsXCJcbmltcG9ydCB7IGNhbGN1bGF0ZVN0YXRpc3RpY3MgfSBmcm9tIFwiLi4vdXRpbHMvc3RhdGlzdGljc1wiXG5cbmludGVyZmFjZSBQYXJ0aWNpcGFudE1hbmFnZW1lbnRQYWdlUHJvcHMge1xuICBhbGxQYXJ0aWNpcGFudHM6IFBhcnRpY2lwYW50W11cbiAgc2Vzc2lvblBhcnRpY2lwYW50czogU2Vzc2lvblBhcnRpY2lwYW50W11cbiAgc2Vzc2lvbnM6IFNlc3Npb25bXVxuICBzZWxlY3RlZFNlc3Npb25JZDogc3RyaW5nIHwgbnVsbFxuICBvbkFkZFBhcnRpY2lwYW50OiAocGFydGljaXBhbnQ6IE9taXQ8UGFydGljaXBhbnQsIFwiaWRcIiB8IFwiYXR0ZW5kYW5jZVwiPikgPT4gdm9pZFxuICBvbkFkZFNlc3Npb25QYXJ0aWNpcGFudDogKHNlc3Npb25QYXJ0aWNpcGFudDogT21pdDxTZXNzaW9uUGFydGljaXBhbnQsIFwiaWRcIj4pID0+IHZvaWRcbiAgb25CdWxrQWRkUGFydGljaXBhbnRzOiAocGFydGljaXBhbnRzOiBPbWl0PFBhcnRpY2lwYW50LCBcImlkXCIgfCBcImF0dGVuZGFuY2VcIj5bXSkgPT4gdm9pZFxuICBvblVwZGF0ZVBhcnRpY2lwYW50OiAocGFydGljaXBhbnQ6IFBhcnRpY2lwYW50KSA9PiB2b2lkXG4gIG9uVXBkYXRlU2Vzc2lvblBhcnRpY2lwYW50OiAoc2Vzc2lvblBhcnRpY2lwYW50OiBTZXNzaW9uUGFydGljaXBhbnQpID0+IHZvaWRcbiAgb25EZWxldGVQYXJ0aWNpcGFudDogKHBhcnRpY2lwYW50SWQ6IHN0cmluZykgPT4gdm9pZFxuICBvblJlbW92ZUZyb21TZXNzaW9uOiAoc2Vzc2lvblBhcnRpY2lwYW50SWQ6IHN0cmluZykgPT4gdm9pZFxuICBvbkJ1bGtEZWxldGVUaXRsZTogKHRpdGxlOiBzdHJpbmcpID0+IHsgcGFydGljaXBhbnRzVXBkYXRlZDogbnVtYmVyOyBwbGFjZWhvbGRlcnNSZW1vdmVkOiBudW1iZXI7IHNlc3Npb25QYXJ0aWNpcGFudHNVcGRhdGVkOiBudW1iZXIgfVxuICBvbkJhY2s6ICgpID0+IHZvaWRcbiAgYWN0aXZpdGllczogQWN0aXZpdHlbXVxuICBhY3Rpdml0eUxldmVsU2V0dGluZ3M/OiBBY3Rpdml0eUxldmVsU2V0dGluZ3Ncbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFBhcnRpY2lwYW50TWFuYWdlbWVudFBhZ2Uoe1xuICBhbGxQYXJ0aWNpcGFudHMsXG4gIHNlc3Npb25QYXJ0aWNpcGFudHMsXG4gIHNlc3Npb25zLFxuICBzZWxlY3RlZFNlc3Npb25JZCxcbiAgb25BZGRQYXJ0aWNpcGFudCxcbiAgb25BZGRTZXNzaW9uUGFydGljaXBhbnQsXG4gIG9uQnVsa0FkZFBhcnRpY2lwYW50cyxcbiAgb25VcGRhdGVQYXJ0aWNpcGFudCxcbiAgb25VcGRhdGVTZXNzaW9uUGFydGljaXBhbnQsXG4gIG9uRGVsZXRlUGFydGljaXBhbnQsXG4gIG9uUmVtb3ZlRnJvbVNlc3Npb24sXG4gIG9uQnVsa0RlbGV0ZVRpdGxlLFxuICBvbkJhY2ssXG4gIGFjdGl2aXRpZXMsXG4gIGFjdGl2aXR5TGV2ZWxTZXR0aW5ncyA9IERFRkFVTFRfQUNUSVZJVFlfTEVWRUxfU0VUVElOR1MsXG59OiBQYXJ0aWNpcGFudE1hbmFnZW1lbnRQYWdlUHJvcHMpIHtcbiAgY29uc3QgW25ld1BhcnRpY2lwYW50LCBzZXROZXdQYXJ0aWNpcGFudF0gPSB1c2VTdGF0ZSh7XG4gICAgbmFtZTogXCJcIixcbiAgICBjYXRlZ29yeTogXCJcIixcbiAgfSlcbiAgY29uc3QgW2VkaXRpbmdQYXJ0aWNpcGFudCwgc2V0RWRpdGluZ1BhcnRpY2lwYW50XSA9IHVzZVN0YXRlPFBhcnRpY2lwYW50IHwgbnVsbD4obnVsbClcbiAgY29uc3QgW2VkaXRpbmdTZXNzaW9uUGFydGljaXBhbnQsIHNldEVkaXRpbmdTZXNzaW9uUGFydGljaXBhbnRdID0gdXNlU3RhdGU8U2Vzc2lvblBhcnRpY2lwYW50IHwgbnVsbD4obnVsbClcbiAgY29uc3QgW3NlYXJjaFRlcm0sIHNldFNlYXJjaFRlcm1dID0gdXNlU3RhdGUoXCJcIilcbiAgY29uc3QgW2NhdGVnb3J5RmlsdGVyLCBzZXRDYXRlZ29yeUZpbHRlcl0gPSB1c2VTdGF0ZShcIlwiKVxuICBjb25zdCBbc3RhdHVzRmlsdGVyLCBzZXRTdGF0dXNGaWx0ZXJdID0gdXNlU3RhdGU8XCJhbGxcIiB8IFwiYWN0aXZlXCIgfCBcImluYWN0aXZlXCI+KFwiYWxsXCIpXG4gIGNvbnN0IFtzaG93QnVsa0ltcG9ydCwgc2V0U2hvd0J1bGtJbXBvcnRdID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFt2aWV3TW9kZSwgc2V0Vmlld01vZGVdID0gdXNlU3RhdGU8XCJzZXNzaW9uXCIgfCBcImdsb2JhbFwiPihcInNlc3Npb25cIilcbiAgY29uc3QgW3ZpZXdpbmdBdHRlbmRhbmNlSGlzdG9yeSwgc2V0Vmlld2luZ0F0dGVuZGFuY2VIaXN0b3J5XSA9IHVzZVN0YXRlPFBhcnRpY2lwYW50IHwgbnVsbD4obnVsbClcblxuICAvLyDogbfpipznrqHnkIbnm7jpl5zni4DmhYtcbiAgY29uc3QgW3Nob3dUaXRsZU1hbmFnZW1lbnQsIHNldFNob3dUaXRsZU1hbmFnZW1lbnRdID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtlZGl0aW5nVGl0bGUsIHNldEVkaXRpbmdUaXRsZV0gPSB1c2VTdGF0ZTxzdHJpbmc+KFwiXCIpXG4gIGNvbnN0IFtuZXdUaXRsZSwgc2V0TmV3VGl0bGVdID0gdXNlU3RhdGUoXCJcIilcblxuICAvLyDmt7vliqDnj77mnInlj4PliqDogIXlhKrljJbnm7jpl5zni4DmhYtcbiAgY29uc3QgW3Nob3dBZGRFeGlzdGluZywgc2V0U2hvd0FkZEV4aXN0aW5nXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbYWRkRXhpc3RpbmdTZWFyY2hUZXJtLCBzZXRBZGRFeGlzdGluZ1NlYXJjaFRlcm1dID0gdXNlU3RhdGUoXCJcIilcbiAgY29uc3QgW2FkZEV4aXN0aW5nQ2F0ZWdvcnlGaWx0ZXIsIHNldEFkZEV4aXN0aW5nQ2F0ZWdvcnlGaWx0ZXJdID0gdXNlU3RhdGUoXCJcIilcbiAgY29uc3QgW3NlbGVjdGVkUGFydGljaXBhbnRzLCBzZXRTZWxlY3RlZFBhcnRpY2lwYW50c10gPSB1c2VTdGF0ZTxTZXQ8c3RyaW5nPj4obmV3IFNldCgpKVxuICBjb25zdCBbY3VycmVudFBhZ2UsIHNldEN1cnJlbnRQYWdlXSA9IHVzZVN0YXRlKDEpXG4gIGNvbnN0IFtwYWdlU2l6ZV0gPSB1c2VTdGF0ZSgyMClcblxuICBjb25zdCBbc29ydEZpZWxkLCBzZXRTb3J0RmllbGRdID0gdXNlU3RhdGU8c3RyaW5nPihcIlwiKVxuICBjb25zdCBbc29ydERpcmVjdGlvbiwgc2V0U29ydERpcmVjdGlvbl0gPSB1c2VTdGF0ZTxcImFzY1wiIHwgXCJkZXNjXCI+KFwiYXNjXCIpXG5cbiAgLy8g542y5Y+W55W25YmN5bGG5YilXG4gIGNvbnN0IGN1cnJlbnRTZXNzaW9uID0gc2Vzc2lvbnMuZmluZCgocykgPT4gcy5pZCA9PT0gc2VsZWN0ZWRTZXNzaW9uSWQpXG5cbiAgLy8g542y5Y+W55W25YmN5bGG5Yil55qE5Y+D5Yqg6ICFXG4gIGNvbnN0IGN1cnJlbnRTZXNzaW9uUGFydGljaXBhbnRzID0gdXNlTWVtbygoKSA9PiB7XG4gICAgcmV0dXJuIHNlc3Npb25QYXJ0aWNpcGFudHMuZmlsdGVyKChzcCkgPT4gc3Auc2Vzc2lvbklkID09PSBzZWxlY3RlZFNlc3Npb25JZClcbiAgfSwgW3Nlc3Npb25QYXJ0aWNpcGFudHMsIHNlbGVjdGVkU2Vzc2lvbklkXSlcblxuICAvLyDnjbLlj5bnlbbliY3lsYbliKXnmoTmiYDmnInogbfpipxcbiAgY29uc3QgY3VycmVudFNlc3Npb25DYXRlZ29yaWVzID0gdXNlTWVtbygoKSA9PiB7XG4gICAgY29uc3QgY2F0ZWdvcmllcyA9IG5ldyBTZXQoY3VycmVudFNlc3Npb25QYXJ0aWNpcGFudHMubWFwKChzcCkgPT4gc3AuY2F0ZWdvcnkpLmZpbHRlcihCb29sZWFuKSlcbiAgICByZXR1cm4gQXJyYXkuZnJvbShjYXRlZ29yaWVzKVxuICB9LCBbY3VycmVudFNlc3Npb25QYXJ0aWNpcGFudHNdKVxuXG4gIC8vIOeNsuWPluWFqOWxgOaJgOacieiBt+mKnFxuICBjb25zdCBhbGxDYXRlZ29yaWVzID0gdXNlTWVtbygoKSA9PiB7XG4gICAgY29uc3QgZ2xvYmFsQ2F0ZWdvcmllcyA9IG5ldyBTZXQoYWxsUGFydGljaXBhbnRzLm1hcCgocCkgPT4gcC5jYXRlZ29yeSkuZmlsdGVyKEJvb2xlYW4pKVxuICAgIGNvbnN0IHNlc3Npb25DYXRlZ29yaWVzID0gbmV3IFNldChzZXNzaW9uUGFydGljaXBhbnRzLm1hcCgoc3ApID0+IHNwLmNhdGVnb3J5KS5maWx0ZXIoQm9vbGVhbikpXG4gICAgcmV0dXJuIEFycmF5LmZyb20obmV3IFNldChbLi4uZ2xvYmFsQ2F0ZWdvcmllcywgLi4uc2Vzc2lvbkNhdGVnb3JpZXNdKSlcbiAgfSwgW2FsbFBhcnRpY2lwYW50cywgc2Vzc2lvblBhcnRpY2lwYW50c10pXG5cbiAgLy8g6KiI566X57Wx6KiI5pW45pOaXG4gIGNvbnN0IHN0YXRpc3RpY3MgPSB1c2VNZW1vKCgpID0+IHtcbiAgICByZXR1cm4gY2FsY3VsYXRlU3RhdGlzdGljcyhhY3Rpdml0aWVzLCBhbGxQYXJ0aWNpcGFudHMsIHNlc3Npb25zKVxuICB9LCBbYWN0aXZpdGllcywgYWxsUGFydGljaXBhbnRzLCBzZXNzaW9uc10pXG5cbiAgY29uc3QgaGFuZGxlU29ydCA9IChmaWVsZDogc3RyaW5nKSA9PiB7XG4gICAgaWYgKHNvcnRGaWVsZCA9PT0gZmllbGQpIHtcbiAgICAgIHNldFNvcnREaXJlY3Rpb24oc29ydERpcmVjdGlvbiA9PT0gXCJhc2NcIiA/IFwiZGVzY1wiIDogXCJhc2NcIilcbiAgICB9IGVsc2Uge1xuICAgICAgc2V0U29ydEZpZWxkKGZpZWxkKVxuICAgICAgc2V0U29ydERpcmVjdGlvbihcImFzY1wiKVxuICAgIH1cbiAgfVxuXG4gIC8vIOmBjua/vuWPg+WKoOiAhVxuICBjb25zdCBmaWx0ZXJlZFNlc3Npb25QYXJ0aWNpcGFudHMgPSB1c2VNZW1vKCgpID0+IHtcbiAgICBjb25zdCBmaWx0ZXJlZCA9IGN1cnJlbnRTZXNzaW9uUGFydGljaXBhbnRzLmZpbHRlcigoc3ApID0+IHtcbiAgICAgIGNvbnN0IG1hdGNoZXNTZWFyY2ggPSBzcC5uYW1lLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoVGVybS50b0xvd2VyQ2FzZSgpKVxuICAgICAgY29uc3QgbWF0Y2hlc0NhdGVnb3J5ID0gY2F0ZWdvcnlGaWx0ZXIgPyBzcC5jYXRlZ29yeSA9PT0gY2F0ZWdvcnlGaWx0ZXIgOiB0cnVlXG4gICAgICBjb25zdCBtYXRjaGVzU3RhdHVzID1cbiAgICAgICAgc3RhdHVzRmlsdGVyID09PSBcImFsbFwiIHx8XG4gICAgICAgIChzdGF0dXNGaWx0ZXIgPT09IFwiYWN0aXZlXCIgJiYgc3AuaXNBY3RpdmUpIHx8XG4gICAgICAgIChzdGF0dXNGaWx0ZXIgPT09IFwiaW5hY3RpdmVcIiAmJiAhc3AuaXNBY3RpdmUpXG4gICAgICByZXR1cm4gbWF0Y2hlc1NlYXJjaCAmJiBtYXRjaGVzQ2F0ZWdvcnkgJiYgbWF0Y2hlc1N0YXR1c1xuICAgIH0pXG5cbiAgICAvLyDoqIjnrpfmr4/lgIvlj4PliqDogIXnmoTlh7rluK3njoflkozmtLvouo3nrYnntJpcbiAgICBjb25zdCBwYXJ0aWNpcGFudHNXaXRoQWN0aXZpdHlMZXZlbCA9IGZpbHRlcmVkLm1hcCgoc3ApID0+IHtcbiAgICAgIC8vIOeNsuWPluipsuWPg+WKoOiAheWcqOeVtuWJjeWxhuWIpeeahOaJgOaciea0u+WLlVxuICAgICAgY29uc3QgcGFydGljaXBhbnRBY3Rpdml0aWVzID0gYWN0aXZpdGllcy5maWx0ZXIoKGFjdGl2aXR5KSA9PiBhY3Rpdml0eS5zZXNzaW9uSWQgPT09IHNlbGVjdGVkU2Vzc2lvbklkKVxuXG4gICAgICAvLyDoqIjnrpfoqbLlj4PliqDogIXlj4PoiIfnmoTmtLvli5Xmlbjph4/lkozlh7rluK3mlbjph49cbiAgICAgIGxldCB0b3RhbFBhcnRpY2lwYXRlZCA9IDBcbiAgICAgIGxldCB0b3RhbEF0dGVuZGVkID0gMFxuXG4gICAgICBwYXJ0aWNpcGFudEFjdGl2aXRpZXMuZm9yRWFjaCgoYWN0aXZpdHkpID0+IHtcbiAgICAgICAgLy8g5qqi5p+l6Kmy5Y+D5Yqg6ICF5piv5ZCm5Y+D6IiH5LqG6YCZ5YCL5rS75YuVXG4gICAgICAgIGNvbnN0IHBhcnRpY2lwYW50SW5BY3Rpdml0eSA9IGFjdGl2aXR5LnBhcnRpY2lwYW50cy5maW5kKChwKSA9PiBwLmlkID09PSBzcC5wYXJ0aWNpcGFudElkKVxuICAgICAgICBpZiAocGFydGljaXBhbnRJbkFjdGl2aXR5KSB7XG4gICAgICAgICAgdG90YWxQYXJ0aWNpcGF0ZWQrK1xuXG4gICAgICAgICAgLy8g5qqi5p+l5Ye65bit6KiY6YyEIC0g5ZiX6Kmm5aSa56iu5Y+v6IO955qE6Y21XG4gICAgICAgICAgY29uc3QgYXR0ZW5kYW5jZVJlY29yZCA9IHBhcnRpY2lwYW50SW5BY3Rpdml0eS5hdHRlbmRhbmNlXG4gICAgICAgICAgbGV0IGlzQXR0ZW5kZWQgPSBmYWxzZVxuXG4gICAgICAgICAgLy8g5ZiX6Kmm5LiN5ZCM55qE6Y215qC85byPXG4gICAgICAgICAgaWYgKGF0dGVuZGFuY2VSZWNvcmRbYWN0aXZpdHkuaWRdID09PSB0cnVlKSB7XG4gICAgICAgICAgICBpc0F0dGVuZGVkID0gdHJ1ZVxuICAgICAgICAgIH0gZWxzZSBpZiAoYXR0ZW5kYW5jZVJlY29yZFthY3Rpdml0eS5kYXRlXSA9PT0gdHJ1ZSkge1xuICAgICAgICAgICAgaXNBdHRlbmRlZCA9IHRydWVcbiAgICAgICAgICB9IGVsc2UgaWYgKGF0dGVuZGFuY2VSZWNvcmRbYCR7YWN0aXZpdHkuZGF0ZX1gXSA9PT0gdHJ1ZSkge1xuICAgICAgICAgICAgaXNBdHRlbmRlZCA9IHRydWVcbiAgICAgICAgICB9XG5cbiAgICAgICAgICBpZiAoaXNBdHRlbmRlZCkge1xuICAgICAgICAgICAgdG90YWxBdHRlbmRlZCsrXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9KVxuXG4gICAgICBjb25zdCBhdHRlbmRhbmNlUmF0ZSA9IHRvdGFsUGFydGljaXBhdGVkID4gMCA/ICh0b3RhbEF0dGVuZGVkIC8gdG90YWxQYXJ0aWNpcGF0ZWQpICogMTAwIDogMFxuICAgICAgY29uc3QgYWN0aXZpdHlMZXZlbCA9IGdldEFjdGl2aXR5TGV2ZWwoYXR0ZW5kYW5jZVJhdGUsIGFjdGl2aXR5TGV2ZWxTZXR0aW5ncylcblxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgLi4uc3AsXG4gICAgICAgIGF0dGVuZGFuY2VSYXRlLFxuICAgICAgICBhY3Rpdml0eUxldmVsLFxuICAgICAgICB0b3RhbFBhcnRpY2lwYXRlZCxcbiAgICAgICAgdG90YWxBdHRlbmRlZCxcbiAgICAgIH1cbiAgICB9KVxuXG4gICAgLy8g5o6S5bqP6YKP6LyvXG4gICAgaWYgKHNvcnRGaWVsZCkge1xuICAgICAgcGFydGljaXBhbnRzV2l0aEFjdGl2aXR5TGV2ZWwuc29ydCgoYSwgYikgPT4ge1xuICAgICAgICBsZXQgYVZhbHVlOiBhbnkgPSBcIlwiXG4gICAgICAgIGxldCBiVmFsdWU6IGFueSA9IFwiXCJcblxuICAgICAgICBzd2l0Y2ggKHNvcnRGaWVsZCkge1xuICAgICAgICAgIGNhc2UgXCJuYW1lXCI6XG4gICAgICAgICAgICBhVmFsdWUgPSBhLm5hbWUudG9Mb3dlckNhc2UoKVxuICAgICAgICAgICAgYlZhbHVlID0gYi5uYW1lLnRvTG93ZXJDYXNlKClcbiAgICAgICAgICAgIGJyZWFrXG4gICAgICAgICAgY2FzZSBcImNhdGVnb3J5XCI6XG4gICAgICAgICAgICBhVmFsdWUgPSBhLmNhdGVnb3J5LnRvTG93ZXJDYXNlKClcbiAgICAgICAgICAgIGJWYWx1ZSA9IGIuY2F0ZWdvcnkudG9Mb3dlckNhc2UoKVxuICAgICAgICAgICAgYnJlYWtcbiAgICAgICAgICBjYXNlIFwiam9pbkRhdGVcIjpcbiAgICAgICAgICAgIGFWYWx1ZSA9IGEuam9pbkRhdGUgfHwgXCJcIlxuICAgICAgICAgICAgYlZhbHVlID0gYi5qb2luRGF0ZSB8fCBcIlwiXG4gICAgICAgICAgICBicmVha1xuICAgICAgICAgIGNhc2UgXCJzdGF0dXNcIjpcbiAgICAgICAgICAgIGFWYWx1ZSA9IGEuaXNBY3RpdmUgPyBcImFjdGl2ZVwiIDogXCJpbmFjdGl2ZVwiXG4gICAgICAgICAgICBiVmFsdWUgPSBiLmlzQWN0aXZlID8gXCJhY3RpdmVcIiA6IFwiaW5hY3RpdmVcIlxuICAgICAgICAgICAgYnJlYWtcbiAgICAgICAgICBjYXNlIFwiYXR0ZW5kYW5jZVJhdGVcIjpcbiAgICAgICAgICAgIGFWYWx1ZSA9IGEuYXR0ZW5kYW5jZVJhdGVcbiAgICAgICAgICAgIGJWYWx1ZSA9IGIuYXR0ZW5kYW5jZVJhdGVcbiAgICAgICAgICAgIGJyZWFrXG4gICAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgIHJldHVybiAwXG4gICAgICAgIH1cblxuICAgICAgICBpZiAoYVZhbHVlIDwgYlZhbHVlKSByZXR1cm4gc29ydERpcmVjdGlvbiA9PT0gXCJhc2NcIiA/IC0xIDogMVxuICAgICAgICBpZiAoYVZhbHVlID4gYlZhbHVlKSByZXR1cm4gc29ydERpcmVjdGlvbiA9PT0gXCJhc2NcIiA/IDEgOiAtMVxuICAgICAgICByZXR1cm4gMFxuICAgICAgfSlcbiAgICB9XG5cbiAgICByZXR1cm4gcGFydGljaXBhbnRzV2l0aEFjdGl2aXR5TGV2ZWxcbiAgfSwgW1xuICAgIGN1cnJlbnRTZXNzaW9uUGFydGljaXBhbnRzLFxuICAgIHNlYXJjaFRlcm0sXG4gICAgY2F0ZWdvcnlGaWx0ZXIsXG4gICAgc3RhdHVzRmlsdGVyLFxuICAgIHNvcnRGaWVsZCxcbiAgICBzb3J0RGlyZWN0aW9uLFxuICAgIGFjdGl2aXRpZXMsXG4gICAgc2VsZWN0ZWRTZXNzaW9uSWQsXG4gICAgYWN0aXZpdHlMZXZlbFNldHRpbmdzLFxuICBdKVxuXG4gIC8vIOeNsuWPluS4jeWcqOeVtuWJjeWxhuWIpeeahOWFqOWxgOWPg+WKoOiAhe+8iOWEquWMlueJiO+8iVxuICBjb25zdCBhdmFpbGFibGVHbG9iYWxQYXJ0aWNpcGFudHMgPSB1c2VNZW1vKCgpID0+IHtcbiAgICBjb25zdCBzZXNzaW9uUGFydGljaXBhbnRJZHMgPSBuZXcgU2V0KGN1cnJlbnRTZXNzaW9uUGFydGljaXBhbnRzLm1hcCgoc3ApID0+IHNwLnBhcnRpY2lwYW50SWQpKVxuICAgIHJldHVybiBhbGxQYXJ0aWNpcGFudHMuZmlsdGVyKChwKSA9PiAhc2Vzc2lvblBhcnRpY2lwYW50SWRzLmhhcyhwLmlkKSlcbiAgfSwgW2FsbFBhcnRpY2lwYW50cywgY3VycmVudFNlc3Npb25QYXJ0aWNpcGFudHNdKVxuXG4gIC8vIOmBjua/vuWSjOWIhumggeeahOWPr+eUqOWPg+WKoOiAhVxuICBjb25zdCBmaWx0ZXJlZEF2YWlsYWJsZVBhcnRpY2lwYW50cyA9IHVzZU1lbW8oKCkgPT4ge1xuICAgIGNvbnN0IGZpbHRlcmVkID0gYXZhaWxhYmxlR2xvYmFsUGFydGljaXBhbnRzLmZpbHRlcigocGFydGljaXBhbnQpID0+IHtcbiAgICAgIGNvbnN0IG1hdGNoZXNTZWFyY2ggPSBwYXJ0aWNpcGFudC5uYW1lLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoYWRkRXhpc3RpbmdTZWFyY2hUZXJtLnRvTG93ZXJDYXNlKCkpXG4gICAgICBjb25zdCBtYXRjaGVzQ2F0ZWdvcnkgPSBhZGRFeGlzdGluZ0NhdGVnb3J5RmlsdGVyID8gcGFydGljaXBhbnQuY2F0ZWdvcnkgPT09IGFkZEV4aXN0aW5nQ2F0ZWdvcnlGaWx0ZXIgOiB0cnVlXG4gICAgICByZXR1cm4gbWF0Y2hlc1NlYXJjaCAmJiBtYXRjaGVzQ2F0ZWdvcnlcbiAgICB9KVxuXG4gICAgY29uc3Qgc3RhcnRJbmRleCA9IChjdXJyZW50UGFnZSAtIDEpICogcGFnZVNpemVcbiAgICBjb25zdCBlbmRJbmRleCA9IHN0YXJ0SW5kZXggKyBwYWdlU2l6ZVxuICAgIHJldHVybiB7XG4gICAgICBwYXJ0aWNpcGFudHM6IGZpbHRlcmVkLnNsaWNlKHN0YXJ0SW5kZXgsIGVuZEluZGV4KSxcbiAgICAgIHRvdGFsOiBmaWx0ZXJlZC5sZW5ndGgsXG4gICAgICB0b3RhbFBhZ2VzOiBNYXRoLmNlaWwoZmlsdGVyZWQubGVuZ3RoIC8gcGFnZVNpemUpLFxuICAgIH1cbiAgfSwgW2F2YWlsYWJsZUdsb2JhbFBhcnRpY2lwYW50cywgYWRkRXhpc3RpbmdTZWFyY2hUZXJtLCBhZGRFeGlzdGluZ0NhdGVnb3J5RmlsdGVyLCBjdXJyZW50UGFnZSwgcGFnZVNpemVdKVxuXG4gIC8vIOiZleeQhuaWsOWinuWPg+WKoOiAhVxuICBjb25zdCBoYW5kbGVBZGRQYXJ0aWNpcGFudCA9ICgpID0+IHtcbiAgICBpZiAobmV3UGFydGljaXBhbnQubmFtZS50cmltKCkpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIC8vIOiZleeQhuaWsOWinuiBt+mKnOeahOaDheazgVxuICAgICAgICBsZXQgZmluYWxDYXRlZ29yeSA9IG5ld1BhcnRpY2lwYW50LmNhdGVnb3J5XG4gICAgICAgIGlmIChmaW5hbENhdGVnb3J5ID09PSBcIuaWsOWinuiBt+mKnFwiKSB7XG4gICAgICAgICAgZmluYWxDYXRlZ29yeSA9IFwiXCIgLy8g5aaC5p6c6YG45pOH5LqG5paw5aKe6IG36Yqc5L2G5rKS5pyJ6Ly45YWl5YW36auU6IG36Yqc77yM6Kit54K656m6XG4gICAgICAgIH1cblxuICAgICAgICBjb25zdCBwYXJ0aWNpcGFudFRvQWRkID0ge1xuICAgICAgICAgIG5hbWU6IG5ld1BhcnRpY2lwYW50Lm5hbWUudHJpbSgpLFxuICAgICAgICAgIGNhdGVnb3J5OiBmaW5hbENhdGVnb3J5XG4gICAgICAgIH1cblxuICAgICAgICBjb25zb2xlLmxvZygnQWRkaW5nIHBhcnRpY2lwYW50OicsIHBhcnRpY2lwYW50VG9BZGQpIC8vIOiqv+ippuaXpeiqjFxuICAgICAgICBvbkFkZFBhcnRpY2lwYW50KHBhcnRpY2lwYW50VG9BZGQpXG4gICAgICAgIHNldE5ld1BhcnRpY2lwYW50KHsgbmFtZTogXCJcIiwgY2F0ZWdvcnk6IFwiXCIgfSlcblxuICAgICAgICAvLyDpoa/npLrmiJDlip/mj5DnpLpcbiAgICAgICAgYWxlcnQoYOaIkOWKn+aWsOWinuWPg+WKoOiAheOAjCR7cGFydGljaXBhbnRUb0FkZC5uYW1lfeOAjWApXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfmlrDlop7lj4PliqDogIXmmYLnmbznlJ/pjK/oqqQ6JywgZXJyb3IpXG4gICAgICAgIGFsZXJ0KGDmlrDlop7lj4PliqDogIXmmYLnmbznlJ/pjK/oqqQ6ICR7ZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAn5pyq55+l6Yyv6KqkJ31gKVxuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIC8vIOiZleeQhuabtOaWsOWPg+WKoOiAhVxuICBjb25zdCBoYW5kbGVVcGRhdGVQYXJ0aWNpcGFudCA9ICgpID0+IHtcbiAgICBpZiAoZWRpdGluZ1BhcnRpY2lwYW50KSB7XG4gICAgICBvblVwZGF0ZVBhcnRpY2lwYW50KGVkaXRpbmdQYXJ0aWNpcGFudClcbiAgICAgIHNldEVkaXRpbmdQYXJ0aWNpcGFudChudWxsKVxuICAgIH1cbiAgfVxuXG4gIC8vIOiZleeQhuabtOaWsOWxhuWIpeWPg+WKoOiAhVxuICBjb25zdCBoYW5kbGVVcGRhdGVTZXNzaW9uUGFydGljaXBhbnQgPSAoKSA9PiB7XG4gICAgaWYgKGVkaXRpbmdTZXNzaW9uUGFydGljaXBhbnQpIHtcbiAgICAgIG9uVXBkYXRlU2Vzc2lvblBhcnRpY2lwYW50KGVkaXRpbmdTZXNzaW9uUGFydGljaXBhbnQpXG4gICAgICBzZXRFZGl0aW5nU2Vzc2lvblBhcnRpY2lwYW50KG51bGwpXG4gICAgfVxuICB9XG5cbiAgLy8g6JmV55CG5om56YeP5bCO5YWlXG4gIGNvbnN0IGhhbmRsZUJ1bGtJbXBvcnQgPSAocGFydGljaXBhbnRzOiBPbWl0PFBhcnRpY2lwYW50LCBcImlkXCIgfCBcImF0dGVuZGFuY2VcIj5bXSkgPT4ge1xuICAgIG9uQnVsa0FkZFBhcnRpY2lwYW50cyhwYXJ0aWNpcGFudHMpXG4gICAgc2V0U2hvd0J1bGtJbXBvcnQoZmFsc2UpXG4gIH1cblxuICAvLyDomZXnkIbmt7vliqDnj77mnInlj4PliqDogIXliLDnlbbliY3lsYbliKVcbiAgY29uc3QgaGFuZGxlQWRkVG9TZXNzaW9uID0gKHBhcnRpY2lwYW50OiBQYXJ0aWNpcGFudCwgY2F0ZWdvcnk6IHN0cmluZykgPT4ge1xuICAgIGlmIChzZWxlY3RlZFNlc3Npb25JZCkge1xuICAgICAgb25BZGRTZXNzaW9uUGFydGljaXBhbnQoe1xuICAgICAgICBwYXJ0aWNpcGFudElkOiBwYXJ0aWNpcGFudC5pZCxcbiAgICAgICAgc2Vzc2lvbklkOiBzZWxlY3RlZFNlc3Npb25JZCxcbiAgICAgICAgbmFtZTogcGFydGljaXBhbnQubmFtZSxcbiAgICAgICAgY2F0ZWdvcnk6IGNhdGVnb3J5LFxuICAgICAgICBqb2luRGF0ZTogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNwbGl0KFwiVFwiKVswXSxcbiAgICAgICAgaXNBY3RpdmU6IHRydWUsXG4gICAgICB9KVxuICAgIH1cbiAgfVxuXG4gIC8vIOaJuemHj+a3u+WKoOmBuOS4reeahOWPg+WKoOiAhVxuICBjb25zdCBoYW5kbGVCYXRjaEFkZFRvU2Vzc2lvbiA9ICgpID0+IHtcbiAgICBpZiAoc2VsZWN0ZWRTZXNzaW9uSWQgJiYgc2VsZWN0ZWRQYXJ0aWNpcGFudHMuc2l6ZSA+IDApIHtcbiAgICAgIHNlbGVjdGVkUGFydGljaXBhbnRzLmZvckVhY2goKHBhcnRpY2lwYW50SWQpID0+IHtcbiAgICAgICAgY29uc3QgcGFydGljaXBhbnQgPSBhbGxQYXJ0aWNpcGFudHMuZmluZCgocCkgPT4gcC5pZCA9PT0gcGFydGljaXBhbnRJZClcbiAgICAgICAgaWYgKHBhcnRpY2lwYW50KSB7XG4gICAgICAgICAgb25BZGRTZXNzaW9uUGFydGljaXBhbnQoe1xuICAgICAgICAgICAgcGFydGljaXBhbnRJZDogcGFydGljaXBhbnQuaWQsXG4gICAgICAgICAgICBzZXNzaW9uSWQ6IHNlbGVjdGVkU2Vzc2lvbklkLFxuICAgICAgICAgICAgbmFtZTogcGFydGljaXBhbnQubmFtZSxcbiAgICAgICAgICAgIGNhdGVnb3J5OiBwYXJ0aWNpcGFudC5jYXRlZ29yeSB8fCBcIlwiLFxuICAgICAgICAgICAgam9pbkRhdGU6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zcGxpdChcIlRcIilbMF0sXG4gICAgICAgICAgICBpc0FjdGl2ZTogdHJ1ZSxcbiAgICAgICAgICB9KVxuICAgICAgICB9XG4gICAgICB9KVxuICAgICAgc2V0U2VsZWN0ZWRQYXJ0aWNpcGFudHMobmV3IFNldCgpKVxuICAgICAgc2V0U2hvd0FkZEV4aXN0aW5nKGZhbHNlKVxuICAgIH1cbiAgfVxuXG4gIC8vIOiZleeQhuiBt+mKnOe3qOi8r1xuICBjb25zdCBoYW5kbGVVcGRhdGVUaXRsZSA9IChvbGRUaXRsZTogc3RyaW5nLCBuZXdUaXRsZTogc3RyaW5nKSA9PiB7XG4gICAgaWYgKG5ld1RpdGxlLnRyaW0oKSAmJiBuZXdUaXRsZSAhPT0gb2xkVGl0bGUpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIC8vIOaqouafpeaWsOiBt+mKnOaYr+WQpuW3suWtmOWcqO+8iOS4jeWNgOWIhuWkp+Wwj+Wvq++8iVxuICAgICAgICBpZiAoYWxsQ2F0ZWdvcmllcy5zb21lKGNhdGVnb3J5ID0+XG4gICAgICAgICAgY2F0ZWdvcnkudG9Mb3dlckNhc2UoKSA9PT0gbmV3VGl0bGUudHJpbSgpLnRvTG93ZXJDYXNlKCkgJiZcbiAgICAgICAgICBjYXRlZ29yeS50b0xvd2VyQ2FzZSgpICE9PSBvbGRUaXRsZS50b0xvd2VyQ2FzZSgpXG4gICAgICAgICkpIHtcbiAgICAgICAgICBhbGVydChg6IG36Yqc44CMJHtuZXdUaXRsZS50cmltKCl944CN5bey5a2Y5ZyoYClcbiAgICAgICAgICBzZXRFZGl0aW5nVGl0bGUoXCJcIilcbiAgICAgICAgICByZXR1cm5cbiAgICAgICAgfVxuXG4gICAgICAgIC8vIOaJvuWHuuaJgOacieS9v+eUqOipsuiBt+mKnOeahOWPg+WKoOiAhVxuICAgICAgICBjb25zdCBwYXJ0aWNpcGFudHNUb1VwZGF0ZSA9IGFsbFBhcnRpY2lwYW50cy5maWx0ZXIocCA9PlxuICAgICAgICAgIHAuY2F0ZWdvcnkgPT09IG9sZFRpdGxlICYmXG4gICAgICAgICAgIXAubmFtZS5zdGFydHNXaXRoKCfogbfpipzkvZTkvY3nrKYtJykgLy8g5o6S6Zmk5L2U5L2N56ym5Y+D5Yqg6ICFXG4gICAgICAgIClcblxuICAgICAgICAvLyDmib7lh7rkvZTkvY3nrKblj4PliqDogIXvvIjlpoLmnpzmnInvvIlcbiAgICAgICAgY29uc3QgcGxhY2Vob2xkZXJzID0gYWxsUGFydGljaXBhbnRzLmZpbHRlcihwID0+XG4gICAgICAgICAgcC5jYXRlZ29yeSA9PT0gb2xkVGl0bGUgJiZcbiAgICAgICAgICBwLm5hbWUuc3RhcnRzV2l0aCgn6IG36Yqc5L2U5L2N56ymLScpXG4gICAgICAgIClcblxuICAgICAgICAvLyDmib7lh7rmiYDmnInkvb/nlKjoqbLogbfpipznmoTlsYbliKXlj4PliqDogIVcbiAgICAgICAgY29uc3Qgc2Vzc2lvblBhcnRpY2lwYW50c1RvVXBkYXRlID0gc2Vzc2lvblBhcnRpY2lwYW50cy5maWx0ZXIoc3AgPT5cbiAgICAgICAgICBzcC5jYXRlZ29yeSA9PT0gb2xkVGl0bGVcbiAgICAgICAgKVxuXG4gICAgICAgIC8vIOmhr+ekuumAsuW6puaPkOekulxuICAgICAgICBjb25zdCB0b3RhbFVwZGF0ZXMgPSBwYXJ0aWNpcGFudHNUb1VwZGF0ZS5sZW5ndGhcbiAgICAgICAgY29uc3QgaGFzUGxhY2Vob2xkZXJzID0gcGxhY2Vob2xkZXJzLmxlbmd0aCA+IDBcblxuICAgICAgICAvLyDnorroqo3mmK/lkKbnubznuoxcbiAgICAgICAgaWYgKHRvdGFsVXBkYXRlcyA+IDAgfHwgaGFzUGxhY2Vob2xkZXJzIHx8IHNlc3Npb25QYXJ0aWNpcGFudHNUb1VwZGF0ZS5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgaWYgKCF3aW5kb3cuY29uZmlybShg5bCH5pu05paw6IG36Yqc5b6e44CMJHtvbGRUaXRsZX3jgI3liLDjgIwke25ld1RpdGxlfeOAje+8jCR7dG90YWxVcGRhdGVzID4gMCA/IGDlvbHpn78gJHt0b3RhbFVwZGF0ZXN9IOS9jeWPg+WKoOiAhWAgOiAnJ33jgIJcXG5cXG7norrlrprnubznuozll47vvJ9gKSkge1xuICAgICAgICAgICAgc2V0RWRpdGluZ1RpdGxlKFwiXCIpXG4gICAgICAgICAgICByZXR1cm5cbiAgICAgICAgICB9XG5cbiAgICAgICAgICAvLyDmibnph4/mm7TmlrDlhajlsYDlj4PliqDogIVcbiAgICAgICAgICBpZiAocGFydGljaXBhbnRzVG9VcGRhdGUubGVuZ3RoID4gMCkge1xuICAgICAgICAgICAgY29uc3QgYmF0Y2hVcGRhdGVQcm9taXNlcyA9IHBhcnRpY2lwYW50c1RvVXBkYXRlLm1hcChwYXJ0aWNpcGFudCA9PiB7XG4gICAgICAgICAgICAgIGNvbnN0IHVwZGF0ZWRQYXJ0aWNpcGFudCA9IHsgLi4ucGFydGljaXBhbnQsIGNhdGVnb3J5OiBuZXdUaXRsZSB9XG4gICAgICAgICAgICAgIHJldHVybiBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHtcbiAgICAgICAgICAgICAgICBvblVwZGF0ZVBhcnRpY2lwYW50KHVwZGF0ZWRQYXJ0aWNpcGFudClcbiAgICAgICAgICAgICAgICByZXNvbHZlKG51bGwpXG4gICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICB9KVxuXG4gICAgICAgICAgICBQcm9taXNlLmFsbChiYXRjaFVwZGF0ZVByb21pc2VzKS5jYXRjaChlcnIgPT4ge1xuICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCfmibnph4/mm7TmlrDlj4PliqDogIXmmYLnmbznlJ/pjK/oqqQ6JywgZXJyKVxuICAgICAgICAgICAgfSlcbiAgICAgICAgICB9XG5cbiAgICAgICAgICAvLyDmibnph4/mm7TmlrDkvZTkvY3nrKblj4PliqDogIVcbiAgICAgICAgICBpZiAocGxhY2Vob2xkZXJzLmxlbmd0aCA+IDApIHtcbiAgICAgICAgICAgIGNvbnN0IGJhdGNoVXBkYXRlUGxhY2Vob2xkZXJQcm9taXNlcyA9IHBsYWNlaG9sZGVycy5tYXAocGxhY2Vob2xkZXIgPT4ge1xuICAgICAgICAgICAgICBjb25zdCB1cGRhdGVkUGxhY2Vob2xkZXIgPSB7XG4gICAgICAgICAgICAgICAgLi4ucGxhY2Vob2xkZXIsXG4gICAgICAgICAgICAgICAgbmFtZTogYOiBt+mKnOS9lOS9jeespi0ke25ld1RpdGxlfWAsXG4gICAgICAgICAgICAgICAgY2F0ZWdvcnk6IG5ld1RpdGxlXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgcmV0dXJuIG5ldyBQcm9taXNlKHJlc29sdmUgPT4ge1xuICAgICAgICAgICAgICAgIG9uVXBkYXRlUGFydGljaXBhbnQodXBkYXRlZFBsYWNlaG9sZGVyKVxuICAgICAgICAgICAgICAgIHJlc29sdmUobnVsbClcbiAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgIH0pXG5cbiAgICAgICAgICAgIFByb21pc2UuYWxsKGJhdGNoVXBkYXRlUGxhY2Vob2xkZXJQcm9taXNlcykuY2F0Y2goZXJyID0+IHtcbiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcign5om56YeP5pu05paw5L2U5L2N56ym5pmC55m855Sf6Yyv6KqkOicsIGVycilcbiAgICAgICAgICAgIH0pXG4gICAgICAgICAgfVxuXG4gICAgICAgICAgLy8g5om56YeP5pu05paw5bGG5Yil5Y+D5Yqg6ICFXG4gICAgICAgICAgaWYgKHNlc3Npb25QYXJ0aWNpcGFudHNUb1VwZGF0ZS5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgICBjb25zdCBiYXRjaFVwZGF0ZVNlc3Npb25Qcm9taXNlcyA9IHNlc3Npb25QYXJ0aWNpcGFudHNUb1VwZGF0ZS5tYXAoc2Vzc2lvblBhcnRpY2lwYW50ID0+IHtcbiAgICAgICAgICAgICAgaWYgKCdjYXRlZ29yeScgaW4gc2Vzc2lvblBhcnRpY2lwYW50KSB7XG4gICAgICAgICAgICAgICAgY29uc3QgdXBkYXRlZFNlc3Npb25QYXJ0aWNpcGFudCA9IHsgLi4uc2Vzc2lvblBhcnRpY2lwYW50LCBjYXRlZ29yeTogbmV3VGl0bGUgfVxuICAgICAgICAgICAgICAgIHJldHVybiBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHtcbiAgICAgICAgICAgICAgICAgIG9uVXBkYXRlU2Vzc2lvblBhcnRpY2lwYW50KHVwZGF0ZWRTZXNzaW9uUGFydGljaXBhbnQpXG4gICAgICAgICAgICAgICAgICByZXNvbHZlKG51bGwpXG4gICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICByZXR1cm4gUHJvbWlzZS5yZXNvbHZlKG51bGwpXG4gICAgICAgICAgICB9KVxuXG4gICAgICAgICAgICBQcm9taXNlLmFsbChiYXRjaFVwZGF0ZVNlc3Npb25Qcm9taXNlcykuY2F0Y2goZXJyID0+IHtcbiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcign5om56YeP5pu05paw5bGG5Yil5Y+D5Yqg6ICF5pmC55m855Sf6Yyv6KqkOicsIGVycilcbiAgICAgICAgICAgIH0pXG4gICAgICAgICAgfVxuXG4gICAgICAgICAgLy8g6aGv56S65a6M5oiQ5o+Q56S6XG4gICAgICAgICAgaWYgKHRvdGFsVXBkYXRlcyA+IDApIHtcbiAgICAgICAgICAgIGFsZXJ0KGDmiJDlip/mm7TmlrAgJHt0b3RhbFVwZGF0ZXN9IOS9jeWPg+WKoOiAheeahOiBt+mKnOW+nuOAjCR7b2xkVGl0bGV944CN5Yiw44CMJHtuZXdUaXRsZX3jgI1gKVxuICAgICAgICAgIH0gZWxzZSBpZiAoaGFzUGxhY2Vob2xkZXJzKSB7XG4gICAgICAgICAgICBhbGVydChg5oiQ5Yqf5pu05paw6IG36Yqc44CMJHtvbGRUaXRsZX3jgI3ngrrjgIwke25ld1RpdGxlfeOAjWApXG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIGFsZXJ0KGDmiJDlip/mm7TmlrDogbfpipzjgIwke29sZFRpdGxlfeOAjeeCuuOAjCR7bmV3VGl0bGV944CN77yM5rKS5pyJ5Y+D5Yqg6ICF5L2/55So5q2k6IG36YqcYClcbiAgICAgICAgICB9XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgYWxlcnQoYOaykuacieaJvuWIsOS9v+eUqOOAjCR7b2xkVGl0bGV944CN6IG36Yqc55qE5Y+D5Yqg6ICFYClcbiAgICAgICAgfVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign5pu05paw6IG36Yqc5pmC55m855Sf6Yyv6KqkOicsIGVycm9yKVxuICAgICAgICBhbGVydChg5pu05paw6IG36Yqc5pmC55m855Sf6Yyv6KqkOiAke2Vycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ+acquefpemMr+iqpCd9YClcbiAgICAgIH1cbiAgICB9XG4gICAgc2V0RWRpdGluZ1RpdGxlKFwiXCIpXG4gIH1cblxuICAvLyDomZXnkIbogbfpipzliKrpmaRcbiAgY29uc3QgaGFuZGxlRGVsZXRlVGl0bGUgPSAodGl0bGU6IHN0cmluZykgPT4ge1xuICAgIHRyeSB7XG4gICAgICAvLyDmib7lh7rmiYDmnInkvb/nlKjoqbLogbfpipznmoTlr6bpmpvlj4PliqDogIXvvIjmjpLpmaTkvZTkvY3nrKbvvIlcbiAgICAgIGNvbnN0IHBhcnRpY2lwYW50c1RvVXBkYXRlID0gYWxsUGFydGljaXBhbnRzLmZpbHRlcihwID0+XG4gICAgICAgIHAuY2F0ZWdvcnkgPT09IHRpdGxlICYmXG4gICAgICAgICFwLm5hbWUuc3RhcnRzV2l0aCgn6IG36Yqc5L2U5L2N56ymLScpXG4gICAgICApXG5cbiAgICAgIC8vIOaJvuWHuuS9v+eUqOipsuiBt+mKnOeahOS9lOS9jeespuWPg+WKoOiAhVxuICAgICAgY29uc3QgcGxhY2Vob2xkZXJzID0gYWxsUGFydGljaXBhbnRzLmZpbHRlcihwID0+XG4gICAgICAgIHAuY2F0ZWdvcnkgPT09IHRpdGxlICYmXG4gICAgICAgIHAubmFtZS5zdGFydHNXaXRoKCfogbfpipzkvZTkvY3nrKYtJylcbiAgICAgIClcblxuICAgICAgLy8g5om+5Ye65omA5pyJ5L2/55So6Kmy6IG36Yqc55qE5bGG5Yil5Y+D5Yqg6ICFXG4gICAgICBjb25zdCBzZXNzaW9uUGFydGljaXBhbnRzVG9VcGRhdGUgPSBzZXNzaW9uUGFydGljaXBhbnRzLmZpbHRlcihzcCA9PlxuICAgICAgICBzcC5jYXRlZ29yeSA9PT0gdGl0bGVcbiAgICAgIClcblxuICAgICAgY29uc3QgdG90YWxVcGRhdGVzID0gcGFydGljaXBhbnRzVG9VcGRhdGUubGVuZ3RoXG4gICAgICBjb25zdCBoYXNQbGFjZWhvbGRlcnMgPSBwbGFjZWhvbGRlcnMubGVuZ3RoID4gMFxuXG4gICAgICBsZXQgY29uZmlybU1lc3NhZ2UgPSBg56K65a6a6KaB5Yiq6Zmk6IG36YqcIFwiJHt0aXRsZX1cIiDll47vvJ9gXG5cbiAgICAgIGlmICh0b3RhbFVwZGF0ZXMgPiAwKSB7XG4gICAgICAgIGNvbmZpcm1NZXNzYWdlICs9IGBcXG5cXG7pgJnlsIfmnIPvvJpgXG4gICAgICAgIGNvbmZpcm1NZXNzYWdlICs9IGBcXG7igKIg5riF6ZmkICR7dG90YWxVcGRhdGVzfSDkvY3miJDlk6HnmoTogbfpipxgXG4gICAgICAgIGNvbmZpcm1NZXNzYWdlICs9IGBcXG7igKIg5bCH5LuW5YCR55qE6IG36Yqc6Kit54K656m655m9YFxuICAgICAgfVxuXG4gICAgICBpZiAoaGFzUGxhY2Vob2xkZXJzKSB7XG4gICAgICAgIGNvbmZpcm1NZXNzYWdlICs9IHRvdGFsVXBkYXRlcyA+IDAgPyBgXFxu4oCiIOenu+mZpOiBt+mKnOS9lOS9jeespmAgOiBgXFxuXFxu6YCZ5bCH56e76Zmk6IG36Yqc5L2U5L2N56ym44CCYFxuICAgICAgfVxuXG4gICAgICBpZiAodG90YWxVcGRhdGVzID09PSAwICYmICFoYXNQbGFjZWhvbGRlcnMpIHtcbiAgICAgICAgY29uZmlybU1lc3NhZ2UgKz0gYFxcblxcbuaykuacieaIkOWToeS9v+eUqOatpOiBt+mKnO+8jOWwh+ebtOaOpeenu+mZpOOAgmBcbiAgICAgIH1cblxuICAgICAgaWYgKHdpbmRvdy5jb25maXJtKGNvbmZpcm1NZXNzYWdlKSkge1xuICAgICAgICB0cnkge1xuICAgICAgICAgIC8vIOS9v+eUqOaWsOeahOaJuemHj+WIqumZpOWHveaVuFxuICAgICAgICAgIGNvbnN0IHJlc3VsdCA9IG9uQnVsa0RlbGV0ZVRpdGxlKHRpdGxlKVxuXG4gICAgICAgICAgLy8g6aGv56S65a6M5oiQ5o+Q56S6XG4gICAgICAgICAgbGV0IHN1Y2Nlc3NNZXNzYWdlID0gYOaIkOWKn+WIqumZpOiBt+mKnCBcIiR7dGl0bGV9XCJgXG5cbiAgICAgICAgICBpZiAocmVzdWx0LnBhcnRpY2lwYW50c1VwZGF0ZWQgPiAwKSB7XG4gICAgICAgICAgICBzdWNjZXNzTWVzc2FnZSArPSBgXFxuXFxu5bey5a6M5oiQ77yaYFxuICAgICAgICAgICAgc3VjY2Vzc01lc3NhZ2UgKz0gYFxcbuKAoiDmuIXpmaTkuoYgJHtyZXN1bHQucGFydGljaXBhbnRzVXBkYXRlZH0g5L2N5oiQ5ZOh55qE6IG36YqcYFxuICAgICAgICAgICAgc3VjY2Vzc01lc3NhZ2UgKz0gYFxcbuKAoiDpgJnkupvmiJDlk6HnmoTogbfpipznj77lnKjngrrnqbrnmb1gXG4gICAgICAgICAgfVxuXG4gICAgICAgICAgaWYgKHJlc3VsdC5wbGFjZWhvbGRlcnNSZW1vdmVkID4gMCkge1xuICAgICAgICAgICAgc3VjY2Vzc01lc3NhZ2UgKz0gcmVzdWx0LnBhcnRpY2lwYW50c1VwZGF0ZWQgPiAwID8gYFxcbuKAoiDnp7vpmaTkuobogbfpipzkvZTkvY3nrKZgIDogYFxcblxcbuW3suenu+mZpOiBt+mKnOS9lOS9jeespuOAgmBcbiAgICAgICAgICB9XG5cbiAgICAgICAgICBpZiAocmVzdWx0LnBhcnRpY2lwYW50c1VwZGF0ZWQgPT09IDAgJiYgcmVzdWx0LnBsYWNlaG9sZGVyc1JlbW92ZWQgPT09IDApIHtcbiAgICAgICAgICAgIHN1Y2Nlc3NNZXNzYWdlICs9IGBcXG5cXG7mspLmnInmiJDlk6Hkvb/nlKjmraTogbfpipzvvIzlt7Lnm7TmjqXnp7vpmaTjgIJgXG4gICAgICAgICAgfVxuXG4gICAgICAgICAgYWxlcnQoc3VjY2Vzc01lc3NhZ2UpXG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgY29uc29sZS5lcnJvcign5Yiq6Zmk6IG36Yqc5pmC55m855Sf6Yyv6KqkOicsIGVycm9yKVxuICAgICAgICAgIGFsZXJ0KGDliKrpmaTogbfpipzmmYLnmbznlJ/pjK/oqqQ6ICR7ZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAn5pyq55+l6Yyv6KqkJ31gKVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+WIqumZpOiBt+mKnOaZgueZvOeUn+mMr+iqpDonLCBlcnJvcilcbiAgICAgIGFsZXJ0KGDliKrpmaTogbfpipzmmYLnmbznlJ/pjK/oqqQ6ICR7ZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAn5pyq55+l6Yyv6KqkJ31gKVxuICAgIH1cbiAgfVxuXG4gIC8vIOeNsuWPluWPg+WKoOiAheeahOatt+WPsuWxhuWIpeS/oeaBr1xuICBjb25zdCBnZXRQYXJ0aWNpcGFudEhpc3RvcnkgPSAocGFydGljaXBhbnRJZDogc3RyaW5nKSA9PiB7XG4gICAgcmV0dXJuIHNlc3Npb25QYXJ0aWNpcGFudHNcbiAgICAgIC5maWx0ZXIoKHNwKSA9PiBzcC5wYXJ0aWNpcGFudElkID09PSBwYXJ0aWNpcGFudElkKVxuICAgICAgLm1hcCgoc3ApID0+ICh7XG4gICAgICAgIC4uLnNwLFxuICAgICAgICBzZXNzaW9uTmFtZTogc2Vzc2lvbnMuZmluZCgocykgPT4gcy5pZCA9PT0gc3Auc2Vzc2lvbklkKT8ubmFtZSB8fCBcIuacquefpeWxhuWIpVwiLFxuICAgICAgfSkpXG4gICAgICAuc29ydCgoYSwgYikgPT4gYi5zZXNzaW9uSWQubG9jYWxlQ29tcGFyZShhLnNlc3Npb25JZCkpXG4gIH1cblxuICBjb25zdCBzb3J0ZWRBbGxQYXJ0aWNpcGFudHMgPSB1c2VNZW1vKCgpID0+IHtcbiAgICBpZiAoIXNvcnRGaWVsZCkgcmV0dXJuIGFsbFBhcnRpY2lwYW50c1xuXG4gICAgcmV0dXJuIFsuLi5hbGxQYXJ0aWNpcGFudHNdLnNvcnQoKGEsIGIpID0+IHtcbiAgICAgIGxldCBhVmFsdWU6IGFueSA9IFwiXCJcbiAgICAgIGxldCBiVmFsdWU6IGFueSA9IFwiXCJcblxuICAgICAgc3dpdGNoIChzb3J0RmllbGQpIHtcbiAgICAgICAgY2FzZSBcIm5hbWVcIjpcbiAgICAgICAgICBhVmFsdWUgPSBhLm5hbWUudG9Mb3dlckNhc2UoKVxuICAgICAgICAgIGJWYWx1ZSA9IGIubmFtZS50b0xvd2VyQ2FzZSgpXG4gICAgICAgICAgYnJlYWtcbiAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICByZXR1cm4gMFxuICAgICAgfVxuXG4gICAgICBpZiAoYVZhbHVlIDwgYlZhbHVlKSByZXR1cm4gc29ydERpcmVjdGlvbiA9PT0gXCJhc2NcIiA/IC0xIDogMVxuICAgICAgaWYgKGFWYWx1ZSA+IGJWYWx1ZSkgcmV0dXJuIHNvcnREaXJlY3Rpb24gPT09IFwiYXNjXCIgPyAxIDogLTFcbiAgICAgIHJldHVybiAwXG4gICAgfSlcbiAgfSwgW2FsbFBhcnRpY2lwYW50cywgc29ydEZpZWxkLCBzb3J0RGlyZWN0aW9uXSlcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgb25DbGljaz17b25CYWNrfVxuICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTMgcHktMSBiZy1ncmF5LTIwMCBkYXJrOmJnLWdyYXktNjAwIHRleHQtZ3JheS03MDAgZGFyazp0ZXh0LWdyYXktMjAwIHJvdW5kZWQtbWQgaG92ZXI6YmctZ3JheS0zMDAgZGFyazpob3ZlcjpiZy1ncmF5LTUwMCB0cmFuc2l0aW9uLWNvbG9ycyB0ZXh0LXNtXCJcbiAgICAgICAgPlxuICAgICAgICAgIOKGkCDov5Tlm55cbiAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC13aGl0ZVwiPuWPg+WKoOiAheeuoeeQhjwvaDI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTJcIj5cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93VGl0bGVNYW5hZ2VtZW50KHRydWUpfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtMyBweS0xIGJnLXB1cnBsZS01MDAgdGV4dC13aGl0ZSByb3VuZGVkLW1kIGhvdmVyOmJnLXB1cnBsZS02MDAgdHJhbnNpdGlvbi1jb2xvcnMgdGV4dC1zbSBmbGV4IGl0ZW1zLWNlbnRlclwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPHN2Z1xuICAgICAgICAgICAgICB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC00IHctNCBtci0xXCJcbiAgICAgICAgICAgICAgZmlsbD1cIm5vbmVcIlxuICAgICAgICAgICAgICB2aWV3Qm94PVwiMCAwIDI0IDI0XCJcbiAgICAgICAgICAgICAgc3Ryb2tlPVwiY3VycmVudENvbG9yXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPHBhdGhcbiAgICAgICAgICAgICAgICBzdHJva2VMaW5lY2FwPVwicm91bmRcIlxuICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIlxuICAgICAgICAgICAgICAgIHN0cm9rZVdpZHRoPXsyfVxuICAgICAgICAgICAgICAgIGQ9XCJNMTAuMzI1IDQuMzE3Yy40MjYtMS43NTYgMi45MjQtMS43NTYgMy4zNSAwYTEuNzI0IDEuNzI0IDAgMDAyLjU3MyAxLjA2NmMxLjU0My0uOTQgMy4zMS44MjYgMi4zNyAyLjM3YTEuNzI0IDEuNzI0IDAgMDAxLjA2NSAyLjU3MmMxLjc1Ni40MjYgMS43NTYgMi45MjQgMCAzLjM1YTEuNzI0IDEuNzI0IDAgMDAtMS4wNjYgMi41NzNjLjk0IDEuNTQzLS44MjYgMy4zMS0yLjM3IDIuMzdhMS43MjQgMS43MjQgMCAwMC0yLjU3MiAxLjA2NWMtLjQyNiAxLjc1Ni0yLjkyNCAxLjc1Ni0zLjM1IDBhMS43MjQgMS43MjQgMCAwMC0yLjU3My0xLjA2NmMtMS41NDMuOTQtMy4zMS0uODI2LTIuMzctMi4zN2ExLjcyNCAxLjcyNCAwIDAwLTEuMDY1LTIuNTcyYy0xLjc1Ni0uNDI2LTEuNzU2LTIuOTI0IDAtMy4zNWExLjcyNCAxLjcyNCAwIDAwMS4wNjYtMi41NzNjLS45NC0xLjU0My44MjYtMy4zMSAyLjM3LTIuMzcuOTk2LjYwOCAyLjI5Ni4wNyAyLjU3Mi0xLjA2NXpcIlxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNMTUgMTJhMyAzIDAgMTEtNiAwIDMgMyAwIDAxNiAwelwiIC8+XG4gICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgIOiBt+mKnOeuoeeQhlxuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dCdWxrSW1wb3J0KHRydWUpfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtMyBweS0xIGJnLWluZGlnby01MDAgdGV4dC13aGl0ZSByb3VuZGVkLW1kIGhvdmVyOmJnLWluZGlnby02MDAgdHJhbnNpdGlvbi1jb2xvcnMgdGV4dC1zbSBmbGV4IGl0ZW1zLWNlbnRlclwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPHN2Z1xuICAgICAgICAgICAgICB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC00IHctNCBtci0xXCJcbiAgICAgICAgICAgICAgZmlsbD1cIm5vbmVcIlxuICAgICAgICAgICAgICB2aWV3Qm94PVwiMCAwIDI0IDI0XCJcbiAgICAgICAgICAgICAgc3Ryb2tlPVwiY3VycmVudENvbG9yXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPHBhdGhcbiAgICAgICAgICAgICAgICBzdHJva2VMaW5lY2FwPVwicm91bmRcIlxuICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIlxuICAgICAgICAgICAgICAgIHN0cm9rZVdpZHRoPXsyfVxuICAgICAgICAgICAgICAgIGQ9XCJNNyAxNmE0IDQgMCAwMS0uODgtNy45MDNBNSA1IDAgMTExNS45IDZMMTYgNmE1IDUgMCAwMTEgOS45TTkgMTlsMyAzbTAgMGwzLTNtLTMgM1YxMFwiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgIOaJuemHj+WwjuWFpVxuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7Lyog5rS76LqN6Kit5a6a6aGv56S6ICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ibHVlLTUwIGRhcms6YmctYmx1ZS05MDAvMjAgcm91bmRlZC1sZyBwLTNcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtYmx1ZS04MDAgZGFyazp0ZXh0LWJsdWUtMjAwXCI+XG4gICAgICAgICAgPHN0cm9uZz7nlbbliY3mtLvouo3oqK3lrprvvJo8L3N0cm9uZz5cbiAgICAgICAgICDpnZ7luLjmtLvouo0g4omlIHthY3Rpdml0eUxldmVsU2V0dGluZ3MudmVyeUFjdGl2ZX0l77yMIOa0u+i6jSB7YWN0aXZpdHlMZXZlbFNldHRpbmdzLmFjdGl2ZX0lLVxuICAgICAgICAgIHthY3Rpdml0eUxldmVsU2V0dGluZ3MudmVyeUFjdGl2ZSAtIDF9Je+8jCDkuI3mtLvouo0gJmx0OyB7YWN0aXZpdHlMZXZlbFNldHRpbmdzLmFjdGl2ZX0lXG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiDlj4PoiIfluqbntbHoqIggKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIGRhcms6YmctZ3JheS03MDAgcm91bmRlZC1sZyBzaGFkb3cgcC00XCI+XG4gICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LW1kIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LXdoaXRlIG1iLTRcIj7lj4PoiIfluqbntbHoqIg8L2gzPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTQgZ2FwLTRcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWJsdWUtNTAgZGFyazpiZy1ibHVlLTkwMC8yMCByb3VuZGVkLWxnIHAtNFwiPlxuICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ibHVlLTcwMCBkYXJrOnRleHQtYmx1ZS0zMDBcIj7mlbTpq5TlubPlnYflj4PoiIfluqY8L2g0PlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtYmx1ZS02MDAgZGFyazp0ZXh0LWJsdWUtNDAwXCI+XG4gICAgICAgICAgICAgIHtzdGF0aXN0aWNzLnBhcnRpY2lwYXRpb25TdGF0cy5hdmVyYWdlUGFydGljaXBhdGlvblJhdGUudG9GaXhlZCgxKX0lXG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtYmx1ZS02MDAgZGFyazp0ZXh0LWJsdWUtNDAwIG10LTFcIj5cbiAgICAgICAgICAgICAg5oiQ5ZOh5Y+D6IiH5rS75YuV55qE5bmz5Z2H5q+U5L6LXG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7c2VsZWN0ZWRTZXNzaW9uSWQgJiYgc3RhdGlzdGljcy5wYXJ0aWNpcGF0aW9uU3RhdHMuc2Vzc2lvblBhcnRpY2lwYXRpb25TdGF0c1tzZWxlY3RlZFNlc3Npb25JZF0gJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmVlbi01MCBkYXJrOmJnLWdyZWVuLTkwMC8yMCByb3VuZGVkLWxnIHAtNFwiPlxuICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyZWVuLTcwMCBkYXJrOnRleHQtZ3JlZW4tMzAwXCI+55W25YmN5bGG5Yil5Y+D6IiH5bqmPC9oND5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JlZW4tNjAwIGRhcms6dGV4dC1ncmVlbi00MDBcIj5cbiAgICAgICAgICAgICAgICB7c3RhdGlzdGljcy5wYXJ0aWNpcGF0aW9uU3RhdHMuc2Vzc2lvblBhcnRpY2lwYXRpb25TdGF0c1tzZWxlY3RlZFNlc3Npb25JZF0uYXZlcmFnZVBhcnRpY2lwYXRpb25SYXRlLnRvRml4ZWQoMSl9JVxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmVlbi02MDAgZGFyazp0ZXh0LWdyZWVuLTQwMCBtdC0xXCI+XG4gICAgICAgICAgICAgICAge3N0YXRpc3RpY3MucGFydGljaXBhdGlvblN0YXRzLnNlc3Npb25QYXJ0aWNpcGF0aW9uU3RhdHNbc2VsZWN0ZWRTZXNzaW9uSWRdLnNlc3Npb25OYW1lfVxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1wdXJwbGUtNTAgZGFyazpiZy1wdXJwbGUtOTAwLzIwIHJvdW5kZWQtbGcgcC00XCI+XG4gICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXB1cnBsZS03MDAgZGFyazp0ZXh0LXB1cnBsZS0zMDBcIj7mtLvouo3miJDlk6Hmlbg8L2g0PlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtcHVycGxlLTYwMCBkYXJrOnRleHQtcHVycGxlLTQwMFwiPlxuICAgICAgICAgICAgICB7c2VsZWN0ZWRTZXNzaW9uSWQgJiYgc3RhdGlzdGljcy5wYXJ0aWNpcGF0aW9uU3RhdHMuc2Vzc2lvblBhcnRpY2lwYXRpb25TdGF0c1tzZWxlY3RlZFNlc3Npb25JZF1cbiAgICAgICAgICAgICAgICA/IHN0YXRpc3RpY3MucGFydGljaXBhdGlvblN0YXRzLnNlc3Npb25QYXJ0aWNpcGF0aW9uU3RhdHNbc2VsZWN0ZWRTZXNzaW9uSWRdLmFjdGl2ZVBhcnRpY2lwYW50c1xuICAgICAgICAgICAgICAgIDogc3RhdGlzdGljcy5wYXJ0aWNpcGFudFN0YXRzLmZpbHRlcihwID0+IHAucGFydGljaXBhdGlvblJhdGUgPiAwKS5sZW5ndGh9XG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtcHVycGxlLTYwMCBkYXJrOnRleHQtcHVycGxlLTQwMCBtdC0xXCI+XG4gICAgICAgICAgICAgIOWPg+iIh+mBjua0u+WLleeahOaIkOWToVxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1vcmFuZ2UtNTAgZGFyazpiZy1vcmFuZ2UtOTAwLzIwIHJvdW5kZWQtbGcgcC00XCI+XG4gICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LW9yYW5nZS03MDAgZGFyazp0ZXh0LW9yYW5nZS0zMDBcIj7pq5jlj4PoiIfluqbmiJDlk6E8L2g0PlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtb3JhbmdlLTYwMCBkYXJrOnRleHQtb3JhbmdlLTQwMFwiPlxuICAgICAgICAgICAgICB7c3RhdGlzdGljcy5wYXJ0aWNpcGF0aW9uU3RhdHMuaGlnaFBhcnRpY2lwYXRpb25TdGF0cy5jb3VudH1cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1vcmFuZ2UtNjAwIGRhcms6dGV4dC1vcmFuZ2UtNDAwIG10LTFcIj5cbiAgICAgICAgICAgICAg4omle3N0YXRpc3RpY3MucGFydGljaXBhdGlvblN0YXRzLmhpZ2hQYXJ0aWNpcGF0aW9uU3RhdHMudGhyZXNob2xkfSUgKHtzdGF0aXN0aWNzLnBhcnRpY2lwYXRpb25TdGF0cy5oaWdoUGFydGljaXBhdGlvblN0YXRzLnBlcmNlbnRhZ2UudG9GaXhlZCgxKX0lKVxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7Lyog6IG36Yqc566h55CG5bCN6Kmx5qGGICovfVxuICAgICAge3Nob3dUaXRsZU1hbmFnZW1lbnQgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgYmctYmxhY2sgYmctb3BhY2l0eS01MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB6LTUwXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBkYXJrOmJnLWdyYXktODAwIHJvdW5kZWQtbGcgc2hhZG93LWxnIG1heC13LTJ4bCB3LWZ1bGwgbXgtNCBtYXgtaC1bODB2aF0gb3ZlcmZsb3cteS1hdXRvXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNlwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlciBtYi00XCI+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtd2hpdGVcIj7ogbfpipznrqHnkIY8L2gzPlxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dUaXRsZU1hbmFnZW1lbnQoZmFsc2UpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LWdyYXktNjAwIGRhcms6aG92ZXI6dGV4dC1ncmF5LTMwMFwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTYgaC02XCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk02IDE4TDE4IDZNNiA2bDEyIDEyXCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7Lyog5paw5aKe6IG36YqcICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTZcIj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIGRhcms6dGV4dC1ncmF5LTMwMCBtYi0yXCI+5paw5aKe6IG36YqcPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17bmV3VGl0bGV9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0TmV3VGl0bGUoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIui8uOWFpeaWsOiBt+mKnOWQjeeosVwiXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXgtMSBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCBkYXJrOmJvcmRlci1ncmF5LTYwMCByb3VuZGVkLW1kIGRhcms6YmctZ3JheS03MDAgZGFyazp0ZXh0LXdoaXRlXCJcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICBpZiAobmV3VGl0bGUudHJpbSgpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAvLyDmqqLmn6XogbfpipzmmK/lkKblt7LlrZjlnKjvvIjkuI3ljYDliIblpKflsI/lr6vvvIlcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGFsbENhdGVnb3JpZXMuc29tZShjYXRlZ29yeSA9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNhdGVnb3J5LnRvTG93ZXJDYXNlKCkgPT09IG5ld1RpdGxlLnRyaW0oKS50b0xvd2VyQ2FzZSgpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbGVydChg6IG36Yqc44CMJHtuZXdUaXRsZS50cmltKCl944CN5bey5a2Y5ZyoYClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAgICAgICAgICAgICAgIC8vIOaWsOWinuiBt+mKnCAtIOeUseaWvOiBt+mKnOaYr+W+nuWPg+WKoOiAheizh+aWmeS4reaPkOWPlueahO+8jFxuICAgICAgICAgICAgICAgICAgICAgICAgICAvLyDmiJHlgJHlibXlu7rkuIDlgIvkvb/nlKjpgJnlgIvogbfpipznmoTkvZTkvY3nrKblj4PliqDogIVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgbmV3UGFydGljaXBhbnRXaXRoVGl0bGUgPSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ6IGB0ZW1wLSR7RGF0ZS5ub3coKX1gLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5hbWU6IGDogbfpipzkvZTkvY3nrKYtJHtuZXdUaXRsZS50cmltKCl9YCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjYXRlZ29yeTogbmV3VGl0bGUudHJpbSgpLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlzQWN0aXZlOiB0cnVlLFxuICAgICAgICAgICAgICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgLy8g5re75Yqg5Yiw5YWo5bGA5Y+D5Yqg6ICF5YiX6KGoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQWRkUGFydGljaXBhbnQobmV3UGFydGljaXBhbnRXaXRoVGl0bGUpXG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgYWxlcnQoYOaIkOWKn+aWsOWinuiBt+mKnOOAjCR7bmV3VGl0bGUudHJpbSgpfeOAjWApXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHNldE5ld1RpdGxlKFwiXCIpXG4gICAgICAgICAgICAgICAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCfmlrDlop7ogbfpipzmmYLnmbznlJ/pjK/oqqQ6JywgZXJyb3IpXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGFsZXJ0KGDmlrDlop7ogbfpipzmmYLnmbznlJ/pjK/oqqQ6ICR7ZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAn5pyq55+l6Yyv6KqkJ31gKVxuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNCBweS0yIGJnLWdyZWVuLTUwMCB0ZXh0LXdoaXRlIHJvdW5kZWQtbWQgaG92ZXI6YmctZ3JlZW4tNjAwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9eyFuZXdUaXRsZS50cmltKCl9XG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIOaWsOWinlxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiDnj77mnInogbfpipzliJfooaggKi99XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtbWQgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtd2hpdGUgbWItM1wiPuePvuacieiBt+mKnDwvaDQ+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgIHthbGxDYXRlZ29yaWVzLm1hcCgodGl0bGUpID0+IChcbiAgICAgICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgICAgIGtleT17dGl0bGV9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHAtMyBiZy1ncmF5LTUwIGRhcms6YmctZ3JheS03MDAgcm91bmRlZC1tZFwiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICB7ZWRpdGluZ1RpdGxlID09PSB0aXRsZSA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIGZsZXggc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkZWZhdWx0VmFsdWU9e3RpdGxlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXgtMSBweC0yIHB5LTEgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCBkYXJrOmJvcmRlci1ncmF5LTYwMCByb3VuZGVkIGRhcms6YmctZ3JheS02MDAgZGFyazp0ZXh0LXdoaXRlXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbktleURvd249eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoZS5rZXkgPT09IFwiRW50ZXJcIikge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVVcGRhdGVUaXRsZSh0aXRsZSwgZS5jdXJyZW50VGFyZ2V0LnZhbHVlKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIGlmIChlLmtleSA9PT0gXCJFc2NhcGVcIikge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRFZGl0aW5nVGl0bGUoXCJcIilcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGF1dG9Gb2N1c1xuICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0RWRpdGluZ1RpdGxlKFwiXCIpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTIgcHktMSBiZy1ncmF5LTUwMCB0ZXh0LXdoaXRlIHJvdW5kZWQgdGV4dC1zbSBob3ZlcjpiZy1ncmF5LTYwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICDlj5bmtohcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtd2hpdGVcIj57dGl0bGV9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0RWRpdGluZ1RpdGxlKHRpdGxlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtYmx1ZS02MDAgZGFyazp0ZXh0LWJsdWUtNDAwIGhvdmVyOnRleHQtYmx1ZS04MDAgZGFyazpob3Zlcjp0ZXh0LWJsdWUtMzAwIHRleHQtc21cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIOe3qOi8r1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZURlbGV0ZVRpdGxlKHRpdGxlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtcmVkLTYwMCBkYXJrOnRleHQtcmVkLTQwMCBob3Zlcjp0ZXh0LXJlZC04MDAgZGFyazpob3Zlcjp0ZXh0LXJlZC0zMDAgdGV4dC1zbVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg5Yiq6ZmkXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgIHthbGxDYXRlZ29yaWVzLmxlbmd0aCA9PT0gMCAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDAgZGFyazp0ZXh0LWdyYXktNDAwIHRleHQtY2VudGVyIHB5LTRcIj7mmqvnhKHogbfpipw8L3A+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHsvKiDmibnph4/lsI7lhaXlsI3oqbHmoYYgKi99XG4gICAgICB7c2hvd0J1bGtJbXBvcnQgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgYmctYmxhY2sgYmctb3BhY2l0eS01MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB6LTUwXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBkYXJrOmJnLWdyYXktODAwIHJvdW5kZWQtbGcgc2hhZG93LWxnIG1heC13LTR4bCB3LWZ1bGwgbXgtNCBtYXgtaC1bOTB2aF0gb3ZlcmZsb3cteS1hdXRvXCI+XG4gICAgICAgICAgICA8VW5pdmVyc2FsQnVsa0ltcG9ydFxuICAgICAgICAgICAgICBkYXRhVHlwZT1cInBhcnRpY2lwYW50c1wiXG4gICAgICAgICAgICAgIHNlc3Npb25zPXtzZXNzaW9uc31cbiAgICAgICAgICAgICAgc2VsZWN0ZWRTZXNzaW9uSWQ9e3NlbGVjdGVkU2Vzc2lvbklkfVxuICAgICAgICAgICAgICBvbkltcG9ydD17aGFuZGxlQnVsa0ltcG9ydH1cbiAgICAgICAgICAgICAgb25DYW5jZWw9eygpID0+IHNldFNob3dCdWxrSW1wb3J0KGZhbHNlKX1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cblxuICAgICAgey8qIOeVtuWJjeWxhuWIpeS/oeaBryAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgZGFyazpiZy1ncmF5LTcwMCByb3VuZGVkLWxnIHNoYWRvdyBwLTRcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbWQgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtd2hpdGVcIj7nlbbliY3lsYbliKU8L2gzPlxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwicHgtMyBweS0xIGJnLWJsdWUtMTAwIGRhcms6YmctYmx1ZS05MDAgdGV4dC1ibHVlLTgwMCBkYXJrOnRleHQtYmx1ZS0yMDAgcm91bmRlZC1mdWxsIHRleHQtc20gZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAge2N1cnJlbnRTZXNzaW9uPy5uYW1lIHx8IFwi5pyq6YG45pOH5bGG5YilXCJ9XG4gICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICB7Y3VycmVudFNlc3Npb24gJiYgKFxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDAgZGFyazp0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAgICAgICAge2N1cnJlbnRTZXNzaW9uLnN0YXJ0RGF0ZX0gLSB7Y3VycmVudFNlc3Npb24uZW5kRGF0ZX1cbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMCBkYXJrOnRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgIOWxhuWIpeWPg+WKoOiAhToge2N1cnJlbnRTZXNzaW9uUGFydGljaXBhbnRzLmxlbmd0aH0g5Lq6XG4gICAgICAgICAgICA8YnIgLz5cbiAgICAgICAgICAgIOaJgOacieaIkOWToToge2FsbFBhcnRpY2lwYW50cy5sZW5ndGh9IOS6ulxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7Lyog6KaW5ZyW5qih5byP5YiH5o+bICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBkYXJrOmJnLWdyYXktNzAwIHJvdW5kZWQtbGcgc2hhZG93IHAtNFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0Vmlld01vZGUoXCJzZXNzaW9uXCIpfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgICAgICAgIFwicHgtNCBweS0yIHJvdW5kZWQtbWQgdHJhbnNpdGlvbi1jb2xvcnNcIixcbiAgICAgICAgICAgICAgICB2aWV3TW9kZSA9PT0gXCJzZXNzaW9uXCJcbiAgICAgICAgICAgICAgICAgID8gXCJiZy1ibHVlLTUwMCB0ZXh0LXdoaXRlXCJcbiAgICAgICAgICAgICAgICAgIDogXCJiZy1ncmF5LTIwMCBkYXJrOmJnLWdyYXktNjAwIHRleHQtZ3JheS03MDAgZGFyazp0ZXh0LWdyYXktMjAwIGhvdmVyOmJnLWdyYXktMzAwIGRhcms6aG92ZXI6YmctZ3JheS01MDBcIixcbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAg5bGG5Yil5Y+D5Yqg6ICFXG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0Vmlld01vZGUoXCJnbG9iYWxcIil9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgICAgICAgXCJweC00IHB5LTIgcm91bmRlZC1tZCB0cmFuc2l0aW9uLWNvbG9yc1wiLFxuICAgICAgICAgICAgICAgIHZpZXdNb2RlID09PSBcImdsb2JhbFwiXG4gICAgICAgICAgICAgICAgICA/IFwiYmctYmx1ZS01MDAgdGV4dC13aGl0ZVwiXG4gICAgICAgICAgICAgICAgICA6IFwiYmctZ3JheS0yMDAgZGFyazpiZy1ncmF5LTYwMCB0ZXh0LWdyYXktNzAwIGRhcms6dGV4dC1ncmF5LTIwMCBob3ZlcjpiZy1ncmF5LTMwMCBkYXJrOmhvdmVyOmJnLWdyYXktNTAwXCIsXG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIOaJgOacieaIkOWToVxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDAgZGFyazp0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAgICB7dmlld01vZGUgPT09IFwic2Vzc2lvblwiXG4gICAgICAgICAgICAgID8gYOmhr+ekuiAke2ZpbHRlcmVkU2Vzc2lvblBhcnRpY2lwYW50cy5sZW5ndGh9IC8gJHtjdXJyZW50U2Vzc2lvblBhcnRpY2lwYW50cy5sZW5ndGh9IOS9jeWxhuWIpeWPg+WKoOiAhWBcbiAgICAgICAgICAgICAgOiBg6aGv56S6ICR7YWxsUGFydGljaXBhbnRzLmxlbmd0aH0g5L2N5omA5pyJ5oiQ5ZOhYH1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAge3ZpZXdNb2RlID09PSBcInNlc3Npb25cIiA/IChcbiAgICAgICAgPD5cbiAgICAgICAgICB7Lyog5pCc57Si5ZKM6YGO5r++ICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMyBnYXAtNFwiPlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBkYXJrOnRleHQtZ3JheS0zMDAgbWItMVwiPuaQnOe0ouWPg+WKoOiAhTwvbGFiZWw+XG4gICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICB2YWx1ZT17c2VhcmNoVGVybX1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNlYXJjaFRlcm0oZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi6Ly45YWl5Y+D5Yqg6ICF5aeT5ZCNXCJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtMyBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgZGFyazpib3JkZXItZ3JheS02MDAgcm91bmRlZC1tZCBkYXJrOmJnLWdyYXktNzAwIGRhcms6dGV4dC13aGl0ZVwiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgZGFyazp0ZXh0LWdyYXktMzAwIG1iLTFcIj7ogbfpipzpgY7mv748L2xhYmVsPlxuICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgdmFsdWU9e2NhdGVnb3J5RmlsdGVyfVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Q2F0ZWdvcnlGaWx0ZXIoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCBkYXJrOmJvcmRlci1ncmF5LTYwMCByb3VuZGVkLW1kIGRhcms6YmctZ3JheS03MDAgZGFyazp0ZXh0LXdoaXRlXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJcIj7lhajpg6jogbfpipw8L29wdGlvbj5cbiAgICAgICAgICAgICAgICB7Y3VycmVudFNlc3Npb25DYXRlZ29yaWVzLm1hcCgoY2F0ZWdvcnkpID0+IChcbiAgICAgICAgICAgICAgICAgIDxvcHRpb24ga2V5PXtjYXRlZ29yeX0gdmFsdWU9e2NhdGVnb3J5fT5cbiAgICAgICAgICAgICAgICAgICAge2NhdGVnb3J5fVxuICAgICAgICAgICAgICAgICAgPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIGRhcms6dGV4dC1ncmF5LTMwMCBtYi0xXCI+54uA5oWL6YGO5r++PC9sYWJlbD5cbiAgICAgICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgICAgIHZhbHVlPXtzdGF0dXNGaWx0ZXJ9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTdGF0dXNGaWx0ZXIoZS50YXJnZXQudmFsdWUgYXMgXCJhbGxcIiB8IFwiYWN0aXZlXCIgfCBcImluYWN0aXZlXCIpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCBkYXJrOmJvcmRlci1ncmF5LTYwMCByb3VuZGVkLW1kIGRhcms6YmctZ3JheS03MDAgZGFyazp0ZXh0LXdoaXRlXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJhbGxcIj7lhajpg6jni4DmhYs8L29wdGlvbj5cbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiYWN0aXZlXCI+5rS76LqNPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImluYWN0aXZlXCI+6Z2e5rS76LqNPC9vcHRpb24+XG4gICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7Lyog5bGG5Yil5Y+D5Yqg6ICF5YiX6KGoICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgZGFyazpiZy1ncmF5LTcwMCByb3VuZGVkLWxnIHNoYWRvdyBvdmVyZmxvdy1oaWRkZW5cIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtNiBweS00IGJvcmRlci1iIGJvcmRlci1ncmF5LTIwMCBkYXJrOmJvcmRlci1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICAgIHtjdXJyZW50U2Vzc2lvbj8ubmFtZX0g5Y+D5Yqg6ICF5YiX6KGoICh7ZmlsdGVyZWRTZXNzaW9uUGFydGljaXBhbnRzLmxlbmd0aH0pXG4gICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwib3ZlcmZsb3cteC1hdXRvXCI+XG4gICAgICAgICAgICAgIDx0YWJsZSBjbGFzc05hbWU9XCJtaW4tdy1mdWxsIGRpdmlkZS15IGRpdmlkZS1ncmF5LTIwMCBkYXJrOmRpdmlkZS1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgIDx0aGVhZCBjbGFzc05hbWU9XCJiZy1ncmF5LTUwIGRhcms6YmctZ3JheS04MDBcIj5cbiAgICAgICAgICAgICAgICAgIDx0cj5cbiAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTYgcHktMyB0ZXh0LWxlZnQgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyYXktNTAwIGRhcms6dGV4dC1ncmF5LTMwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVTb3J0KFwibmFtZVwiKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMSBob3ZlcjpiZy1ncmF5LTEwMCBkYXJrOmhvdmVyOmJnLWdyYXktNzAwIHB4LTIgcHktMSByb3VuZGVkIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7lp5PlkI08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTRcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge3NvcnRGaWVsZCA9PT0gXCJuYW1lXCIgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc29ydERpcmVjdGlvbiA9PT0gXCJhc2NcIiA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VXaWR0aD17Mn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZD1cIk01IDE1bDctNyA3IDdcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNTAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VXaWR0aD17Mn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZD1cIk0xOSA5bC03IDctNy03XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTUwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlTGluZWNhcD1cInJvdW5kXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9ezJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkPVwiTTggOWw0LTQgNCA0bTAgNmwtNCA0LTQtNFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS0zIHRleHQtbGVmdCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgZGFyazp0ZXh0LWdyYXktMzAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZVNvcnQoXCJjYXRlZ29yeVwiKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMSBob3ZlcjpiZy1ncmF5LTEwMCBkYXJrOmhvdmVyOmJnLWdyYXktNzAwIHB4LTIgcHktMSByb3VuZGVkIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7ogbfpipw8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTRcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge3NvcnRGaWVsZCA9PT0gXCJjYXRlZ29yeVwiID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNvcnREaXJlY3Rpb24gPT09IFwiYXNjXCIgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VMaW5lY2FwPVwicm91bmRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VMaW5lam9pbj1cInJvdW5kXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9ezJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGQ9XCJNNSAxNWw3LTcgNyA3XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTUwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VMaW5lY2FwPVwicm91bmRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VMaW5lam9pbj1cInJvdW5kXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9ezJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGQ9XCJNMTkgOWwtNyA3LTctN1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtYmx1ZS01MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGhcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VMaW5lam9pbj1cInJvdW5kXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZVdpZHRoPXsyfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZD1cIk04IDlsNC00IDQgNG0wIDZsLTQgNC00LTRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTYgcHktMyB0ZXh0LWxlZnQgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyYXktNTAwIGRhcms6dGV4dC1ncmF5LTMwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVTb3J0KFwiam9pbkRhdGVcIil9XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTEgaG92ZXI6YmctZ3JheS0xMDAgZGFyazpob3ZlcjpiZy1ncmF5LTcwMCBweC0yIHB5LTEgcm91bmRlZCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+5Yqg5YWl5pel5pyfPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00XCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtzb3J0RmllbGQgPT09IFwiam9pbkRhdGVcIiA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzb3J0RGlyZWN0aW9uID09PSBcImFzY1wiID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGhcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlTGluZWNhcD1cInJvdW5kXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZVdpZHRoPXsyfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkPVwiTTUgMTVsNy03IDcgN1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtYmx1ZS01MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGhcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlTGluZWNhcD1cInJvdW5kXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZVdpZHRoPXsyfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkPVwiTTE5IDlsLTcgNy03LTdcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNTAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKVxuICAgICAgICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VMaW5lY2FwPVwicm91bmRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VXaWR0aD17Mn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGQ9XCJNOCA5bDQtNCA0IDRtMCA2bC00IDQtNC00XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTMgdGV4dC1sZWZ0IHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMCBkYXJrOnRleHQtZ3JheS0zMDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlU29ydChcImF0dGVuZGFuY2VSYXRlXCIpfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0xIGhvdmVyOmJnLWdyYXktMTAwIGRhcms6aG92ZXI6YmctZ3JheS03MDAgcHgtMiBweS0xIHJvdW5kZWQgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPua0u+i6jeeLgOaFizwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7c29ydEZpZWxkID09PSBcImF0dGVuZGFuY2VSYXRlXCIgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc29ydERpcmVjdGlvbiA9PT0gXCJhc2NcIiA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VXaWR0aD17Mn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZD1cIk01IDE1bDctNyA3IDdcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNTAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VXaWR0aD17Mn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZD1cIk0xOSA5bC03IDctNy03XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTUwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlTGluZWNhcD1cInJvdW5kXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9ezJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkPVwiTTggOWw0LTQgNCA0bTAgNmwtNCA0LTQtNFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS0zIHRleHQtbGVmdCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgZGFyazp0ZXh0LWdyYXktMzAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIOWPg+iIh+W6puips+aDhVxuICAgICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS0zIHRleHQtbGVmdCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgZGFyazp0ZXh0LWdyYXktMzAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIOatt+WPsuWxhuWIpVxuICAgICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS0zIHRleHQtcmlnaHQgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyYXktNTAwIGRhcms6dGV4dC1ncmF5LTMwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICDmk43kvZxcbiAgICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICAgIDwvdHI+XG4gICAgICAgICAgICAgICAgPC90aGVhZD5cbiAgICAgICAgICAgICAgICA8dGJvZHkgY2xhc3NOYW1lPVwiYmctd2hpdGUgZGFyazpiZy1ncmF5LTcwMCBkaXZpZGUteSBkaXZpZGUtZ3JheS0yMDAgZGFyazpkaXZpZGUtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICAgIHtmaWx0ZXJlZFNlc3Npb25QYXJ0aWNpcGFudHMubGVuZ3RoID4gMCA/IChcbiAgICAgICAgICAgICAgICAgICAgZmlsdGVyZWRTZXNzaW9uUGFydGljaXBhbnRzLm1hcCgoc2Vzc2lvblBhcnRpY2lwYW50KSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgY29uc3QgaGlzdG9yeSA9IGdldFBhcnRpY2lwYW50SGlzdG9yeShzZXNzaW9uUGFydGljaXBhbnQucGFydGljaXBhbnRJZClcbiAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgPHRyIGtleT17c2Vzc2lvblBhcnRpY2lwYW50LmlkfSBjbGFzc05hbWU9XCJob3ZlcjpiZy1ncmF5LTUwIGRhcms6aG92ZXI6YmctZ3JheS02NTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNCB3aGl0ZXNwYWNlLW5vd3JhcCB0ZXh0LXNtIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge3Nlc3Npb25QYXJ0aWNpcGFudC5uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00IHdoaXRlc3BhY2Utbm93cmFwIHRleHQtc20gdGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtd2hpdGVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJweC0yIHB5LTEgYmctYmx1ZS0xMDAgZGFyazpiZy1ibHVlLTkwMCB0ZXh0LWJsdWUtODAwIGRhcms6dGV4dC1ibHVlLTIwMCB0ZXh0LXhzIHJvdW5kZWRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtzZXNzaW9uUGFydGljaXBhbnQuY2F0ZWdvcnkgfHwgXCLnhKHogbfpipxcIn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXAgdGV4dC1zbSB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtzZXNzaW9uUGFydGljaXBhbnQuam9pbkRhdGUgfHwgXCItXCJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXAgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBzcGFjZS15LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXCJweC0yIHB5LTEgdGV4dC14cyByb3VuZGVkIGZvbnQtbWVkaXVtXCIsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZ2V0QWN0aXZpdHlMZXZlbENvbG9yKHNlc3Npb25QYXJ0aWNpcGFudC5hY3Rpdml0eUxldmVsKSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2dldEFjdGl2aXR5TGV2ZWxUZXh0KHNlc3Npb25QYXJ0aWNpcGFudC5hY3Rpdml0eUxldmVsKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMCBkYXJrOnRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg5Ye65bit546HOiB7c2Vzc2lvblBhcnRpY2lwYW50LmF0dGVuZGFuY2VSYXRlPy50b0ZpeGVkKDEpIHx8IDB9JVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwIGRhcms6dGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoe3Nlc3Npb25QYXJ0aWNpcGFudC50b3RhbEF0dGVuZGVkIHx8IDB9L3tzZXNzaW9uUGFydGljaXBhbnQudG90YWxQYXJ0aWNpcGF0ZWQgfHwgMH0pXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgdGV4dC1zbSB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsoKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgcGFydGljaXBhbnRTdGF0ID0gc3RhdGlzdGljcy5wYXJ0aWNpcGFudFN0YXRzLmZpbmQocCA9PiBwLmlkID09PSBzZXNzaW9uUGFydGljaXBhbnQucGFydGljaXBhbnRJZClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmICghcGFydGljaXBhbnRTdGF0KSByZXR1cm4gPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwIGRhcms6dGV4dC1ncmF5LTQwMFwiPueEoeizh+aWmTwvc3Bhbj5cblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIHNwYWNlLXktMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgZGFyazp0ZXh0LWdyYXktMzAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIOe4vemrlDoge3BhcnRpY2lwYW50U3RhdC5wYXJ0aWNpcGF0aW9uRGV0YWlscy5wYXJ0aWNpcGF0aW9uUmF0aW99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7cGFydGljaXBhbnRTdGF0LmlzSGlnaFBhcnRpY2lwYXRpb24gJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJweC0xIHB5LTAuNSBiZy1vcmFuZ2UtMTAwIGRhcms6Ymctb3JhbmdlLTkwMCB0ZXh0LW9yYW5nZS04MDAgZGFyazp0ZXh0LW9yYW5nZS0yMDAgdGV4dC14cyByb3VuZGVkXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg6auY5Y+D6IiHXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3NlbGVjdGVkU2Vzc2lvbklkICYmIHBhcnRpY2lwYW50U3RhdC5zZXNzaW9uUGFydGljaXBhdGlvbkRldGFpbHNbc2VsZWN0ZWRTZXNzaW9uSWRdICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ibHVlLTYwMCBkYXJrOnRleHQtYmx1ZS00MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg5pys5bGGOiB7cGFydGljaXBhbnRTdGF0LnNlc3Npb25QYXJ0aWNpcGF0aW9uRGV0YWlsc1tzZWxlY3RlZFNlc3Npb25JZF0ucGFydGljaXBhdGlvblJhdGlvfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwIGRhcms6dGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg5Y+D6IiH546HOiB7cGFydGljaXBhbnRTdGF0LnBhcnRpY2lwYXRpb25SYXRlLnRvRml4ZWQoMSl9JVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfSkoKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNCB0ZXh0LXNtIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtd3JhcCBnYXAtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2hpc3Rvcnkuc2xpY2UoMCwgMykubWFwKChoKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtoLmlkfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTEgcHktMC41IGJnLWdyYXktMTAwIGRhcms6YmctZ3JheS04MDAgdGV4dC1ncmF5LTcwMCBkYXJrOnRleHQtZ3JheS0zMDAgdGV4dC14cyByb3VuZGVkXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZT17YCR7aC5zZXNzaW9uTmFtZX06ICR7aC5jYXRlZ29yeX1gfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2guc2Vzc2lvbk5hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2hpc3RvcnkubGVuZ3RoID4gMyAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMCBkYXJrOnRleHQtZ3JheS00MDBcIj4re2hpc3RvcnkubGVuZ3RoIC0gM308L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00IHdoaXRlc3BhY2Utbm93cmFwIHRleHQtcmlnaHQgdGV4dC1zbSBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZ2xvYmFsUGFydGljaXBhbnQgPSBhbGxQYXJ0aWNpcGFudHMuZmluZChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAocCkgPT4gcC5pZCA9PT0gc2Vzc2lvblBhcnRpY2lwYW50LnBhcnRpY2lwYW50SWQsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGdsb2JhbFBhcnRpY2lwYW50KSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0Vmlld2luZ0F0dGVuZGFuY2VIaXN0b3J5KGdsb2JhbFBhcnRpY2lwYW50KVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTYwMCBkYXJrOnRleHQtYmx1ZS00MDAgaG92ZXI6dGV4dC1ibHVlLTkwMCBkYXJrOmhvdmVyOnRleHQtYmx1ZS0zMDAgbXItNFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg5Ye65bit6KiY6YyEXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0RWRpdGluZ1Nlc3Npb25QYXJ0aWNpcGFudChzZXNzaW9uUGFydGljaXBhbnQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1pbmRpZ28tNjAwIGRhcms6dGV4dC1pbmRpZ28tNDAwIGhvdmVyOnRleHQtaW5kaWdvLTkwMCBkYXJrOmhvdmVyOnRleHQtaW5kaWdvLTMwMCBtci00XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICDnt6jovK9cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmICh3aW5kb3cuY29uZmlybShg56K65a6a6KaB5bCHIFwiJHtzZXNzaW9uUGFydGljaXBhbnQubmFtZX1cIiDlvp7nlbbliY3lsYbliKXkuK3np7vpmaTll47vvJ9gKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uUmVtb3ZlRnJvbVNlc3Npb24oc2Vzc2lvblBhcnRpY2lwYW50LmlkKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1yZWQtNjAwIGRhcms6dGV4dC1yZWQtNDAwIGhvdmVyOnRleHQtcmVkLTkwMCBkYXJrOmhvdmVyOnRleHQtcmVkLTMwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg56e76ZmkXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3RyPlxuICAgICAgICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgIDx0cj5cbiAgICAgICAgICAgICAgICAgICAgICA8dGQgY29sU3Bhbj17Nn0gY2xhc3NOYW1lPVwicHgtNiBweS00IHRleHQtY2VudGVyIHRleHQtc20gdGV4dC1ncmF5LTUwMCBkYXJrOnRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIOaykuacieaJvuWIsOespuWQiOaineS7tueahOWPg+WKoOiAhVxuICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgIDwvdHI+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvdGJvZHk+XG4gICAgICAgICAgICAgIDwvdGFibGU+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiDlhKrljJblvoznmoTmt7vliqDnj77mnInlj4PliqDogIXlip/og70gKi99XG4gICAgICAgICAge2F2YWlsYWJsZUdsb2JhbFBhcnRpY2lwYW50cy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgZGFyazpiZy1ncmF5LTcwMCByb3VuZGVkLWxnIHNoYWRvdyBwLTRcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgbWItNFwiPlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgICAgICDmt7vliqDnj77mnInlj4PliqDogIXliLAge2N1cnJlbnRTZXNzaW9uPy5uYW1lfVxuICAgICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd0FkZEV4aXN0aW5nKCFzaG93QWRkRXhpc3RpbmcpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNCBweS0yIGJnLWdyZWVuLTUwMCB0ZXh0LXdoaXRlIHJvdW5kZWQtbWQgaG92ZXI6YmctZ3JlZW4tNjAwIHRyYW5zaXRpb24tY29sb3JzIGZsZXggaXRlbXMtY2VudGVyXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8c3ZnXG4gICAgICAgICAgICAgICAgICAgIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIlxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIlxuICAgICAgICAgICAgICAgICAgICBmaWxsPVwibm9uZVwiXG4gICAgICAgICAgICAgICAgICAgIHZpZXdCb3g9XCIwIDAgMjQgMjRcIlxuICAgICAgICAgICAgICAgICAgICBzdHJva2U9XCJjdXJyZW50Q29sb3JcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNMTIgNHYxNm04LThINFwiIC8+XG4gICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgIHtzaG93QWRkRXhpc3RpbmcgPyBcIuaUtui1t1wiIDogYOa3u+WKoOaIkOWToSAoJHthdmFpbGFibGVHbG9iYWxQYXJ0aWNpcGFudHMubGVuZ3RofSlgfVxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7c2hvd0FkZEV4aXN0aW5nICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAgey8qIOaQnOe0ouWSjOevqemBuCAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgZGFyazp0ZXh0LWdyYXktMzAwIG1iLTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIOaQnOe0ouaIkOWToVxuICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17YWRkRXhpc3RpbmdTZWFyY2hUZXJtfVxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHNldEFkZEV4aXN0aW5nU2VhcmNoVGVybShlLnRhcmdldC52YWx1ZSlcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0Q3VycmVudFBhZ2UoMSlcbiAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIui8uOWFpeaIkOWToeWnk+WQjVwiXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtMyBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgZGFyazpib3JkZXItZ3JheS02MDAgcm91bmRlZC1tZCBkYXJrOmJnLWdyYXktNzAwIGRhcms6dGV4dC13aGl0ZVwiXG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBkYXJrOnRleHQtZ3JheS0zMDAgbWItMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAg6IG36Yqc56+p6YG4XG4gICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17YWRkRXhpc3RpbmdDYXRlZ29yeUZpbHRlcn1cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRBZGRFeGlzdGluZ0NhdGVnb3J5RmlsdGVyKGUudGFyZ2V0LnZhbHVlKVxuICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRDdXJyZW50UGFnZSgxKVxuICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCBkYXJrOmJvcmRlci1ncmF5LTYwMCByb3VuZGVkLW1kIGRhcms6YmctZ3JheS03MDAgZGFyazp0ZXh0LXdoaXRlXCJcbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiXCI+5YWo6YOo6IG36YqcPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgICAgICB7YWxsQ2F0ZWdvcmllcy5tYXAoKGNhdGVnb3J5KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24ga2V5PXtjYXRlZ29yeX0gdmFsdWU9e2NhdGVnb3J5fT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y2F0ZWdvcnl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIHsvKiDmibnph4/mk43kvZwgKi99XG4gICAgICAgICAgICAgICAgICB7c2VsZWN0ZWRQYXJ0aWNpcGFudHMuc2l6ZSA+IDAgJiYgKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBwLTMgYmctYmx1ZS01MCBkYXJrOmJnLWJsdWUtOTAwLzIwIHJvdW5kZWQtbWRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtYmx1ZS04MDAgZGFyazp0ZXh0LWJsdWUtMjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICDlt7Lpgbjmk4cge3NlbGVjdGVkUGFydGljaXBhbnRzLnNpemV9IOS9jeaIkOWToVxuICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNlbGVjdGVkUGFydGljaXBhbnRzKG5ldyBTZXQoKSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTMgcHktMSBiZy1ncmF5LTUwMCB0ZXh0LXdoaXRlIHJvdW5kZWQgdGV4dC1zbSBob3ZlcjpiZy1ncmF5LTYwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIOa4hemZpOmBuOaTh1xuICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUJhdGNoQWRkVG9TZXNzaW9ufVxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC0zIHB5LTEgYmctZ3JlZW4tNTAwIHRleHQtd2hpdGUgcm91bmRlZCB0ZXh0LXNtIGhvdmVyOmJnLWdyZWVuLTYwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIOaJuemHj+a3u+WKoFxuICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICAgICAgey8qIOaIkOWToeWIl+ihqCAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBzbTpncmlkLWNvbHMtMiBtZDpncmlkLWNvbHMtMyBsZzpncmlkLWNvbHMtNCBnYXAtM1wiPlxuICAgICAgICAgICAgICAgICAgICB7ZmlsdGVyZWRBdmFpbGFibGVQYXJ0aWNpcGFudHMucGFydGljaXBhbnRzLm1hcCgocGFydGljaXBhbnQpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgICAgICBrZXk9e3BhcnRpY2lwYW50LmlkfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgICAgICAgICAgICAgXCJwLTMgYm9yZGVyIHJvdW5kZWQtbWQgY3Vyc29yLXBvaW50ZXIgdHJhbnNpdGlvbi1jb2xvcnNcIixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgc2VsZWN0ZWRQYXJ0aWNpcGFudHMuaGFzKHBhcnRpY2lwYW50LmlkKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJib3JkZXItYmx1ZS01MDAgYmctYmx1ZS01MCBkYXJrOmJnLWJsdWUtOTAwLzIwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwiYm9yZGVyLWdyYXktMjAwIGRhcms6Ym9yZGVyLWdyYXktNjAwIGhvdmVyOmJnLWdyYXktNTAgZGFyazpob3ZlcjpiZy1ncmF5LTY1MFwiLFxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgbmV3U2VsZWN0ZWQgPSBuZXcgU2V0KHNlbGVjdGVkUGFydGljaXBhbnRzKVxuICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAobmV3U2VsZWN0ZWQuaGFzKHBhcnRpY2lwYW50LmlkKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5ld1NlbGVjdGVkLmRlbGV0ZShwYXJ0aWNpcGFudC5pZClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBuZXdTZWxlY3RlZC5hZGQocGFydGljaXBhbnQuaWQpXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0U2VsZWN0ZWRQYXJ0aWNpcGFudHMobmV3U2VsZWN0ZWQpXG4gICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1zdGFydCBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LXdoaXRlXCI+e3BhcnRpY2lwYW50Lm5hbWV9PC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtwYXJ0aWNpcGFudC5jYXRlZ29yeSAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDAgZGFyazp0ZXh0LWdyYXktNDAwXCI+6IG36YqcOiB7cGFydGljaXBhbnQuY2F0ZWdvcnl9PC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgeygoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBwYXJ0aWNpcGFudFN0YXQgPSBzdGF0aXN0aWNzLnBhcnRpY2lwYW50U3RhdHMuZmluZChwID0+IHAuaWQgPT09IHBhcnRpY2lwYW50LmlkKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKCFwYXJ0aWNpcGFudFN0YXQpIHJldHVybiBudWxsXG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMSBzcGFjZS15LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICDnuL3pq5Q6IHtwYXJ0aWNpcGFudFN0YXQucGFydGljaXBhdGlvbkRldGFpbHMucGFydGljaXBhdGlvblJhdGlvfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3BhcnRpY2lwYW50U3RhdC5pc0hpZ2hQYXJ0aWNpcGF0aW9uICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwicHgtMSBweS0wLjUgYmctb3JhbmdlLTEwMCBkYXJrOmJnLW9yYW5nZS05MDAgdGV4dC1vcmFuZ2UtODAwIGRhcms6dGV4dC1vcmFuZ2UtMjAwIHRleHQteHMgcm91bmRlZFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIOmrmOWPg+iIh1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtzZWxlY3RlZFNlc3Npb25JZCAmJiBwYXJ0aWNpcGFudFN0YXQuc2Vzc2lvblBhcnRpY2lwYXRpb25EZXRhaWxzW3NlbGVjdGVkU2Vzc2lvbklkXSAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtYmx1ZS02MDAgZGFyazp0ZXh0LWJsdWUtNDAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIOacrOWxhjoge3BhcnRpY2lwYW50U3RhdC5zZXNzaW9uUGFydGljaXBhdGlvbkRldGFpbHNbc2VsZWN0ZWRTZXNzaW9uSWRdLnBhcnRpY2lwYXRpb25SYXRpb31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KSgpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImNoZWNrYm94XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtzZWxlY3RlZFBhcnRpY2lwYW50cy5oYXMocGFydGljaXBhbnQuaWQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoKSA9PiB7fX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtbC0yXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICB7Lyog5YiG6aCBICovfVxuICAgICAgICAgICAgICAgICAge2ZpbHRlcmVkQXZhaWxhYmxlUGFydGljaXBhbnRzLnRvdGFsUGFnZXMgPiAxICYmIChcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMCBkYXJrOnRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIOmhr+ekuiB7KGN1cnJlbnRQYWdlIC0gMSkgKiBwYWdlU2l6ZSArIDF9IC17XCIgXCJ9XG4gICAgICAgICAgICAgICAgICAgICAgICB7TWF0aC5taW4oY3VycmVudFBhZ2UgKiBwYWdlU2l6ZSwgZmlsdGVyZWRBdmFpbGFibGVQYXJ0aWNpcGFudHMudG90YWwpfSAve1wiIFwifVxuICAgICAgICAgICAgICAgICAgICAgICAge2ZpbHRlcmVkQXZhaWxhYmxlUGFydGljaXBhbnRzLnRvdGFsfSDkvY3miJDlk6FcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEN1cnJlbnRQYWdlKE1hdGgubWF4KDEsIGN1cnJlbnRQYWdlIC0gMSkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17Y3VycmVudFBhZ2UgPT09IDF9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTMgcHktMSBiZy1ncmF5LTIwMCBkYXJrOmJnLWdyYXktNjAwIHRleHQtZ3JheS03MDAgZGFyazp0ZXh0LWdyYXktMjAwIHJvdW5kZWQgdGV4dC1zbSBob3ZlcjpiZy1ncmF5LTMwMCBkYXJrOmhvdmVyOmJnLWdyYXktNTAwIGRpc2FibGVkOm9wYWNpdHktNTAgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkXCJcbiAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAg5LiK5LiA6aCBXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInB4LTMgcHktMSB0ZXh0LXNtIHRleHQtZ3JheS03MDAgZGFyazp0ZXh0LWdyYXktMzAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtjdXJyZW50UGFnZX0gLyB7ZmlsdGVyZWRBdmFpbGFibGVQYXJ0aWNpcGFudHMudG90YWxQYWdlc31cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRDdXJyZW50UGFnZShNYXRoLm1pbihmaWx0ZXJlZEF2YWlsYWJsZVBhcnRpY2lwYW50cy50b3RhbFBhZ2VzLCBjdXJyZW50UGFnZSArIDEpKVxuICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtjdXJyZW50UGFnZSA9PT0gZmlsdGVyZWRBdmFpbGFibGVQYXJ0aWNpcGFudHMudG90YWxQYWdlc31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtMyBweS0xIGJnLWdyYXktMjAwIGRhcms6YmctZ3JheS02MDAgdGV4dC1ncmF5LTcwMCBkYXJrOnRleHQtZ3JheS0yMDAgcm91bmRlZCB0ZXh0LXNtIGhvdmVyOmJnLWdyYXktMzAwIGRhcms6aG92ZXI6YmctZ3JheS01MDAgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICDkuIvkuIDpoIFcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgICAgIHtmaWx0ZXJlZEF2YWlsYWJsZVBhcnRpY2lwYW50cy5wYXJ0aWNpcGFudHMubGVuZ3RoID09PSAwICYmIChcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS04IHRleHQtZ3JheS01MDAgZGFyazp0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAge2F2YWlsYWJsZUdsb2JhbFBhcnRpY2lwYW50cy5sZW5ndGggPiAwID8gXCLmspLmnInmib7liLDnrKblkIjnr6npgbjmop3ku7bnmoTmiJDlk6FcIiA6IFwi5rKS5pyJ5Y+v5re75Yqg55qE5oiQ5ZOhXCJ9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG4gICAgICAgIDwvPlxuICAgICAgKSA6IChcbiAgICAgICAgLyog5YWo5bGA5Y+D5Yqg6ICF6KaW5ZyWICovXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgZGFyazpiZy1ncmF5LTcwMCByb3VuZGVkLWxnIHNoYWRvdyBvdmVyZmxvdy1oaWRkZW5cIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInB4LTYgcHktNCBib3JkZXItYiBib3JkZXItZ3JheS0yMDAgZGFyazpib3JkZXItZ3JheS02MDBcIj5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgIOaJgOacieaIkOWToeWIl+ihqCAoe2FsbFBhcnRpY2lwYW50cy5sZW5ndGh9KVxuICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm92ZXJmbG93LXgtYXV0b1wiPlxuICAgICAgICAgICAgPHRhYmxlIGNsYXNzTmFtZT1cIm1pbi13LWZ1bGwgZGl2aWRlLXkgZGl2aWRlLWdyYXktMjAwIGRhcms6ZGl2aWRlLWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgIDx0aGVhZCBjbGFzc05hbWU9XCJiZy1ncmF5LTUwIGRhcms6YmctZ3JheS04MDBcIj5cbiAgICAgICAgICAgICAgICA8dHI+XG4gICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS0zIHRleHQtbGVmdCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgZGFyazp0ZXh0LWdyYXktMzAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPlxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlU29ydChcIm5hbWVcIil9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0xIGhvdmVyOmJnLWdyYXktMTAwIGRhcms6aG92ZXI6YmctZ3JheS03MDAgcHgtMiBweS0xIHJvdW5kZWQgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4+5aeT5ZCNPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge3NvcnRGaWVsZCA9PT0gXCJuYW1lXCIgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHNvcnREaXJlY3Rpb24gPT09IFwiYXNjXCIgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGhcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VMaW5lam9pbj1cInJvdW5kXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZVdpZHRoPXsyfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZD1cIk01IDE1bDctNyA3IDdcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTUwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlTGluZWNhcD1cInJvdW5kXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9ezJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkPVwiTTE5IDlsLTcgNy03LTdcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTUwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKVxuICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGhcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VMaW5lY2FwPVwicm91bmRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZVdpZHRoPXsyfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGQ9XCJNOCA5bDQtNCA0IDRtMCA2bC00IDQtNC00XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTMgdGV4dC1sZWZ0IHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMCBkYXJrOnRleHQtZ3JheS0zMDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICAgIOWPg+iIh+WxhuWIpVxuICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTMgdGV4dC1yaWdodCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgZGFyazp0ZXh0LWdyYXktMzAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPlxuICAgICAgICAgICAgICAgICAgICDmk43kvZxcbiAgICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgPC90cj5cbiAgICAgICAgICAgICAgPC90aGVhZD5cbiAgICAgICAgICAgICAgPHRib2R5IGNsYXNzTmFtZT1cImJnLXdoaXRlIGRhcms6YmctZ3JheS03MDAgZGl2aWRlLXkgZGl2aWRlLWdyYXktMjAwIGRhcms6ZGl2aWRlLWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAge3NvcnRlZEFsbFBhcnRpY2lwYW50cy5tYXAoKHBhcnRpY2lwYW50KSA9PiB7XG4gICAgICAgICAgICAgICAgICBjb25zdCBoaXN0b3J5ID0gZ2V0UGFydGljaXBhbnRIaXN0b3J5KHBhcnRpY2lwYW50LmlkKVxuICAgICAgICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgICAgICAgPHRyIGtleT17cGFydGljaXBhbnQuaWR9IGNsYXNzTmFtZT1cImhvdmVyOmJnLWdyYXktNTAgZGFyazpob3ZlcjpiZy1ncmF5LTY1MFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXAgdGV4dC1zbSB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge3BhcnRpY2lwYW50Lm5hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00IHRleHQtc20gdGV4dC1ncmF5LTkwMCBkYXJrOnRleHQtd2hpdGVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXdyYXAgZ2FwLTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2hpc3RvcnkubWFwKChoKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17aC5pZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTIgcHktMSBiZy1ibHVlLTEwMCBkYXJrOmJnLWJsdWUtOTAwIHRleHQtYmx1ZS04MDAgZGFyazp0ZXh0LWJsdWUtMjAwIHRleHQteHMgcm91bmRlZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZT17YCR7aC5zZXNzaW9uTmFtZX06ICR7aC5jYXRlZ29yeX1gfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtoLnNlc3Npb25OYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtoaXN0b3J5Lmxlbmd0aCA9PT0gMCAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwIGRhcms6dGV4dC1ncmF5LTQwMFwiPuacquWPg+iIh+S7u+S9leWxhuWIpTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNCB3aGl0ZXNwYWNlLW5vd3JhcCB0ZXh0LXJpZ2h0IHRleHQtc20gZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0Vmlld2luZ0F0dGVuZGFuY2VIaXN0b3J5KHBhcnRpY2lwYW50KX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTYwMCBkYXJrOnRleHQtYmx1ZS00MDAgaG92ZXI6dGV4dC1ibHVlLTkwMCBkYXJrOmhvdmVyOnRleHQtYmx1ZS0zMDAgbXItNFwiXG4gICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIOWHuuW4reiomOmMhFxuICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEVkaXRpbmdQYXJ0aWNpcGFudChwYXJ0aWNpcGFudCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtaW5kaWdvLTYwMCBkYXJrOnRleHQtaW5kaWdvLTQwMCBob3Zlcjp0ZXh0LWluZGlnby05MDAgZGFyazpob3Zlcjp0ZXh0LWluZGlnby0zMDAgbXItNFwiXG4gICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIOe3qOi8r1xuICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAod2luZG93LmNvbmZpcm0oYOeiuuWumuimgeWIqumZpOWPg+WKoOiAhSBcIiR7cGFydGljaXBhbnQubmFtZX1cIiDll47vvJ/pgJnlsIfliKrpmaTmiYDmnInnm7jpl5zmlbjmk5rjgIJgKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25EZWxldGVQYXJ0aWNpcGFudChwYXJ0aWNpcGFudC5pZClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtcmVkLTYwMCBkYXJrOnRleHQtcmVkLTQwMCBob3Zlcjp0ZXh0LXJlZC05MDAgZGFyazpob3Zlcjp0ZXh0LXJlZC0zMDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICDliKrpmaRcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICAgIDwvdHI+XG4gICAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgICAgfSl9XG4gICAgICAgICAgICAgIDwvdGJvZHk+XG4gICAgICAgICAgICA8L3RhYmxlPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHsvKiDmlrDlop7lj4PliqDogIXooajllq4gKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIGRhcms6YmctZ3JheS03MDAgcm91bmRlZC1sZyBzaGFkb3cgcC00XCI+XG4gICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LXdoaXRlIG1iLTRcIj5cbiAgICAgICAgICB7ZWRpdGluZ1BhcnRpY2lwYW50ID8gXCLnt6jovK/miJDlk6FcIiA6IGVkaXRpbmdTZXNzaW9uUGFydGljaXBhbnQgPyBcIue3qOi8r+WxhuWIpeWPg+WKoOiAhVwiIDogXCLmlrDlop7lj4PliqDogIVcIn1cbiAgICAgICAgPC9oMz5cblxuICAgICAgICB7ZWRpdGluZ1Nlc3Npb25QYXJ0aWNpcGFudCA/IChcbiAgICAgICAgICAvKiDnt6jovK/lsYbliKXlj4PliqDogIXooajllq4gKi9cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgZGFyazp0ZXh0LWdyYXktMzAwIG1iLTFcIj7lp5PlkI08L2xhYmVsPlxuICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgdmFsdWU9e2VkaXRpbmdTZXNzaW9uUGFydGljaXBhbnQubmFtZX1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEVkaXRpbmdTZXNzaW9uUGFydGljaXBhbnQoeyAuLi5lZGl0aW5nU2Vzc2lvblBhcnRpY2lwYW50LCBuYW1lOiBlLnRhcmdldC52YWx1ZSB9KX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtMyBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgZGFyazpib3JkZXItZ3JheS02MDAgcm91bmRlZC1tZCBkYXJrOmJnLWdyYXktNzAwIGRhcms6dGV4dC13aGl0ZVwiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgZGFyazp0ZXh0LWdyYXktMzAwIG1iLTFcIj7ogbfpipw8L2xhYmVsPlxuICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgdmFsdWU9e2VkaXRpbmdTZXNzaW9uUGFydGljaXBhbnQuY2F0ZWdvcnl9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PlxuICAgICAgICAgICAgICAgICAgc2V0RWRpdGluZ1Nlc3Npb25QYXJ0aWNpcGFudCh7IC4uLmVkaXRpbmdTZXNzaW9uUGFydGljaXBhbnQsIGNhdGVnb3J5OiBlLnRhcmdldC52YWx1ZSB9KVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtMyBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgZGFyazpib3JkZXItZ3JheS02MDAgcm91bmRlZC1tZCBkYXJrOmJnLWdyYXktNzAwIGRhcms6dGV4dC13aGl0ZVwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiXCI+6YG45pOH6IG36YqcPC9vcHRpb24+XG4gICAgICAgICAgICAgICAge2FsbENhdGVnb3JpZXMubWFwKChjYXRlZ29yeSkgPT4gKFxuICAgICAgICAgICAgICAgICAgPG9wdGlvbiBrZXk9e2NhdGVnb3J5fSB2YWx1ZT17Y2F0ZWdvcnl9PlxuICAgICAgICAgICAgICAgICAgICB7Y2F0ZWdvcnl9XG4gICAgICAgICAgICAgICAgICA8L29wdGlvbj5cbiAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgZGFyazp0ZXh0LWdyYXktMzAwIG1iLTFcIj7liqDlhaXml6XmnJ88L2xhYmVsPlxuICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICB0eXBlPVwiZGF0ZVwiXG4gICAgICAgICAgICAgICAgdmFsdWU9e2VkaXRpbmdTZXNzaW9uUGFydGljaXBhbnQuam9pbkRhdGUgfHwgXCJcIn1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+XG4gICAgICAgICAgICAgICAgICBzZXRFZGl0aW5nU2Vzc2lvblBhcnRpY2lwYW50KHsgLi4uZWRpdGluZ1Nlc3Npb25QYXJ0aWNpcGFudCwgam9pbkRhdGU6IGUudGFyZ2V0LnZhbHVlIH0pXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCBkYXJrOmJvcmRlci1ncmF5LTYwMCByb3VuZGVkLW1kIGRhcms6YmctZ3JheS03MDAgZGFyazp0ZXh0LXdoaXRlXCJcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJjaGVja2JveFwiXG4gICAgICAgICAgICAgICAgICBjaGVja2VkPXtlZGl0aW5nU2Vzc2lvblBhcnRpY2lwYW50LmlzQWN0aXZlfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PlxuICAgICAgICAgICAgICAgICAgICBzZXRFZGl0aW5nU2Vzc2lvblBhcnRpY2lwYW50KHsgLi4uZWRpdGluZ1Nlc3Npb25QYXJ0aWNpcGFudCwgaXNBY3RpdmU6IGUudGFyZ2V0LmNoZWNrZWQgfSlcbiAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1yLTJcIlxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNzAwIGRhcms6dGV4dC1ncmF5LTMwMFwiPuWcqOatpOWxhuWIpeS4rea0u+i6jTwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApIDogKFxuICAgICAgICAgIC8qIOaWsOWini/nt6jovK/lhajlsYDlj4PliqDogIXooajllq4gKi9cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgZGFyazp0ZXh0LWdyYXktMzAwIG1iLTFcIj7lp5PlkI08L2xhYmVsPlxuICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgdmFsdWU9e2VkaXRpbmdQYXJ0aWNpcGFudCA/IGVkaXRpbmdQYXJ0aWNpcGFudC5uYW1lIDogbmV3UGFydGljaXBhbnQubmFtZX1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+XG4gICAgICAgICAgICAgICAgICBlZGl0aW5nUGFydGljaXBhbnRcbiAgICAgICAgICAgICAgICAgICAgPyBzZXRFZGl0aW5nUGFydGljaXBhbnQoeyAuLi5lZGl0aW5nUGFydGljaXBhbnQsIG5hbWU6IGUudGFyZ2V0LnZhbHVlIH0pXG4gICAgICAgICAgICAgICAgICAgIDogc2V0TmV3UGFydGljaXBhbnQoeyAuLi5uZXdQYXJ0aWNpcGFudCwgbmFtZTogZS50YXJnZXQudmFsdWUgfSlcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLovLjlhaXlj4PliqDogIXlp5PlkI1cIlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCBkYXJrOmJvcmRlci1ncmF5LTYwMCByb3VuZGVkLW1kIGRhcms6YmctZ3JheS03MDAgZGFyazp0ZXh0LXdoaXRlXCJcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBkYXJrOnRleHQtZ3JheS0zMDAgbWItMVwiPum7mOiqjeiBt+mKnDwvbGFiZWw+XG4gICAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgICB2YWx1ZT17ZWRpdGluZ1BhcnRpY2lwYW50ID8gZWRpdGluZ1BhcnRpY2lwYW50LmNhdGVnb3J5IHx8IFwiXCIgOiBuZXdQYXJ0aWNpcGFudC5jYXRlZ29yeX1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+XG4gICAgICAgICAgICAgICAgICBlZGl0aW5nUGFydGljaXBhbnRcbiAgICAgICAgICAgICAgICAgICAgPyBzZXRFZGl0aW5nUGFydGljaXBhbnQoeyAuLi5lZGl0aW5nUGFydGljaXBhbnQsIGNhdGVnb3J5OiBlLnRhcmdldC52YWx1ZSB9KVxuICAgICAgICAgICAgICAgICAgICA6IHNldE5ld1BhcnRpY2lwYW50KHsgLi4ubmV3UGFydGljaXBhbnQsIGNhdGVnb3J5OiBlLnRhcmdldC52YWx1ZSB9KVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtMyBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgZGFyazpib3JkZXItZ3JheS02MDAgcm91bmRlZC1tZCBkYXJrOmJnLWdyYXktNzAwIGRhcms6dGV4dC13aGl0ZVwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiXCI+6YG45pOH6IG36YqcPC9vcHRpb24+XG4gICAgICAgICAgICAgICAge2FsbENhdGVnb3JpZXMubWFwKChjYXRlZ29yeSkgPT4gKFxuICAgICAgICAgICAgICAgICAgPG9wdGlvbiBrZXk9e2NhdGVnb3J5fSB2YWx1ZT17Y2F0ZWdvcnl9PlxuICAgICAgICAgICAgICAgICAgICB7Y2F0ZWdvcnl9XG4gICAgICAgICAgICAgICAgICA8L29wdGlvbj5cbiAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwi5paw5aKe6IG36YqcXCI+KyDmlrDlop7ogbfpipw8L29wdGlvbj5cbiAgICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIHsoZWRpdGluZ1BhcnRpY2lwYW50Py5jYXRlZ29yeSA9PT0gXCLmlrDlop7ogbfpipxcIiB8fCBuZXdQYXJ0aWNpcGFudC5jYXRlZ29yeSA9PT0gXCLmlrDlop7ogbfpipxcIikgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1kOmNvbC1zcGFuLTJcIj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIGRhcms6dGV4dC1ncmF5LTMwMCBtYi0xXCI+5paw6IG36Yqc5ZCN56ixPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi6Ly45YWl5paw6IG36Yqc5ZCN56ixXCJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCBkYXJrOmJvcmRlci1ncmF5LTYwMCByb3VuZGVkLW1kIGRhcms6YmctZ3JheS03MDAgZGFyazp0ZXh0LXdoaXRlXCJcbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT5cbiAgICAgICAgICAgICAgICAgICAgZWRpdGluZ1BhcnRpY2lwYW50XG4gICAgICAgICAgICAgICAgICAgICAgPyBzZXRFZGl0aW5nUGFydGljaXBhbnQoeyAuLi5lZGl0aW5nUGFydGljaXBhbnQsIGNhdGVnb3J5OiBlLnRhcmdldC52YWx1ZSB9KVxuICAgICAgICAgICAgICAgICAgICAgIDogc2V0TmV3UGFydGljaXBhbnQoeyAuLi5uZXdQYXJ0aWNpcGFudCwgY2F0ZWdvcnk6IGUudGFyZ2V0LnZhbHVlIH0pXG4gICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNCBmbGV4IGp1c3RpZnktZW5kIHNwYWNlLXgtM1wiPlxuICAgICAgICAgIHsoZWRpdGluZ1BhcnRpY2lwYW50IHx8IGVkaXRpbmdTZXNzaW9uUGFydGljaXBhbnQpICYmIChcbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgIHNldEVkaXRpbmdQYXJ0aWNpcGFudChudWxsKVxuICAgICAgICAgICAgICAgIHNldEVkaXRpbmdTZXNzaW9uUGFydGljaXBhbnQobnVsbClcbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNCBweS0yIGJnLWdyYXktMjAwIGRhcms6YmctZ3JheS02MDAgdGV4dC1ncmF5LTcwMCBkYXJrOnRleHQtZ3JheS0yMDAgcm91bmRlZC1tZCBob3ZlcjpiZy1ncmF5LTMwMCBkYXJrOmhvdmVyOmJnLWdyYXktNTAwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAg5Y+W5raIXG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICApfVxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIG9uQ2xpY2s9e1xuICAgICAgICAgICAgICBlZGl0aW5nU2Vzc2lvblBhcnRpY2lwYW50XG4gICAgICAgICAgICAgICAgPyBoYW5kbGVVcGRhdGVTZXNzaW9uUGFydGljaXBhbnRcbiAgICAgICAgICAgICAgICA6IGVkaXRpbmdQYXJ0aWNpcGFudFxuICAgICAgICAgICAgICAgICAgPyBoYW5kbGVVcGRhdGVQYXJ0aWNpcGFudFxuICAgICAgICAgICAgICAgICAgOiBoYW5kbGVBZGRQYXJ0aWNpcGFudFxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNCBweS0yIGJnLWdyZWVuLTUwMCB0ZXh0LXdoaXRlIHJvdW5kZWQtbWQgaG92ZXI6YmctZ3JlZW4tNjAwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgIGRpc2FibGVkPXtcbiAgICAgICAgICAgICAgZWRpdGluZ1Nlc3Npb25QYXJ0aWNpcGFudFxuICAgICAgICAgICAgICAgID8gIWVkaXRpbmdTZXNzaW9uUGFydGljaXBhbnQubmFtZS50cmltKCkgfHwgIWVkaXRpbmdTZXNzaW9uUGFydGljaXBhbnQuY2F0ZWdvcnlcbiAgICAgICAgICAgICAgICA6IGVkaXRpbmdQYXJ0aWNpcGFudFxuICAgICAgICAgICAgICAgICAgPyAhZWRpdGluZ1BhcnRpY2lwYW50Lm5hbWUudHJpbSgpXG4gICAgICAgICAgICAgICAgICA6ICFuZXdQYXJ0aWNpcGFudC5uYW1lLnRyaW0oKVxuICAgICAgICAgICAgfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIHtlZGl0aW5nU2Vzc2lvblBhcnRpY2lwYW50ID8gXCLmm7TmlrDlsYbliKXlj4PliqDogIVcIiA6IGVkaXRpbmdQYXJ0aWNpcGFudCA/IFwi5pu05paw5oiQ5ZOhXCIgOiBcIuaWsOWinuWPg+WKoOiAhVwifVxuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7Lyog57Wx6KiI5L+h5oGvICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBkYXJrOmJnLWdyYXktNzAwIHJvdW5kZWQtbGcgc2hhZG93IHAtNFwiPlxuICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC13aGl0ZSBtYi00XCI+57Wx6KiI5L+h5oGvPC9oMz5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy00IGdhcC00XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtYmx1ZS02MDAgZGFyazp0ZXh0LWJsdWUtNDAwXCI+e2FsbFBhcnRpY2lwYW50cy5sZW5ndGh9PC9wPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwIGRhcms6dGV4dC1ncmF5LTQwMFwiPuaJgOacieaIkOWToee4veaVuDwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmVlbi02MDAgZGFyazp0ZXh0LWdyZWVuLTQwMFwiPntjdXJyZW50U2Vzc2lvblBhcnRpY2lwYW50cy5sZW5ndGh9PC9wPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwIGRhcms6dGV4dC1ncmF5LTQwMFwiPueVtuWJjeWxhuWIpeWPg+WKoOiAhTwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1wdXJwbGUtNjAwIGRhcms6dGV4dC1wdXJwbGUtNDAwXCI+XG4gICAgICAgICAgICAgIHtjdXJyZW50U2Vzc2lvblBhcnRpY2lwYW50cy5maWx0ZXIoKHNwKSA9PiBzcC5pc0FjdGl2ZSkubGVuZ3RofVxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwIGRhcms6dGV4dC1ncmF5LTQwMFwiPua0u+i6jeWPg+WKoOiAhTwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1vcmFuZ2UtNjAwIGRhcms6dGV4dC1vcmFuZ2UtNDAwXCI+e2N1cnJlbnRTZXNzaW9uQ2F0ZWdvcmllcy5sZW5ndGh9PC9wPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwIGRhcms6dGV4dC1ncmF5LTQwMFwiPueVtuWJjeWxhuWIpeiBt+mKnOaVuDwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIOWHuuW4reiomOmMhOaooeaFi+ahhiAqL31cbiAgICAgIHt2aWV3aW5nQXR0ZW5kYW5jZUhpc3RvcnkgJiYgKFxuICAgICAgICA8UGFydGljaXBhbnRBdHRlbmRhbmNlSGlzdG9yeVxuICAgICAgICAgIHBhcnRpY2lwYW50PXt2aWV3aW5nQXR0ZW5kYW5jZUhpc3Rvcnl9XG4gICAgICAgICAgYWN0aXZpdGllcz17YWN0aXZpdGllc31cbiAgICAgICAgICBzZXNzaW9uUGFydGljaXBhbnRzPXtzZXNzaW9uUGFydGljaXBhbnRzfVxuICAgICAgICAgIHNlc3Npb25zPXtzZXNzaW9uc31cbiAgICAgICAgICBvbkNsb3NlPXsoKSA9PiBzZXRWaWV3aW5nQXR0ZW5kYW5jZUhpc3RvcnkobnVsbCl9XG4gICAgICAgIC8+XG4gICAgICApfVxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VNZW1vIiwiY24iLCJVbml2ZXJzYWxCdWxrSW1wb3J0IiwiUGFydGljaXBhbnRBdHRlbmRhbmNlSGlzdG9yeSIsImdldEFjdGl2aXR5TGV2ZWwiLCJnZXRBY3Rpdml0eUxldmVsVGV4dCIsImdldEFjdGl2aXR5TGV2ZWxDb2xvciIsIkRFRkFVTFRfQUNUSVZJVFlfTEVWRUxfU0VUVElOR1MiLCJjYWxjdWxhdGVTdGF0aXN0aWNzIiwiUGFydGljaXBhbnRNYW5hZ2VtZW50UGFnZSIsImFsbFBhcnRpY2lwYW50cyIsInNlc3Npb25QYXJ0aWNpcGFudHMiLCJzZXNzaW9ucyIsInNlbGVjdGVkU2Vzc2lvbklkIiwib25BZGRQYXJ0aWNpcGFudCIsIm9uQWRkU2Vzc2lvblBhcnRpY2lwYW50Iiwib25CdWxrQWRkUGFydGljaXBhbnRzIiwib25VcGRhdGVQYXJ0aWNpcGFudCIsIm9uVXBkYXRlU2Vzc2lvblBhcnRpY2lwYW50Iiwib25EZWxldGVQYXJ0aWNpcGFudCIsIm9uUmVtb3ZlRnJvbVNlc3Npb24iLCJvbkJ1bGtEZWxldGVUaXRsZSIsIm9uQmFjayIsImFjdGl2aXRpZXMiLCJhY3Rpdml0eUxldmVsU2V0dGluZ3MiLCJuZXdQYXJ0aWNpcGFudCIsInNldE5ld1BhcnRpY2lwYW50IiwibmFtZSIsImNhdGVnb3J5IiwiZWRpdGluZ1BhcnRpY2lwYW50Iiwic2V0RWRpdGluZ1BhcnRpY2lwYW50IiwiZWRpdGluZ1Nlc3Npb25QYXJ0aWNpcGFudCIsInNldEVkaXRpbmdTZXNzaW9uUGFydGljaXBhbnQiLCJzZWFyY2hUZXJtIiwic2V0U2VhcmNoVGVybSIsImNhdGVnb3J5RmlsdGVyIiwic2V0Q2F0ZWdvcnlGaWx0ZXIiLCJzdGF0dXNGaWx0ZXIiLCJzZXRTdGF0dXNGaWx0ZXIiLCJzaG93QnVsa0ltcG9ydCIsInNldFNob3dCdWxrSW1wb3J0Iiwidmlld01vZGUiLCJzZXRWaWV3TW9kZSIsInZpZXdpbmdBdHRlbmRhbmNlSGlzdG9yeSIsInNldFZpZXdpbmdBdHRlbmRhbmNlSGlzdG9yeSIsInNob3dUaXRsZU1hbmFnZW1lbnQiLCJzZXRTaG93VGl0bGVNYW5hZ2VtZW50IiwiZWRpdGluZ1RpdGxlIiwic2V0RWRpdGluZ1RpdGxlIiwibmV3VGl0bGUiLCJzZXROZXdUaXRsZSIsInNob3dBZGRFeGlzdGluZyIsInNldFNob3dBZGRFeGlzdGluZyIsImFkZEV4aXN0aW5nU2VhcmNoVGVybSIsInNldEFkZEV4aXN0aW5nU2VhcmNoVGVybSIsImFkZEV4aXN0aW5nQ2F0ZWdvcnlGaWx0ZXIiLCJzZXRBZGRFeGlzdGluZ0NhdGVnb3J5RmlsdGVyIiwic2VsZWN0ZWRQYXJ0aWNpcGFudHMiLCJzZXRTZWxlY3RlZFBhcnRpY2lwYW50cyIsIlNldCIsImN1cnJlbnRQYWdlIiwic2V0Q3VycmVudFBhZ2UiLCJwYWdlU2l6ZSIsInNvcnRGaWVsZCIsInNldFNvcnRGaWVsZCIsInNvcnREaXJlY3Rpb24iLCJzZXRTb3J0RGlyZWN0aW9uIiwiY3VycmVudFNlc3Npb24iLCJmaW5kIiwicyIsImlkIiwiY3VycmVudFNlc3Npb25QYXJ0aWNpcGFudHMiLCJmaWx0ZXIiLCJzcCIsInNlc3Npb25JZCIsImN1cnJlbnRTZXNzaW9uQ2F0ZWdvcmllcyIsImNhdGVnb3JpZXMiLCJtYXAiLCJCb29sZWFuIiwiQXJyYXkiLCJmcm9tIiwiYWxsQ2F0ZWdvcmllcyIsImdsb2JhbENhdGVnb3JpZXMiLCJwIiwic2Vzc2lvbkNhdGVnb3JpZXMiLCJzdGF0aXN0aWNzIiwiaGFuZGxlU29ydCIsImZpZWxkIiwiZmlsdGVyZWRTZXNzaW9uUGFydGljaXBhbnRzIiwiZmlsdGVyZWQiLCJtYXRjaGVzU2VhcmNoIiwidG9Mb3dlckNhc2UiLCJpbmNsdWRlcyIsIm1hdGNoZXNDYXRlZ29yeSIsIm1hdGNoZXNTdGF0dXMiLCJpc0FjdGl2ZSIsInBhcnRpY2lwYW50c1dpdGhBY3Rpdml0eUxldmVsIiwicGFydGljaXBhbnRBY3Rpdml0aWVzIiwiYWN0aXZpdHkiLCJ0b3RhbFBhcnRpY2lwYXRlZCIsInRvdGFsQXR0ZW5kZWQiLCJmb3JFYWNoIiwicGFydGljaXBhbnRJbkFjdGl2aXR5IiwicGFydGljaXBhbnRzIiwicGFydGljaXBhbnRJZCIsImF0dGVuZGFuY2VSZWNvcmQiLCJhdHRlbmRhbmNlIiwiaXNBdHRlbmRlZCIsImRhdGUiLCJhdHRlbmRhbmNlUmF0ZSIsImFjdGl2aXR5TGV2ZWwiLCJzb3J0IiwiYSIsImIiLCJhVmFsdWUiLCJiVmFsdWUiLCJqb2luRGF0ZSIsImF2YWlsYWJsZUdsb2JhbFBhcnRpY2lwYW50cyIsInNlc3Npb25QYXJ0aWNpcGFudElkcyIsImhhcyIsImZpbHRlcmVkQXZhaWxhYmxlUGFydGljaXBhbnRzIiwicGFydGljaXBhbnQiLCJzdGFydEluZGV4IiwiZW5kSW5kZXgiLCJzbGljZSIsInRvdGFsIiwibGVuZ3RoIiwidG90YWxQYWdlcyIsIk1hdGgiLCJjZWlsIiwiaGFuZGxlQWRkUGFydGljaXBhbnQiLCJ0cmltIiwiZmluYWxDYXRlZ29yeSIsInBhcnRpY2lwYW50VG9BZGQiLCJjb25zb2xlIiwibG9nIiwiYWxlcnQiLCJlcnJvciIsIkVycm9yIiwibWVzc2FnZSIsImhhbmRsZVVwZGF0ZVBhcnRpY2lwYW50IiwiaGFuZGxlVXBkYXRlU2Vzc2lvblBhcnRpY2lwYW50IiwiaGFuZGxlQnVsa0ltcG9ydCIsImhhbmRsZUFkZFRvU2Vzc2lvbiIsIkRhdGUiLCJ0b0lTT1N0cmluZyIsInNwbGl0IiwiaGFuZGxlQmF0Y2hBZGRUb1Nlc3Npb24iLCJzaXplIiwiaGFuZGxlVXBkYXRlVGl0bGUiLCJvbGRUaXRsZSIsInNvbWUiLCJwYXJ0aWNpcGFudHNUb1VwZGF0ZSIsInN0YXJ0c1dpdGgiLCJwbGFjZWhvbGRlcnMiLCJzZXNzaW9uUGFydGljaXBhbnRzVG9VcGRhdGUiLCJ0b3RhbFVwZGF0ZXMiLCJoYXNQbGFjZWhvbGRlcnMiLCJ3aW5kb3ciLCJjb25maXJtIiwiYmF0Y2hVcGRhdGVQcm9taXNlcyIsInVwZGF0ZWRQYXJ0aWNpcGFudCIsIlByb21pc2UiLCJyZXNvbHZlIiwiYWxsIiwiY2F0Y2giLCJlcnIiLCJiYXRjaFVwZGF0ZVBsYWNlaG9sZGVyUHJvbWlzZXMiLCJwbGFjZWhvbGRlciIsInVwZGF0ZWRQbGFjZWhvbGRlciIsImJhdGNoVXBkYXRlU2Vzc2lvblByb21pc2VzIiwic2Vzc2lvblBhcnRpY2lwYW50IiwidXBkYXRlZFNlc3Npb25QYXJ0aWNpcGFudCIsImhhbmRsZURlbGV0ZVRpdGxlIiwidGl0bGUiLCJjb25maXJtTWVzc2FnZSIsInJlc3VsdCIsInN1Y2Nlc3NNZXNzYWdlIiwicGFydGljaXBhbnRzVXBkYXRlZCIsInBsYWNlaG9sZGVyc1JlbW92ZWQiLCJnZXRQYXJ0aWNpcGFudEhpc3RvcnkiLCJzZXNzaW9uTmFtZSIsImxvY2FsZUNvbXBhcmUiLCJzb3J0ZWRBbGxQYXJ0aWNpcGFudHMiLCJkaXYiLCJjbGFzc05hbWUiLCJidXR0b24iLCJvbkNsaWNrIiwiaDIiLCJzdmciLCJ4bWxucyIsImZpbGwiLCJ2aWV3Qm94Iiwic3Ryb2tlIiwicGF0aCIsInN0cm9rZUxpbmVjYXAiLCJzdHJva2VMaW5lam9pbiIsInN0cm9rZVdpZHRoIiwiZCIsInN0cm9uZyIsInZlcnlBY3RpdmUiLCJhY3RpdmUiLCJoMyIsImg0IiwicGFydGljaXBhdGlvblN0YXRzIiwiYXZlcmFnZVBhcnRpY2lwYXRpb25SYXRlIiwidG9GaXhlZCIsInNlc3Npb25QYXJ0aWNpcGF0aW9uU3RhdHMiLCJhY3RpdmVQYXJ0aWNpcGFudHMiLCJwYXJ0aWNpcGFudFN0YXRzIiwicGFydGljaXBhdGlvblJhdGUiLCJoaWdoUGFydGljaXBhdGlvblN0YXRzIiwiY291bnQiLCJ0aHJlc2hvbGQiLCJwZXJjZW50YWdlIiwibGFiZWwiLCJpbnB1dCIsInR5cGUiLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwiZSIsInRhcmdldCIsIm5ld1BhcnRpY2lwYW50V2l0aFRpdGxlIiwibm93IiwiZGlzYWJsZWQiLCJkZWZhdWx0VmFsdWUiLCJvbktleURvd24iLCJrZXkiLCJjdXJyZW50VGFyZ2V0IiwiYXV0b0ZvY3VzIiwic3BhbiIsImRhdGFUeXBlIiwib25JbXBvcnQiLCJvbkNhbmNlbCIsInN0YXJ0RGF0ZSIsImVuZERhdGUiLCJiciIsInNlbGVjdCIsIm9wdGlvbiIsInRhYmxlIiwidGhlYWQiLCJ0ciIsInRoIiwidGJvZHkiLCJoaXN0b3J5IiwidGQiLCJwYXJ0aWNpcGFudFN0YXQiLCJwYXJ0aWNpcGF0aW9uRGV0YWlscyIsInBhcnRpY2lwYXRpb25SYXRpbyIsImlzSGlnaFBhcnRpY2lwYXRpb24iLCJzZXNzaW9uUGFydGljaXBhdGlvbkRldGFpbHMiLCJoIiwiZ2xvYmFsUGFydGljaXBhbnQiLCJjb2xTcGFuIiwibmV3U2VsZWN0ZWQiLCJkZWxldGUiLCJhZGQiLCJjaGVja2VkIiwibWluIiwibWF4Iiwib25DbG9zZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/participant-management-page.tsx\n"));

/***/ })

});