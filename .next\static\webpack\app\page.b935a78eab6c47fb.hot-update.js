"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./hooks/use-participant-handlers.ts":
/*!*******************************************!*\
  !*** ./hooks/use-participant-handlers.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useParticipantHandlers: () => (/* binding */ useParticipantHandlers)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useParticipantHandlers auto */ \nfunction useParticipantHandlers(props) {\n    const { allParticipants, sessionParticipants, activities, setAllParticipants, setSessionParticipants, setActivities } = props;\n    const handleAddParticipant = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useParticipantHandlers.useCallback[handleAddParticipant]\": (newParticipant)=>{\n            // 使用時間戳和隨機數生成唯一ID，避免ID衝突\n            const id = \"\".concat(Date.now(), \"-\").concat(Math.random().toString(36).substr(2, 9));\n            const participant = {\n                id,\n                ...newParticipant,\n                attendance: {}\n            };\n            console.log('Adding participant to global list:', participant) // 調試日誌\n            ;\n            setAllParticipants([\n                ...allParticipants,\n                participant\n            ]);\n        }\n    }[\"useParticipantHandlers.useCallback[handleAddParticipant]\"], [\n        allParticipants,\n        setAllParticipants\n    ]);\n    const handleAddSessionParticipant = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useParticipantHandlers.useCallback[handleAddSessionParticipant]\": (newSessionParticipant)=>{\n            const id = (sessionParticipants.length + 1).toString();\n            const sessionParticipant = {\n                id,\n                ...newSessionParticipant\n            };\n            setSessionParticipants([\n                ...sessionParticipants,\n                sessionParticipant\n            ]);\n        }\n    }[\"useParticipantHandlers.useCallback[handleAddSessionParticipant]\"], [\n        sessionParticipants,\n        setSessionParticipants\n    ]);\n    const handleBulkAddParticipants = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useParticipantHandlers.useCallback[handleBulkAddParticipants]\": (newParticipants)=>{\n            let currentId = allParticipants.length + 1;\n            const participants = newParticipants.map({\n                \"useParticipantHandlers.useCallback[handleBulkAddParticipants].participants\": (p)=>{\n                    const participant = {\n                        id: currentId.toString(),\n                        ...p,\n                        attendance: {}\n                    };\n                    currentId++;\n                    return participant;\n                }\n            }[\"useParticipantHandlers.useCallback[handleBulkAddParticipants].participants\"]);\n            setAllParticipants([\n                ...allParticipants,\n                ...participants\n            ]);\n        }\n    }[\"useParticipantHandlers.useCallback[handleBulkAddParticipants]\"], [\n        allParticipants,\n        setAllParticipants\n    ]);\n    const handleUpdateParticipant = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useParticipantHandlers.useCallback[handleUpdateParticipant]\": (updatedParticipant)=>{\n            setAllParticipants(allParticipants.map({\n                \"useParticipantHandlers.useCallback[handleUpdateParticipant]\": (p)=>p.id === updatedParticipant.id ? updatedParticipant : p\n            }[\"useParticipantHandlers.useCallback[handleUpdateParticipant]\"]));\n            setSessionParticipants(sessionParticipants.map({\n                \"useParticipantHandlers.useCallback[handleUpdateParticipant]\": (sp)=>sp.participantId === updatedParticipant.id ? {\n                        ...sp,\n                        name: updatedParticipant.name\n                    } : sp\n            }[\"useParticipantHandlers.useCallback[handleUpdateParticipant]\"]));\n        }\n    }[\"useParticipantHandlers.useCallback[handleUpdateParticipant]\"], [\n        allParticipants,\n        sessionParticipants,\n        setAllParticipants,\n        setSessionParticipants\n    ]);\n    const handleUpdateSessionParticipant = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useParticipantHandlers.useCallback[handleUpdateSessionParticipant]\": (updatedSessionParticipant)=>{\n            setSessionParticipants(sessionParticipants.map({\n                \"useParticipantHandlers.useCallback[handleUpdateSessionParticipant]\": (sp)=>sp.id === updatedSessionParticipant.id ? updatedSessionParticipant : sp\n            }[\"useParticipantHandlers.useCallback[handleUpdateSessionParticipant]\"]));\n        }\n    }[\"useParticipantHandlers.useCallback[handleUpdateSessionParticipant]\"], [\n        sessionParticipants,\n        setSessionParticipants\n    ]);\n    const handleDeleteParticipant = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useParticipantHandlers.useCallback[handleDeleteParticipant]\": (participantId)=>{\n            setAllParticipants(allParticipants.filter({\n                \"useParticipantHandlers.useCallback[handleDeleteParticipant]\": (p)=>p.id !== participantId\n            }[\"useParticipantHandlers.useCallback[handleDeleteParticipant]\"]));\n            setSessionParticipants(sessionParticipants.filter({\n                \"useParticipantHandlers.useCallback[handleDeleteParticipant]\": (sp)=>sp.participantId !== participantId\n            }[\"useParticipantHandlers.useCallback[handleDeleteParticipant]\"]));\n            setActivities(activities.map({\n                \"useParticipantHandlers.useCallback[handleDeleteParticipant]\": (activity)=>({\n                        ...activity,\n                        participants: activity.participants.filter({\n                            \"useParticipantHandlers.useCallback[handleDeleteParticipant]\": (p)=>p.id !== participantId\n                        }[\"useParticipantHandlers.useCallback[handleDeleteParticipant]\"])\n                    })\n            }[\"useParticipantHandlers.useCallback[handleDeleteParticipant]\"]));\n        }\n    }[\"useParticipantHandlers.useCallback[handleDeleteParticipant]\"], [\n        allParticipants,\n        sessionParticipants,\n        activities,\n        setAllParticipants,\n        setSessionParticipants,\n        setActivities\n    ]);\n    const handleRemoveFromSession = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useParticipantHandlers.useCallback[handleRemoveFromSession]\": (sessionParticipantId)=>{\n            // 找到要移除的屆別參加者\n            const sessionParticipantToRemove = sessionParticipants.find({\n                \"useParticipantHandlers.useCallback[handleRemoveFromSession].sessionParticipantToRemove\": (sp)=>sp.id === sessionParticipantId\n            }[\"useParticipantHandlers.useCallback[handleRemoveFromSession].sessionParticipantToRemove\"]);\n            if (sessionParticipantToRemove) {\n                // 從屆別參加者列表中移除\n                setSessionParticipants(sessionParticipants.filter({\n                    \"useParticipantHandlers.useCallback[handleRemoveFromSession]\": (sp)=>sp.id !== sessionParticipantId\n                }[\"useParticipantHandlers.useCallback[handleRemoveFromSession]\"]));\n                // 從所有相關活動中移除該參加者\n                setActivities(activities.map({\n                    \"useParticipantHandlers.useCallback[handleRemoveFromSession]\": (activity)=>({\n                            ...activity,\n                            participants: activity.participants.filter({\n                                \"useParticipantHandlers.useCallback[handleRemoveFromSession]\": (p)=>p.id !== sessionParticipantToRemove.participantId\n                            }[\"useParticipantHandlers.useCallback[handleRemoveFromSession]\"])\n                        })\n                }[\"useParticipantHandlers.useCallback[handleRemoveFromSession]\"]));\n            }\n        }\n    }[\"useParticipantHandlers.useCallback[handleRemoveFromSession]\"], [\n        sessionParticipants,\n        activities,\n        setSessionParticipants,\n        setActivities\n    ]);\n    const handleBulkDeleteTitle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useParticipantHandlers.useCallback[handleBulkDeleteTitle]\": (title)=>{\n            // 找出所有使用該職銜的參加者\n            const participantsToUpdate = allParticipants.filter({\n                \"useParticipantHandlers.useCallback[handleBulkDeleteTitle].participantsToUpdate\": (p)=>p.category === title && !p.name.startsWith('職銜佔位符-')\n            }[\"useParticipantHandlers.useCallback[handleBulkDeleteTitle].participantsToUpdate\"]);\n            const placeholders = allParticipants.filter({\n                \"useParticipantHandlers.useCallback[handleBulkDeleteTitle].placeholders\": (p)=>p.category === title && p.name.startsWith('職銜佔位符-')\n            }[\"useParticipantHandlers.useCallback[handleBulkDeleteTitle].placeholders\"]);\n            const sessionParticipantsToUpdate = sessionParticipants.filter({\n                \"useParticipantHandlers.useCallback[handleBulkDeleteTitle].sessionParticipantsToUpdate\": (sp)=>sp.category === title\n            }[\"useParticipantHandlers.useCallback[handleBulkDeleteTitle].sessionParticipantsToUpdate\"]);\n            // 一次性更新所有狀態\n            // 更新全局參加者 - 清除職銜\n            const updatedAllParticipants = allParticipants.map({\n                \"useParticipantHandlers.useCallback[handleBulkDeleteTitle].updatedAllParticipants\": (p)=>{\n                    if (p.category === title && !p.name.startsWith('職銜佔位符-')) {\n                        return {\n                            ...p,\n                            category: \"\"\n                        };\n                    }\n                    return p;\n                }\n            }[\"useParticipantHandlers.useCallback[handleBulkDeleteTitle].updatedAllParticipants\"]).filter({\n                \"useParticipantHandlers.useCallback[handleBulkDeleteTitle].updatedAllParticipants\": (p)=>!p.name.startsWith(\"職銜佔位符-\".concat(title))\n            }[\"useParticipantHandlers.useCallback[handleBulkDeleteTitle].updatedAllParticipants\"]) // 移除佔位符\n            ;\n            // 更新屆別參加者 - 清除職銜\n            const updatedSessionParticipants = sessionParticipants.map({\n                \"useParticipantHandlers.useCallback[handleBulkDeleteTitle].updatedSessionParticipants\": (sp)=>{\n                    if (sp.category === title) {\n                        return {\n                            ...sp,\n                            category: \"\"\n                        };\n                    }\n                    return sp;\n                }\n            }[\"useParticipantHandlers.useCallback[handleBulkDeleteTitle].updatedSessionParticipants\"]);\n            // 從活動中移除佔位符參加者\n            const updatedActivities = activities.map({\n                \"useParticipantHandlers.useCallback[handleBulkDeleteTitle].updatedActivities\": (activity)=>({\n                        ...activity,\n                        participants: activity.participants.filter({\n                            \"useParticipantHandlers.useCallback[handleBulkDeleteTitle].updatedActivities\": (p)=>!placeholders.some({\n                                    \"useParticipantHandlers.useCallback[handleBulkDeleteTitle].updatedActivities\": (placeholder)=>placeholder.id === p.id\n                                }[\"useParticipantHandlers.useCallback[handleBulkDeleteTitle].updatedActivities\"])\n                        }[\"useParticipantHandlers.useCallback[handleBulkDeleteTitle].updatedActivities\"])\n                    })\n            }[\"useParticipantHandlers.useCallback[handleBulkDeleteTitle].updatedActivities\"]);\n            // 批量更新所有狀態\n            setAllParticipants(updatedAllParticipants);\n            setSessionParticipants(updatedSessionParticipants);\n            setActivities(updatedActivities);\n            return {\n                participantsUpdated: participantsToUpdate.length,\n                placeholdersRemoved: placeholders.length,\n                sessionParticipantsUpdated: sessionParticipantsToUpdate.length\n            };\n        }\n    }[\"useParticipantHandlers.useCallback[handleBulkDeleteTitle]\"], [\n        allParticipants,\n        sessionParticipants,\n        activities,\n        setAllParticipants,\n        setSessionParticipants,\n        setActivities\n    ]);\n    return {\n        handleAddParticipant,\n        handleAddSessionParticipant,\n        handleBulkAddParticipants,\n        handleUpdateParticipant,\n        handleUpdateSessionParticipant,\n        handleDeleteParticipant,\n        handleRemoveFromSession,\n        handleBulkDeleteTitle\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/use-participant-handlers.ts\n"));

/***/ })

});