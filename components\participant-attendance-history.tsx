"use client"

import { useMemo } from "react"
import { cn } from "@/lib/utils"
import { RegistrationStatus, type Activity, type Participant, type SessionParticipant, type Session } from "../types"

interface ParticipantAttendanceHistoryProps {
  participant: Participant
  activities: Activity[]
  sessionParticipants: SessionParticipant[]
  sessions: Session[]
  onClose: () => void
}

export function ParticipantAttendanceHistory({
  participant,
  activities,
  sessionParticipants,
  sessions,
  onClose,
}: ParticipantAttendanceHistoryProps) {
  // 獲取該參加者的所有屆別信息
  const participantSessions = useMemo(() => {
    return sessionParticipants
      .filter((sp) => sp.participantId === participant.id)
      .map((sp) => ({
        ...sp,
        sessionName: sessions.find((s) => s.id === sp.sessionId)?.name || "未知屆別",
        session: sessions.find((s) => s.id === sp.sessionId),
      }))
      .sort((a, b) => b.sessionId.localeCompare(a.sessionId))
  }, [participant.id, sessionParticipants, sessions])

  // 修改獲取報名狀態的邏輯
  const participantActivities = useMemo(() => {
    return activities
      .filter((activity) => {
        // 只包含該參加者有參與記錄的活動（報名或出席）
        const participantInActivity = activity.participants.find((p) => p.id === participant.id)
        if (!participantInActivity) return false

        const attended = participantInActivity.attendance[activity.date] || false
        const registered = participantInActivity.registration?.[activity.date] || false

        // 只有報名或出席的活動才有統計意義
        return registered || attended
      })
      .map((activity) => {
        const participantInActivity = activity.participants.find((p) => p.id === participant.id)
        const attended = participantInActivity?.attendance[activity.date] || false
        const registered = participantInActivity?.registration?.[activity.date] || false
        const sessionInfo = participantSessions.find((ps) => ps.sessionId === activity.sessionId)

        // 確定報名狀態
        let registrationStatus: RegistrationStatus
        if (attended) {
          registrationStatus = RegistrationStatus.ATTENDED
        } else if (registered) {
          registrationStatus = RegistrationStatus.NO_SHOW
        } else {
          // 出席但沒報名的情況（可能是現場報名或特殊情況）
          registrationStatus = RegistrationStatus.ATTENDED
        }

        return {
          ...activity,
          attended,
          registered,
          registrationStatus,
          sessionName: sessionInfo?.sessionName || "未知屆別",
          participantCategory: sessionInfo?.category || "未知類別",
        }
      })
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
  }, [activities, participant.id, participantSessions])

  // 按屆別分組活動
  const activitiesBySession = useMemo(() => {
    const grouped: Record<string, typeof participantActivities> = {}
    participantActivities.forEach((activity) => {
      if (!grouped[activity.sessionId]) {
        grouped[activity.sessionId] = []
      }
      grouped[activity.sessionId].push(activity)
    })
    return grouped
  }, [participantActivities])

  // 計算統計數據
  const statistics = useMemo(() => {
    const totalActivities = participantActivities.length
    const attendedActivities = participantActivities.filter((a) => a.attended).length
    const registeredActivities = participantActivities.filter((a) => a.registered).length
    const noShowActivities = participantActivities.filter((a) => a.registered && !a.attended).length

    const attendanceRate = totalActivities > 0 ? (attendedActivities / totalActivities) * 100 : 0
    const registrationRate = totalActivities > 0 ? (registeredActivities / totalActivities) * 100 : 0
    const attendanceFromRegistrationRate =
      registeredActivities > 0 ? (attendedActivities / registeredActivities) * 100 : 0
    const noShowRate = registeredActivities > 0 ? (noShowActivities / registeredActivities) * 100 : 0

    // 按屆別統計
    const sessionStats = Object.entries(activitiesBySession).map(([sessionId, activities]) => {
      const sessionInfo = participantSessions.find((ps) => ps.sessionId === sessionId)
      const attended = activities.filter((a) => a.attended).length
      const registered = activities.filter((a) => a.registered).length
      const noShows = activities.filter((a) => a.registered && !a.attended).length
      const total = activities.length

      const rate = total > 0 ? (attended / total) * 100 : 0
      const regRate = total > 0 ? (registered / total) * 100 : 0
      const attendFromRegRate = registered > 0 ? (attended / registered) * 100 : 0
      const noShowRateSession = registered > 0 ? (noShows / registered) * 100 : 0

      return {
        sessionId,
        sessionName: sessionInfo?.sessionName || "未知屆別",
        category: sessionInfo?.category || "未知類別",
        attended,
        registered,
        noShows,
        total,
        rate,
        registrationRate: regRate,
        attendanceFromRegistrationRate: attendFromRegRate,
        noShowRate: noShowRateSession,
        activities,
      }
    })

    // 按委員會統計
    const committeeStats: Record<
      string,
      {
        attended: number
        registered: number
        noShows: number
        total: number
        rate: number
        registrationRate: number
        attendanceFromRegistrationRate: number
        noShowRate: number
      }
    > = {}

    participantActivities.forEach((activity) => {
      if (!committeeStats[activity.committee]) {
        committeeStats[activity.committee] = {
          attended: 0,
          registered: 0,
          noShows: 0,
          total: 0,
          rate: 0,
          registrationRate: 0,
          attendanceFromRegistrationRate: 0,
          noShowRate: 0,
        }
      }
      committeeStats[activity.committee].total++
      if (activity.registered) {
        committeeStats[activity.committee].registered++
      }
      if (activity.attended) {
        committeeStats[activity.committee].attended++
      }
      if (activity.registered && !activity.attended) {
        committeeStats[activity.committee].noShows++
      }
    })

    Object.keys(committeeStats).forEach((committee) => {
      const stats = committeeStats[committee]
      stats.rate = stats.total > 0 ? (stats.attended / stats.total) * 100 : 0
      stats.registrationRate = stats.total > 0 ? (stats.registered / stats.total) * 100 : 0
      stats.attendanceFromRegistrationRate = stats.registered > 0 ? (stats.attended / stats.registered) * 100 : 0
      stats.noShowRate = stats.registered > 0 ? (stats.noShows / stats.registered) * 100 : 0
    })

    return {
      totalActivities,
      attendedActivities,
      registeredActivities,
      noShowActivities,
      attendanceRate,
      registrationRate,
      attendanceFromRegistrationRate,
      noShowRate,
      sessionStats,
      committeeStats,
    }
  }, [participantActivities, activitiesBySession, participantSessions])

  // 修改獲取報名狀態顯示信息的函數
  const getRegistrationStatusDisplay = (status: RegistrationStatus) => {
    switch (status) {
      case RegistrationStatus.REGISTERED_ONLY:
        return { text: "已報名", className: "bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200" }
      case RegistrationStatus.ATTENDED:
        return { text: "已出席", className: "bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200" }
      case RegistrationStatus.NO_SHOW:
        return { text: "爽約", className: "bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200" }
      default:
        return { text: "未知", className: "bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200" }
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-7xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* 標題 */}
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">{participant.name} 的活動參與記錄</h2>
            <button onClick={onClose} className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* 統計概覽 */}
          <div className="grid grid-cols-2 md:grid-cols-6 gap-4 mb-6">
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
              <h3 className="text-sm font-medium text-blue-600 dark:text-blue-400">總參與活動</h3>
              <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">{statistics.totalActivities}</p>
            </div>
            <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4">
              <h3 className="text-sm font-medium text-purple-600 dark:text-purple-400">報名活動</h3>
              <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                {statistics.registeredActivities}
              </p>
              <p className="text-xs text-purple-600 dark:text-purple-400">{statistics.registrationRate.toFixed(1)}%</p>
            </div>
            <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
              <h3 className="text-sm font-medium text-green-600 dark:text-green-400">出席活動</h3>
              <p className="text-2xl font-bold text-green-900 dark:text-green-100">{statistics.attendedActivities}</p>
              <p className="text-xs text-green-600 dark:text-green-400">{statistics.attendanceRate.toFixed(1)}%</p>
            </div>
            <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4">
              <h3 className="text-sm font-medium text-orange-600 dark:text-orange-400">報名後出席率</h3>
              <p className="text-2xl font-bold text-orange-900 dark:text-orange-100">
                {statistics.attendanceFromRegistrationRate.toFixed(1)}%
              </p>
            </div>
            <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-4">
              <h3 className="text-sm font-medium text-red-600 dark:text-red-400">爽約次數</h3>
              <p className="text-2xl font-bold text-red-900 dark:text-red-100">{statistics.noShowActivities}</p>
              <p className="text-xs text-red-600 dark:text-red-400">{statistics.noShowRate.toFixed(1)}%</p>
            </div>
            <div className="bg-indigo-50 dark:bg-indigo-900/20 rounded-lg p-4">
              <h3 className="text-sm font-medium text-indigo-600 dark:text-indigo-400">參與屆別</h3>
              <p className="text-2xl font-bold text-indigo-900 dark:text-indigo-100">
                {statistics.sessionStats.length}
              </p>
            </div>
          </div>

          {/* 屆別統計 */}
          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">各屆別參與統計</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {statistics.sessionStats.map((sessionStat) => (
                <div key={sessionStat.sessionId} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">{sessionStat.sessionName}</h4>
                      <p className="text-sm text-gray-500 dark:text-gray-400">{sessionStat.category}</p>
                    </div>
                    <span
                      className={cn(
                        "px-2 py-1 text-xs rounded",
                        sessionStat.rate >= 80
                          ? "bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200"
                          : sessionStat.rate >= 60
                            ? "bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200"
                            : "bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200",
                      )}
                    >
                      出席率 {sessionStat.rate.toFixed(1)}%
                    </span>
                  </div>
                  <div className="space-y-1 text-sm text-gray-600 dark:text-gray-300">
                    <p>總活動: {sessionStat.total}</p>
                    <p>
                      報名: {sessionStat.registered} ({sessionStat.registrationRate.toFixed(1)}%)
                    </p>
                    <p>出席: {sessionStat.attended}</p>
                    <p>
                      爽約: {sessionStat.noShows} ({sessionStat.noShowRate.toFixed(1)}%)
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 委員會統計 */}
          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">各委員會參與統計</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {Object.entries(statistics.committeeStats).map(([committee, stats]) => (
                <div key={committee} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-3">{committee}</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-300">總活動:</span>
                      <span className="text-gray-900 dark:text-white">{stats.total}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-300">報名率:</span>
                      <span className="text-purple-600 dark:text-purple-400">{stats.registrationRate.toFixed(1)}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-300">出席率:</span>
                      <span className="text-green-600 dark:text-green-400">{stats.rate.toFixed(1)}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-300">爽約率:</span>
                      <span className="text-red-600 dark:text-red-400">{stats.noShowRate.toFixed(1)}%</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 詳細活動記錄 */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">詳細活動記錄</h3>

            {Object.entries(activitiesBySession).map(([sessionId, activities]) => {
              const sessionInfo = statistics.sessionStats.find((s) => s.sessionId === sessionId)
              return (
                <div key={sessionId} className="mb-6">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-md font-medium text-gray-900 dark:text-white">
                      {sessionInfo?.sessionName} ({sessionInfo?.category})
                    </h4>
                    <span className="text-sm text-gray-500 dark:text-gray-400">{activities.length} 個活動</span>
                  </div>

                  <div className="bg-white dark:bg-gray-700 rounded-lg shadow overflow-hidden">
                    <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
                      <thead className="bg-gray-50 dark:bg-gray-800">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            日期
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            活動名稱
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            委員會
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            報名狀態
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            描述
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white dark:bg-gray-700 divide-y divide-gray-200 dark:divide-gray-600">
                        {activities.map((activity) => {
                          const statusDisplay = getRegistrationStatusDisplay(activity.registrationStatus)
                          return (
                            <tr key={activity.id} className="hover:bg-gray-50 dark:hover:bg-gray-650">
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                {activity.date}
                              </td>
                              <td className="px-6 py-4 text-sm text-gray-900 dark:text-white">{activity.name}</td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                <span className="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded">
                                  {activity.committee}
                                </span>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm">
                                <span className={cn("px-2 py-1 text-xs rounded", statusDisplay.className)}>
                                  {statusDisplay.text}
                                </span>
                              </td>
                              <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                                {activity.description || "-"}
                              </td>
                            </tr>
                          )
                        })}
                      </tbody>
                    </table>
                  </div>
                </div>
              )
            })}
          </div>

          {/* 關閉按鈕 */}
          <div className="mt-6 flex justify-end">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors"
            >
              關閉
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
