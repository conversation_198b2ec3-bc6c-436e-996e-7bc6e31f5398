/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { chromeuxreport_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof chromeuxreport_v1.Chromeuxreport;
};
export declare function chromeuxreport(version: 'v1'): chromeuxreport_v1.Chromeuxreport;
export declare function chromeuxreport(options: chromeuxreport_v1.Options): chromeuxreport_v1.Chromeuxreport;
declare const auth: AuthPlus;
export { auth };
export { chromeuxreport_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
