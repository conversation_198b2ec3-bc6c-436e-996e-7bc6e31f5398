// Google Sheets 設置組件
import React, { useState } from 'react'
import {
  Save,
  TestTube,
  Eye,
  EyeOff,
  AlertCircle,
  CheckCircle,
  ExternalLink,
  Copy,
  Download
} from 'lucide-react'
import { GoogleOAuthLogin } from './google-oauth-login'
import type { GoogleSheetsConfig } from '../types/google-sheets'

interface GoogleSheetsSettingsProps {
  config: GoogleSheetsConfig
  onConfigChange: (config: GoogleSheetsConfig) => void
  onTestConnection: () => Promise<boolean>
  className?: string
}

export function GoogleSheetsSettings({
  config,
  onConfigChange,
  onTestConnection,
  className = ''
}: GoogleSheetsSettingsProps) {
  const [localConfig, setLocalConfig] = useState<GoogleSheetsConfig>(config)
  const [showPrivateKey, setShowPrivateKey] = useState(false)
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null)
  const [isTesting, setIsTesting] = useState(false)
  const [authMethod, setAuthMethod] = useState<'service-account' | 'oauth2'>('oauth2')
  const [accessToken, setAccessToken] = useState<string | null>(null)
  const [authError, setAuthError] = useState<string | null>(null)

  const handleInputChange = (field: keyof GoogleSheetsConfig, value: string) => {
    const newConfig = { ...localConfig, [field]: value }
    setLocalConfig(newConfig)
  }

  const handleSave = () => {
    onConfigChange(localConfig)
    alert('設置已保存')
  }

  const handleTest = async () => {
    setIsTesting(true)
    setTestResult(null)

    try {
      // 先保存當前配置
      onConfigChange(localConfig)

      // 測試連接
      const success = await onTestConnection()

      setTestResult({
        success,
        message: success ? '連接測試成功！' : '連接測試失敗，請檢查配置。'
      })
    } catch (error) {
      setTestResult({
        success: false,
        message: `連接測試失敗: ${error instanceof Error ? error.message : '未知錯誤'}`
      })
    } finally {
      setIsTesting(false)
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    alert('已複製到剪貼板')
  }

  const downloadServiceAccountTemplate = () => {
    const template = *************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

    const blob = new Blob([JSON.stringify(template, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'service-account-template.json'
    a.click()
    URL.revokeObjectURL(url)
  }

  const handleOAuthSuccess = (token: string) => {
    setAccessToken(token)
    setAuthError(null)
    // 更新配置以包含 access token
    const newConfig = { ...localConfig, accessToken: token }
    setLocalConfig(newConfig)
    onConfigChange(newConfig)
  }

  const handleOAuthError = (error: string) => {
    setAuthError(error)
    setAccessToken(null)
  }

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 ${className}`}>
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          Google Sheets 設置
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          配置 Google Sheets API 連接以啟用數據同步功能
        </p>
      </div>

      {/* 認證方法選擇 */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          認證方法
        </label>
        <div className="flex space-x-4">
          <label className="flex items-center">
            <input
              type="radio"
              value="service-account"
              checked={authMethod === 'service-account'}
              onChange={(e) => setAuthMethod(e.target.value as 'service-account')}
              className="mr-2"
            />
            <span className="text-sm">Service Account (推薦)</span>
          </label>
          <label className="flex items-center">
            <input
              type="radio"
              value="oauth2"
              checked={authMethod === 'oauth2'}
              onChange={(e) => setAuthMethod(e.target.value as 'oauth2')}
              className="mr-2"
            />
            <span className="text-sm">OAuth2</span>
          </label>
        </div>
      </div>

      {/* 基本設置 */}
      <div className="space-y-4 mb-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Google Sheets ID *
          </label>
          <div className="flex space-x-2">
            <input
              type="text"
              value={localConfig.spreadsheetId}
              onChange={(e) => handleInputChange('spreadsheetId', e.target.value)}
              placeholder="從 Google Sheets URL 中提取的 ID"
              className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
            />
            <button
              onClick={() => copyToClipboard(localConfig.spreadsheetId)}
              className="px-3 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
            >
              <Copy className="h-4 w-4" />
            </button>
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            從 Google Sheets URL 中提取: https://docs.google.com/spreadsheets/d/[SPREADSHEET_ID]/edit
          </p>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Project ID *
          </label>
          <input
            type="text"
            value={localConfig.projectId || ''}
            onChange={(e) => handleInputChange('projectId', e.target.value)}
            placeholder="Google Cloud Project ID"
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
          />
        </div>
      </div>

      {/* Service Account 設置 */}
      {authMethod === 'service-account' && (
        <div className="space-y-4 mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <h4 className="font-medium text-blue-900 dark:text-blue-100">Service Account 設置</h4>
            <button
              onClick={downloadServiceAccountTemplate}
              className="flex items-center space-x-1 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200"
            >
              <Download className="h-3 w-3" />
              <span>下載模板</span>
            </button>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Service Account Email *
            </label>
            <input
              type="email"
              value={localConfig.serviceAccountEmail || ''}
              onChange={(e) => handleInputChange('serviceAccountEmail', e.target.value)}
              placeholder="<EMAIL>"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Private Key *
            </label>
            <div className="relative">
              <textarea
                value={localConfig.privateKey || ''}
                onChange={(e) => handleInputChange('privateKey', e.target.value)}
                placeholder="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----"
                rows={4}
                type={showPrivateKey ? 'text' : 'password'}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white font-mono text-xs"
              />
              <button
                onClick={() => setShowPrivateKey(!showPrivateKey)}
                className="absolute top-2 right-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                {showPrivateKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* OAuth2 設置 */}
      {authMethod === 'oauth2' && (
        <div className="space-y-4 mb-6 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <h4 className="font-medium text-green-900 dark:text-green-100 mb-2">OAuth2 設置</h4>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Client ID *
            </label>
            <input
              type="text"
              value={localConfig.clientId || ''}
              onChange={(e) => handleInputChange('clientId', e.target.value)}
              placeholder="your-client-id.apps.googleusercontent.com"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Client Secret *
            </label>
            <input
              type="password"
              value={localConfig.clientSecret || ''}
              onChange={(e) => handleInputChange('clientSecret', e.target.value)}
              placeholder="your-client-secret"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
            />
          </div>

          {/* OAuth2 登入組件 */}
          {localConfig.clientId && (
            <div className="mt-4">
              <GoogleOAuthLogin
                clientId={localConfig.clientId}
                onAuthSuccess={handleOAuthSuccess}
                onAuthError={handleOAuthError}
              />
            </div>
          )}

          {/* 顯示當前認證狀態 */}
          {accessToken && (
            <div className="mt-2 p-2 bg-green-100 dark:bg-green-900/30 rounded text-sm text-green-700 dark:text-green-300">
              ✅ 已獲得 Google 認證令牌
            </div>
          )}

          {authError && (
            <div className="mt-2 p-2 bg-red-100 dark:bg-red-900/30 rounded text-sm text-red-700 dark:text-red-300">
              ❌ 認證錯誤：{authError}
            </div>
          )}
        </div>
      )}

      {/* 測試結果 */}
      {testResult && (
        <div className={`mb-4 p-3 rounded-lg ${
          testResult.success
            ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800'
            : 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800'
        }`}>
          <div className="flex items-start space-x-2">
            {testResult.success ? (
              <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
            ) : (
              <AlertCircle className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
            )}
            <div className={`text-sm ${
              testResult.success
                ? 'text-green-700 dark:text-green-300'
                : 'text-red-700 dark:text-red-300'
            }`}>
              {testResult.message}
            </div>
          </div>
        </div>
      )}

      {/* 操作按鈕 */}
      <div className="flex items-center justify-between">
        <div className="flex space-x-3">
          <button
            onClick={handleSave}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
          >
            <Save className="h-4 w-4" />
            <span>保存設置</span>
          </button>

          <button
            onClick={handleTest}
            disabled={isTesting}
            className="flex items-center space-x-2 px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isTesting ? (
              <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
            ) : (
              <TestTube className="h-4 w-4" />
            )}
            <span>{isTesting ? '測試中...' : '測試連接'}</span>
          </button>
        </div>

        <a
          href="https://console.cloud.google.com/apis/credentials"
          target="_blank"
          rel="noopener noreferrer"
          className="flex items-center space-x-1 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200"
        >
          <span>Google Cloud Console</span>
          <ExternalLink className="h-3 w-3" />
        </a>
      </div>

      {/* 設置說明 */}
      <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
        <h5 className="font-medium text-gray-900 dark:text-white mb-2">設置說明</h5>
        <div className="text-sm text-gray-600 dark:text-gray-400 space-y-2">
          <p><strong>Service Account (推薦):</strong></p>
          <ol className="list-decimal list-inside space-y-1 ml-4">
            <li>在 Google Cloud Console 中創建 Service Account</li>
            <li>為 Service Account 生成 JSON 金鑰文件</li>
            <li>將 Service Account 的電子郵件添加到 Google Sheets 的共享權限中</li>
            <li>從 JSON 文件中提取所需信息填入上方表單</li>
          </ol>

          <p className="mt-3"><strong>OAuth2:</strong></p>
          <ol className="list-decimal list-inside space-y-1 ml-4">
            <li>在 Google Cloud Console 中創建 OAuth2 客戶端</li>
            <li>配置授權重定向 URI</li>
            <li>獲取 Client ID 和 Client Secret</li>
          </ol>
        </div>
      </div>
    </div>
  )
}
