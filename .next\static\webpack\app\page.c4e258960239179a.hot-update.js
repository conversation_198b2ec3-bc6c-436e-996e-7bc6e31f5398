"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/participant-management-page.tsx":
/*!****************************************************!*\
  !*** ./components/participant-management-page.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ParticipantManagementPage: () => (/* binding */ ParticipantManagementPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var _universal_bulk_import__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./universal-bulk-import */ \"(app-pages-browser)/./components/universal-bulk-import.tsx\");\n/* harmony import */ var _participant_attendance_history__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./participant-attendance-history */ \"(app-pages-browser)/./components/participant-attendance-history.tsx\");\n/* harmony import */ var _utils_activity_level__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils/activity-level */ \"(app-pages-browser)/./utils/activity-level.ts\");\n/* harmony import */ var _utils_statistics__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../utils/statistics */ \"(app-pages-browser)/./utils/statistics.ts\");\n/* __next_internal_client_entry_do_not_use__ ParticipantManagementPage auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction ParticipantManagementPage(param) {\n    let { allParticipants, sessionParticipants, sessions, selectedSessionId, onAddParticipant, onAddSessionParticipant, onBulkAddParticipants, onUpdateParticipant, onUpdateSessionParticipant, onDeleteParticipant, onRemoveFromSession, onBulkDeleteTitle, onBack, activities, activityLevelSettings = _utils_activity_level__WEBPACK_IMPORTED_MODULE_5__.DEFAULT_ACTIVITY_LEVEL_SETTINGS } = param;\n    _s();\n    const [newParticipant, setNewParticipant] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        category: \"\"\n    });\n    const [newParticipantTitle, setNewParticipantTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\") // 新增職銜時的臨時輸入\n    ;\n    const [isAddingParticipant, setIsAddingParticipant] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false) // 新增參加者的載入狀態\n    ;\n    const [editingParticipant, setEditingParticipant] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingSessionParticipant, setEditingSessionParticipant] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [categoryFilter, setCategoryFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [showBulkImport, setShowBulkImport] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"session\");\n    const [viewingAttendanceHistory, setViewingAttendanceHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 職銜管理相關狀態\n    const [showTitleManagement, setShowTitleManagement] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingTitle, setEditingTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [newTitle, setNewTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 添加現有參加者優化相關狀態\n    const [showAddExisting, setShowAddExisting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [addExistingSearchTerm, setAddExistingSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [addExistingCategoryFilter, setAddExistingCategoryFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedParticipants, setSelectedParticipants] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [pageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(20);\n    const [sortField, setSortField] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [sortDirection, setSortDirection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"asc\");\n    // 獲取當前屆別\n    const currentSession = sessions.find((s)=>s.id === selectedSessionId);\n    // 獲取當前屆別的參加者\n    const currentSessionParticipants = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ParticipantManagementPage.useMemo[currentSessionParticipants]\": ()=>{\n            return sessionParticipants.filter({\n                \"ParticipantManagementPage.useMemo[currentSessionParticipants]\": (sp)=>sp.sessionId === selectedSessionId\n            }[\"ParticipantManagementPage.useMemo[currentSessionParticipants]\"]);\n        }\n    }[\"ParticipantManagementPage.useMemo[currentSessionParticipants]\"], [\n        sessionParticipants,\n        selectedSessionId\n    ]);\n    // 獲取當前屆別的所有職銜\n    const currentSessionCategories = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ParticipantManagementPage.useMemo[currentSessionCategories]\": ()=>{\n            const categories = new Set(currentSessionParticipants.map({\n                \"ParticipantManagementPage.useMemo[currentSessionCategories]\": (sp)=>sp.category\n            }[\"ParticipantManagementPage.useMemo[currentSessionCategories]\"]).filter(Boolean));\n            return Array.from(categories);\n        }\n    }[\"ParticipantManagementPage.useMemo[currentSessionCategories]\"], [\n        currentSessionParticipants\n    ]);\n    // 獲取全局所有職銜\n    const allCategories = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ParticipantManagementPage.useMemo[allCategories]\": ()=>{\n            const globalCategories = new Set(allParticipants.map({\n                \"ParticipantManagementPage.useMemo[allCategories]\": (p)=>p.category\n            }[\"ParticipantManagementPage.useMemo[allCategories]\"]).filter(Boolean));\n            const sessionCategories = new Set(sessionParticipants.map({\n                \"ParticipantManagementPage.useMemo[allCategories]\": (sp)=>sp.category\n            }[\"ParticipantManagementPage.useMemo[allCategories]\"]).filter(Boolean));\n            return Array.from(new Set([\n                ...globalCategories,\n                ...sessionCategories\n            ]));\n        }\n    }[\"ParticipantManagementPage.useMemo[allCategories]\"], [\n        allParticipants,\n        sessionParticipants\n    ]);\n    // 計算統計數據\n    const statistics = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ParticipantManagementPage.useMemo[statistics]\": ()=>{\n            return (0,_utils_statistics__WEBPACK_IMPORTED_MODULE_6__.calculateStatistics)(activities, allParticipants, sessions);\n        }\n    }[\"ParticipantManagementPage.useMemo[statistics]\"], [\n        activities,\n        allParticipants,\n        sessions\n    ]);\n    const handleSort = (field)=>{\n        if (sortField === field) {\n            setSortDirection(sortDirection === \"asc\" ? \"desc\" : \"asc\");\n        } else {\n            setSortField(field);\n            setSortDirection(\"asc\");\n        }\n    };\n    // 過濾參加者\n    const filteredSessionParticipants = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ParticipantManagementPage.useMemo[filteredSessionParticipants]\": ()=>{\n            const filtered = currentSessionParticipants.filter({\n                \"ParticipantManagementPage.useMemo[filteredSessionParticipants].filtered\": (sp)=>{\n                    const matchesSearch = sp.name.toLowerCase().includes(searchTerm.toLowerCase());\n                    const matchesCategory = categoryFilter ? sp.category === categoryFilter : true;\n                    const matchesStatus = statusFilter === \"all\" || statusFilter === \"active\" && sp.isActive || statusFilter === \"inactive\" && !sp.isActive;\n                    return matchesSearch && matchesCategory && matchesStatus;\n                }\n            }[\"ParticipantManagementPage.useMemo[filteredSessionParticipants].filtered\"]);\n            // 計算每個參加者的出席率和活躍等級\n            const participantsWithActivityLevel = filtered.map({\n                \"ParticipantManagementPage.useMemo[filteredSessionParticipants].participantsWithActivityLevel\": (sp)=>{\n                    // 獲取該參加者在當前屆別的所有活動\n                    const participantActivities = activities.filter({\n                        \"ParticipantManagementPage.useMemo[filteredSessionParticipants].participantsWithActivityLevel.participantActivities\": (activity)=>activity.sessionId === selectedSessionId\n                    }[\"ParticipantManagementPage.useMemo[filteredSessionParticipants].participantsWithActivityLevel.participantActivities\"]);\n                    // 計算該參加者參與的活動數量和出席數量\n                    let totalParticipated = 0;\n                    let totalAttended = 0;\n                    participantActivities.forEach({\n                        \"ParticipantManagementPage.useMemo[filteredSessionParticipants].participantsWithActivityLevel\": (activity)=>{\n                            // 檢查該參加者是否參與了這個活動\n                            const participantInActivity = activity.participants.find({\n                                \"ParticipantManagementPage.useMemo[filteredSessionParticipants].participantsWithActivityLevel.participantInActivity\": (p)=>p.id === sp.participantId\n                            }[\"ParticipantManagementPage.useMemo[filteredSessionParticipants].participantsWithActivityLevel.participantInActivity\"]);\n                            if (participantInActivity) {\n                                totalParticipated++;\n                                // 檢查出席記錄 - 嘗試多種可能的鍵\n                                const attendanceRecord = participantInActivity.attendance;\n                                let isAttended = false;\n                                // 嘗試不同的鍵格式\n                                if (attendanceRecord[activity.id] === true) {\n                                    isAttended = true;\n                                } else if (attendanceRecord[activity.date] === true) {\n                                    isAttended = true;\n                                } else if (attendanceRecord[\"\".concat(activity.date)] === true) {\n                                    isAttended = true;\n                                }\n                                if (isAttended) {\n                                    totalAttended++;\n                                }\n                            }\n                        }\n                    }[\"ParticipantManagementPage.useMemo[filteredSessionParticipants].participantsWithActivityLevel\"]);\n                    const attendanceRate = totalParticipated > 0 ? totalAttended / totalParticipated * 100 : 0;\n                    const activityLevel = (0,_utils_activity_level__WEBPACK_IMPORTED_MODULE_5__.getActivityLevel)(attendanceRate, activityLevelSettings);\n                    return {\n                        ...sp,\n                        attendanceRate,\n                        activityLevel,\n                        totalParticipated,\n                        totalAttended\n                    };\n                }\n            }[\"ParticipantManagementPage.useMemo[filteredSessionParticipants].participantsWithActivityLevel\"]);\n            // 排序邏輯\n            if (sortField) {\n                participantsWithActivityLevel.sort({\n                    \"ParticipantManagementPage.useMemo[filteredSessionParticipants]\": (a, b)=>{\n                        let aValue = \"\";\n                        let bValue = \"\";\n                        switch(sortField){\n                            case \"name\":\n                                aValue = a.name.toLowerCase();\n                                bValue = b.name.toLowerCase();\n                                break;\n                            case \"category\":\n                                aValue = a.category.toLowerCase();\n                                bValue = b.category.toLowerCase();\n                                break;\n                            case \"joinDate\":\n                                aValue = a.joinDate || \"\";\n                                bValue = b.joinDate || \"\";\n                                break;\n                            case \"status\":\n                                aValue = a.isActive ? \"active\" : \"inactive\";\n                                bValue = b.isActive ? \"active\" : \"inactive\";\n                                break;\n                            case \"attendanceRate\":\n                                aValue = a.attendanceRate;\n                                bValue = b.attendanceRate;\n                                break;\n                            default:\n                                return 0;\n                        }\n                        if (aValue < bValue) return sortDirection === \"asc\" ? -1 : 1;\n                        if (aValue > bValue) return sortDirection === \"asc\" ? 1 : -1;\n                        return 0;\n                    }\n                }[\"ParticipantManagementPage.useMemo[filteredSessionParticipants]\"]);\n            }\n            return participantsWithActivityLevel;\n        }\n    }[\"ParticipantManagementPage.useMemo[filteredSessionParticipants]\"], [\n        currentSessionParticipants,\n        searchTerm,\n        categoryFilter,\n        statusFilter,\n        sortField,\n        sortDirection,\n        activities,\n        selectedSessionId,\n        activityLevelSettings\n    ]);\n    // 獲取不在當前屆別的全局參加者（優化版）\n    const availableGlobalParticipants = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ParticipantManagementPage.useMemo[availableGlobalParticipants]\": ()=>{\n            const sessionParticipantIds = new Set(currentSessionParticipants.map({\n                \"ParticipantManagementPage.useMemo[availableGlobalParticipants]\": (sp)=>sp.participantId\n            }[\"ParticipantManagementPage.useMemo[availableGlobalParticipants]\"]));\n            return allParticipants.filter({\n                \"ParticipantManagementPage.useMemo[availableGlobalParticipants]\": (p)=>!sessionParticipantIds.has(p.id)\n            }[\"ParticipantManagementPage.useMemo[availableGlobalParticipants]\"]);\n        }\n    }[\"ParticipantManagementPage.useMemo[availableGlobalParticipants]\"], [\n        allParticipants,\n        currentSessionParticipants\n    ]);\n    // 過濾和分頁的可用參加者\n    const filteredAvailableParticipants = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ParticipantManagementPage.useMemo[filteredAvailableParticipants]\": ()=>{\n            const filtered = availableGlobalParticipants.filter({\n                \"ParticipantManagementPage.useMemo[filteredAvailableParticipants].filtered\": (participant)=>{\n                    const matchesSearch = participant.name.toLowerCase().includes(addExistingSearchTerm.toLowerCase());\n                    const matchesCategory = addExistingCategoryFilter ? participant.category === addExistingCategoryFilter : true;\n                    return matchesSearch && matchesCategory;\n                }\n            }[\"ParticipantManagementPage.useMemo[filteredAvailableParticipants].filtered\"]);\n            const startIndex = (currentPage - 1) * pageSize;\n            const endIndex = startIndex + pageSize;\n            return {\n                participants: filtered.slice(startIndex, endIndex),\n                total: filtered.length,\n                totalPages: Math.ceil(filtered.length / pageSize)\n            };\n        }\n    }[\"ParticipantManagementPage.useMemo[filteredAvailableParticipants]\"], [\n        availableGlobalParticipants,\n        addExistingSearchTerm,\n        addExistingCategoryFilter,\n        currentPage,\n        pageSize\n    ]);\n    // 處理新增參加者\n    const handleAddParticipant = ()=>{\n        if (newParticipant.name.trim()) {\n            try {\n                const trimmedName = newParticipant.name.trim();\n                // 檢查是否已存在相同姓名的參加者\n                const existingParticipant = allParticipants.find((p)=>p.name.toLowerCase() === trimmedName.toLowerCase());\n                if (existingParticipant) {\n                    alert(\"參加者「\".concat(trimmedName, \"」已存在，請使用不同的姓名\"));\n                    return;\n                }\n                // 處理新增職銜的情況\n                let finalCategory = newParticipant.category;\n                if (finalCategory === \"新增職銜\") {\n                    finalCategory = newParticipantTitle.trim() // 使用新輸入的職銜\n                    ;\n                }\n                const participantToAdd = {\n                    name: trimmedName,\n                    category: finalCategory\n                };\n                console.log('Adding participant:', participantToAdd) // 調試日誌\n                ;\n                onAddParticipant(participantToAdd);\n                setNewParticipant({\n                    name: \"\",\n                    category: \"\"\n                });\n                setNewParticipantTitle(\"\") // 清空新職銜輸入\n                ;\n                // 顯示成功提示\n                alert(\"成功新增參加者「\".concat(participantToAdd.name, \"」\").concat(finalCategory ? \"，職銜：\".concat(finalCategory) : ''));\n            } catch (error) {\n                console.error('新增參加者時發生錯誤:', error);\n                alert(\"新增參加者時發生錯誤: \".concat(error instanceof Error ? error.message : '未知錯誤'));\n            }\n        }\n    };\n    // 處理更新參加者\n    const handleUpdateParticipant = ()=>{\n        if (editingParticipant) {\n            onUpdateParticipant(editingParticipant);\n            setEditingParticipant(null);\n        }\n    };\n    // 處理更新屆別參加者\n    const handleUpdateSessionParticipant = ()=>{\n        if (editingSessionParticipant) {\n            onUpdateSessionParticipant(editingSessionParticipant);\n            setEditingSessionParticipant(null);\n        }\n    };\n    // 處理批量導入\n    const handleBulkImport = (participants)=>{\n        onBulkAddParticipants(participants);\n        setShowBulkImport(false);\n    };\n    // 處理添加現有參加者到當前屆別\n    const handleAddToSession = (participant, category)=>{\n        if (selectedSessionId) {\n            onAddSessionParticipant({\n                participantId: participant.id,\n                sessionId: selectedSessionId,\n                name: participant.name,\n                category: category,\n                joinDate: new Date().toISOString().split(\"T\")[0],\n                isActive: true\n            });\n        }\n    };\n    // 批量添加選中的參加者\n    const handleBatchAddToSession = ()=>{\n        if (selectedSessionId && selectedParticipants.size > 0) {\n            selectedParticipants.forEach((participantId)=>{\n                const participant = allParticipants.find((p)=>p.id === participantId);\n                if (participant) {\n                    onAddSessionParticipant({\n                        participantId: participant.id,\n                        sessionId: selectedSessionId,\n                        name: participant.name,\n                        category: participant.category || \"\",\n                        joinDate: new Date().toISOString().split(\"T\")[0],\n                        isActive: true\n                    });\n                }\n            });\n            setSelectedParticipants(new Set());\n            setShowAddExisting(false);\n        }\n    };\n    // 處理職銜編輯\n    const handleUpdateTitle = (oldTitle, newTitle)=>{\n        if (newTitle.trim() && newTitle !== oldTitle) {\n            try {\n                // 檢查新職銜是否已存在（不區分大小寫）\n                if (allCategories.some((category)=>category.toLowerCase() === newTitle.trim().toLowerCase() && category.toLowerCase() !== oldTitle.toLowerCase())) {\n                    alert(\"職銜「\".concat(newTitle.trim(), \"」已存在\"));\n                    setEditingTitle(\"\");\n                    return;\n                }\n                // 找出所有使用該職銜的參加者\n                const participantsToUpdate = allParticipants.filter((p)=>p.category === oldTitle && !p.name.startsWith('職銜佔位符-') // 排除佔位符參加者\n                );\n                // 找出佔位符參加者（如果有）\n                const placeholders = allParticipants.filter((p)=>p.category === oldTitle && p.name.startsWith('職銜佔位符-'));\n                // 找出所有使用該職銜的屆別參加者\n                const sessionParticipantsToUpdate = sessionParticipants.filter((sp)=>sp.category === oldTitle);\n                // 顯示進度提示\n                const totalUpdates = participantsToUpdate.length;\n                const hasPlaceholders = placeholders.length > 0;\n                // 確認是否繼續\n                if (totalUpdates > 0 || hasPlaceholders || sessionParticipantsToUpdate.length > 0) {\n                    if (!window.confirm(\"將更新職銜從「\".concat(oldTitle, \"」到「\").concat(newTitle, \"」，\").concat(totalUpdates > 0 ? \"影響 \".concat(totalUpdates, \" 位參加者\") : '', \"。\\n\\n確定繼續嗎？\"))) {\n                        setEditingTitle(\"\");\n                        return;\n                    }\n                    // 批量更新全局參加者\n                    if (participantsToUpdate.length > 0) {\n                        const batchUpdatePromises = participantsToUpdate.map((participant)=>{\n                            const updatedParticipant = {\n                                ...participant,\n                                category: newTitle\n                            };\n                            return new Promise((resolve)=>{\n                                onUpdateParticipant(updatedParticipant);\n                                resolve(null);\n                            });\n                        });\n                        Promise.all(batchUpdatePromises).catch((err)=>{\n                            console.error('批量更新參加者時發生錯誤:', err);\n                        });\n                    }\n                    // 批量更新佔位符參加者\n                    if (placeholders.length > 0) {\n                        const batchUpdatePlaceholderPromises = placeholders.map((placeholder)=>{\n                            const updatedPlaceholder = {\n                                ...placeholder,\n                                name: \"職銜佔位符-\".concat(newTitle),\n                                category: newTitle\n                            };\n                            return new Promise((resolve)=>{\n                                onUpdateParticipant(updatedPlaceholder);\n                                resolve(null);\n                            });\n                        });\n                        Promise.all(batchUpdatePlaceholderPromises).catch((err)=>{\n                            console.error('批量更新佔位符時發生錯誤:', err);\n                        });\n                    }\n                    // 批量更新屆別參加者\n                    if (sessionParticipantsToUpdate.length > 0) {\n                        const batchUpdateSessionPromises = sessionParticipantsToUpdate.map((sessionParticipant)=>{\n                            if ('category' in sessionParticipant) {\n                                const updatedSessionParticipant = {\n                                    ...sessionParticipant,\n                                    category: newTitle\n                                };\n                                return new Promise((resolve)=>{\n                                    onUpdateSessionParticipant(updatedSessionParticipant);\n                                    resolve(null);\n                                });\n                            }\n                            return Promise.resolve(null);\n                        });\n                        Promise.all(batchUpdateSessionPromises).catch((err)=>{\n                            console.error('批量更新屆別參加者時發生錯誤:', err);\n                        });\n                    }\n                    // 顯示完成提示\n                    if (totalUpdates > 0) {\n                        alert(\"成功更新 \".concat(totalUpdates, \" 位參加者的職銜從「\").concat(oldTitle, \"」到「\").concat(newTitle, \"」\"));\n                    } else if (hasPlaceholders) {\n                        alert(\"成功更新職銜「\".concat(oldTitle, \"」為「\").concat(newTitle, \"」\"));\n                    } else {\n                        alert(\"成功更新職銜「\".concat(oldTitle, \"」為「\").concat(newTitle, \"」，沒有參加者使用此職銜\"));\n                    }\n                } else {\n                    alert(\"沒有找到使用「\".concat(oldTitle, \"」職銜的參加者\"));\n                }\n            } catch (error) {\n                console.error('更新職銜時發生錯誤:', error);\n                alert(\"更新職銜時發生錯誤: \".concat(error instanceof Error ? error.message : '未知錯誤'));\n            }\n        }\n        setEditingTitle(\"\");\n    };\n    // 處理職銜刪除\n    const handleDeleteTitle = (title)=>{\n        try {\n            // 找出所有使用該職銜的實際參加者（排除佔位符）\n            const participantsToUpdate = allParticipants.filter((p)=>p.category === title && !p.name.startsWith('職銜佔位符-'));\n            // 找出使用該職銜的佔位符參加者\n            const placeholders = allParticipants.filter((p)=>p.category === title && p.name.startsWith('職銜佔位符-'));\n            // 找出所有使用該職銜的屆別參加者\n            const sessionParticipantsToUpdate = sessionParticipants.filter((sp)=>sp.category === title);\n            const totalUpdates = participantsToUpdate.length;\n            const hasPlaceholders = placeholders.length > 0;\n            let confirmMessage = '確定要刪除職銜 \"'.concat(title, '\" 嗎？');\n            if (totalUpdates > 0) {\n                confirmMessage += \"\\n\\n這將會：\";\n                confirmMessage += \"\\n• 清除 \".concat(totalUpdates, \" 位成員的職銜\");\n                confirmMessage += \"\\n• 將他們的職銜設為空白\";\n            }\n            if (hasPlaceholders) {\n                confirmMessage += totalUpdates > 0 ? \"\\n• 移除職銜佔位符\" : \"\\n\\n這將移除職銜佔位符。\";\n            }\n            if (totalUpdates === 0 && !hasPlaceholders) {\n                confirmMessage += \"\\n\\n沒有成員使用此職銜，將直接移除。\";\n            }\n            if (window.confirm(confirmMessage)) {\n                try {\n                    // 使用新的批量刪除函數\n                    const result = onBulkDeleteTitle(title);\n                    // 顯示完成提示\n                    let successMessage = '成功刪除職銜 \"'.concat(title, '\"');\n                    if (result.participantsUpdated > 0) {\n                        successMessage += \"\\n\\n已完成：\";\n                        successMessage += \"\\n• 清除了 \".concat(result.participantsUpdated, \" 位成員的職銜\");\n                        successMessage += \"\\n• 這些成員的職銜現在為空白\";\n                    }\n                    if (result.placeholdersRemoved > 0) {\n                        successMessage += result.participantsUpdated > 0 ? \"\\n• 移除了職銜佔位符\" : \"\\n\\n已移除職銜佔位符。\";\n                    }\n                    if (result.participantsUpdated === 0 && result.placeholdersRemoved === 0) {\n                        successMessage += \"\\n\\n沒有成員使用此職銜，已直接移除。\";\n                    }\n                    alert(successMessage);\n                } catch (error) {\n                    console.error('刪除職銜時發生錯誤:', error);\n                    alert(\"刪除職銜時發生錯誤: \".concat(error instanceof Error ? error.message : '未知錯誤'));\n                }\n            }\n        } catch (error) {\n            console.error('刪除職銜時發生錯誤:', error);\n            alert(\"刪除職銜時發生錯誤: \".concat(error instanceof Error ? error.message : '未知錯誤'));\n        }\n    };\n    // 獲取參加者的歷史屆別信息\n    const getParticipantHistory = (participantId)=>{\n        return sessionParticipants.filter((sp)=>sp.participantId === participantId).map((sp)=>{\n            var _sessions_find;\n            return {\n                ...sp,\n                sessionName: ((_sessions_find = sessions.find((s)=>s.id === sp.sessionId)) === null || _sessions_find === void 0 ? void 0 : _sessions_find.name) || \"未知屆別\"\n            };\n        }).sort((a, b)=>b.sessionId.localeCompare(a.sessionId));\n    };\n    const sortedAllParticipants = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ParticipantManagementPage.useMemo[sortedAllParticipants]\": ()=>{\n            if (!sortField) return allParticipants;\n            return [\n                ...allParticipants\n            ].sort({\n                \"ParticipantManagementPage.useMemo[sortedAllParticipants]\": (a, b)=>{\n                    let aValue = \"\";\n                    let bValue = \"\";\n                    switch(sortField){\n                        case \"name\":\n                            aValue = a.name.toLowerCase();\n                            bValue = b.name.toLowerCase();\n                            break;\n                        default:\n                            return 0;\n                    }\n                    if (aValue < bValue) return sortDirection === \"asc\" ? -1 : 1;\n                    if (aValue > bValue) return sortDirection === \"asc\" ? 1 : -1;\n                    return 0;\n                }\n            }[\"ParticipantManagementPage.useMemo[sortedAllParticipants]\"]);\n        }\n    }[\"ParticipantManagementPage.useMemo[sortedAllParticipants]\"], [\n        allParticipants,\n        sortField,\n        sortDirection\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onBack,\n                        className: \"px-3 py-1 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors text-sm\",\n                        children: \"← 返回\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 568,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-gray-900 dark:text-white\",\n                        children: \"參加者管理\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 574,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowTitleManagement(true),\n                                className: \"px-3 py-1 bg-purple-500 text-white rounded-md hover:bg-purple-600 transition-colors text-sm flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        className: \"h-4 w-4 mr-1\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        stroke: \"currentColor\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 587,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 593,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 580,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"職銜管理\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 576,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowBulkImport(true),\n                                className: \"px-3 py-1 bg-indigo-500 text-white rounded-md hover:bg-indigo-600 transition-colors text-sm flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        className: \"h-4 w-4 mr-1\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        stroke: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                            lineNumber: 608,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 601,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"批量導入\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 597,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 575,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                lineNumber: 567,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm text-blue-800 dark:text-blue-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: \"當前活躍設定：\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                            lineNumber: 623,\n                            columnNumber: 11\n                        }, this),\n                        \"非常活躍 ≥ \",\n                        activityLevelSettings.veryActive,\n                        \"%， 活躍 \",\n                        activityLevelSettings.active,\n                        \"%-\",\n                        activityLevelSettings.veryActive - 1,\n                        \"%， 不活躍 < \",\n                        activityLevelSettings.active,\n                        \"%\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                    lineNumber: 622,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                lineNumber: 621,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-700 rounded-lg shadow p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-md font-medium text-gray-900 dark:text-white mb-4\",\n                        children: \"參與度統計\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 631,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-medium text-blue-700 dark:text-blue-300\",\n                                        children: \"整體平均參與度\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 634,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-blue-600 dark:text-blue-400\",\n                                        children: [\n                                            statistics.participationStats.averageParticipationRate.toFixed(1),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 635,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-blue-600 dark:text-blue-400 mt-1\",\n                                        children: \"成員參與活動的平均比例\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 638,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 633,\n                                columnNumber: 11\n                            }, this),\n                            selectedSessionId && statistics.participationStats.sessionParticipationStats[selectedSessionId] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 dark:bg-green-900/20 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-medium text-green-700 dark:text-green-300\",\n                                        children: \"當前屆別參與度\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 645,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-green-600 dark:text-green-400\",\n                                        children: [\n                                            statistics.participationStats.sessionParticipationStats[selectedSessionId].averageParticipationRate.toFixed(1),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 646,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-green-600 dark:text-green-400 mt-1\",\n                                        children: statistics.participationStats.sessionParticipationStats[selectedSessionId].sessionName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 649,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 644,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-medium text-purple-700 dark:text-purple-300\",\n                                        children: \"活躍成員數\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 656,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-purple-600 dark:text-purple-400\",\n                                        children: selectedSessionId && statistics.participationStats.sessionParticipationStats[selectedSessionId] ? statistics.participationStats.sessionParticipationStats[selectedSessionId].activeParticipants : statistics.participantStats.filter((p)=>p.participationRate > 0).length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 657,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-purple-600 dark:text-purple-400 mt-1\",\n                                        children: \"參與過活動的成員\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 662,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 655,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-medium text-orange-700 dark:text-orange-300\",\n                                        children: \"高參與度成員\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 668,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-orange-600 dark:text-orange-400\",\n                                        children: statistics.participationStats.highParticipationStats.count\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 669,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-orange-600 dark:text-orange-400 mt-1\",\n                                        children: [\n                                            \"≥\",\n                                            statistics.participationStats.highParticipationStats.threshold,\n                                            \"% (\",\n                                            statistics.participationStats.highParticipationStats.percentage.toFixed(1),\n                                            \"%)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 672,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 667,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 632,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                lineNumber: 630,\n                columnNumber: 7\n            }, this),\n            showTitleManagement && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 dark:text-white\",\n                                        children: \"職銜管理\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 685,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowTitleManagement(false),\n                                        className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M6 18L18 6M6 6l12 12\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 691,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                            lineNumber: 690,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 686,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 684,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                        children: \"新增職銜\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 698,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: newTitle,\n                                                onChange: (e)=>setNewTitle(e.target.value),\n                                                placeholder: \"輸入新職銜名稱\",\n                                                className: \"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 700,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    if (newTitle.trim()) {\n                                                        try {\n                                                            // 檢查職銜是否已存在（不區分大小寫）\n                                                            if (allCategories.some((category)=>category.toLowerCase() === newTitle.trim().toLowerCase())) {\n                                                                alert(\"職銜「\".concat(newTitle.trim(), \"」已存在\"));\n                                                                return;\n                                                            }\n                                                            // 新增職銜 - 由於職銜是從參加者資料中提取的，\n                                                            // 我們創建一個使用這個職銜的佔位符參加者\n                                                            const newParticipantWithTitle = {\n                                                                id: \"temp-\".concat(Date.now()),\n                                                                name: \"職銜佔位符-\".concat(newTitle.trim()),\n                                                                category: newTitle.trim(),\n                                                                isActive: true\n                                                            };\n                                                            // 添加到全局參加者列表\n                                                            onAddParticipant(newParticipantWithTitle);\n                                                            alert(\"成功新增職銜「\".concat(newTitle.trim(), \"」\"));\n                                                            setNewTitle(\"\");\n                                                        } catch (error) {\n                                                            console.error('新增職銜時發生錯誤:', error);\n                                                            alert(\"新增職銜時發生錯誤: \".concat(error instanceof Error ? error.message : '未知錯誤'));\n                                                        }\n                                                    }\n                                                },\n                                                className: \"px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors\",\n                                                disabled: !newTitle.trim(),\n                                                children: \"新增\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 707,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 699,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 697,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-md font-medium text-gray-900 dark:text-white mb-3\",\n                                        children: \"現有職銜\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 749,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            allCategories.map((title)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-md\",\n                                                    children: editingTitle === title ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 flex space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                defaultValue: title,\n                                                                className: \"flex-1 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-600 dark:text-white\",\n                                                                onKeyDown: (e)=>{\n                                                                    if (e.key === \"Enter\") {\n                                                                        handleUpdateTitle(title, e.currentTarget.value);\n                                                                    } else if (e.key === \"Escape\") {\n                                                                        setEditingTitle(\"\");\n                                                                    }\n                                                                },\n                                                                autoFocus: true\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 758,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setEditingTitle(\"\"),\n                                                                className: \"px-2 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600\",\n                                                                children: \"取消\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 771,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 757,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-900 dark:text-white\",\n                                                                children: title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 780,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>setEditingTitle(title),\n                                                                        className: \"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm\",\n                                                                        children: \"編輯\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 782,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleDeleteTitle(title),\n                                                                        className: \"text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 text-sm\",\n                                                                        children: \"刪除\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 788,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 781,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true)\n                                                }, title, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 752,\n                                                    columnNumber: 21\n                                                }, this)),\n                                            allCategories.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-500 dark:text-gray-400 text-center py-4\",\n                                                children: \"暫無職銜\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 800,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 750,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 748,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 683,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                    lineNumber: 682,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                lineNumber: 681,\n                columnNumber: 9\n            }, this),\n            showBulkImport && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_universal_bulk_import__WEBPACK_IMPORTED_MODULE_3__.UniversalBulkImport, {\n                        dataType: \"participants\",\n                        sessions: sessions,\n                        selectedSessionId: selectedSessionId,\n                        onImport: handleBulkImport,\n                        onCancel: ()=>setShowBulkImport(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 813,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                    lineNumber: 812,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                lineNumber: 811,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-700 rounded-lg shadow p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-md font-medium text-gray-900 dark:text-white\",\n                                    children: \"當前屆別\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                    lineNumber: 828,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full text-sm font-medium\",\n                                    children: (currentSession === null || currentSession === void 0 ? void 0 : currentSession.name) || \"未選擇屆別\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                    lineNumber: 829,\n                                    columnNumber: 13\n                                }, this),\n                                currentSession && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                    children: [\n                                        currentSession.startDate,\n                                        \" - \",\n                                        currentSession.endDate\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                    lineNumber: 833,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                            lineNumber: 827,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                            children: [\n                                \"屆別參加者: \",\n                                currentSessionParticipants.length,\n                                \" 人\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                    lineNumber: 840,\n                                    columnNumber: 13\n                                }, this),\n                                \"所有成員: \",\n                                allParticipants.length,\n                                \" 人\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                            lineNumber: 838,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                    lineNumber: 826,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                lineNumber: 825,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-700 rounded-lg shadow p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setViewMode(\"session\"),\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-4 py-2 rounded-md transition-colors\", viewMode === \"session\" ? \"bg-blue-500 text-white\" : \"bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-500\"),\n                                    children: \"屆別參加者\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                    lineNumber: 850,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setViewMode(\"global\"),\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-4 py-2 rounded-md transition-colors\", viewMode === \"global\" ? \"bg-blue-500 text-white\" : \"bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-500\"),\n                                    children: \"所有成員\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                    lineNumber: 861,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                            lineNumber: 849,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                            children: viewMode === \"session\" ? \"顯示 \".concat(filteredSessionParticipants.length, \" / \").concat(currentSessionParticipants.length, \" 位屆別參加者\") : \"顯示 \".concat(allParticipants.length, \" 位所有成員\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                            lineNumber: 873,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                    lineNumber: 848,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                lineNumber: 847,\n                columnNumber: 7\n            }, this),\n            viewMode === \"session\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                        children: \"搜索參加者\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 886,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        placeholder: \"輸入參加者姓名\",\n                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 887,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 885,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                        children: \"職銜過濾\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 896,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: categoryFilter,\n                                        onChange: (e)=>setCategoryFilter(e.target.value),\n                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"全部職銜\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 902,\n                                                columnNumber: 17\n                                            }, this),\n                                            currentSessionCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: category,\n                                                    children: category\n                                                }, category, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 904,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 897,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 895,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                        children: \"狀態過濾\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 911,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: statusFilter,\n                                        onChange: (e)=>setStatusFilter(e.target.value),\n                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"all\",\n                                                children: \"全部狀態\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 917,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"active\",\n                                                children: \"活躍\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 918,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"inactive\",\n                                                children: \"非活躍\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 919,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 912,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 910,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 884,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-700 rounded-lg shadow overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-6 py-4 border-b border-gray-200 dark:border-gray-600\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 dark:text-white\",\n                                    children: [\n                                        currentSession === null || currentSession === void 0 ? void 0 : currentSession.name,\n                                        \" 參加者列表 (\",\n                                        filteredSessionParticipants.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                    lineNumber: 927,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 926,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"min-w-full divide-y divide-gray-200 dark:divide-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            className: \"bg-gray-50 dark:bg-gray-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleSort(\"name\"),\n                                                            className: \"flex items-center space-x-1 hover:bg-gray-100 dark:hover:bg-gray-700 px-2 py-1 rounded transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"姓名\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 940,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: sortField === \"name\" ? sortDirection === \"asc\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M5 15l7-7 7 7\",\n                                                                        className: \"text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 944,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M19 9l-7 7-7-7\",\n                                                                        className: \"text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 952,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M8 9l4-4 4 4m0 6l-4 4-4-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 961,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 941,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 936,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 935,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleSort(\"category\"),\n                                                            className: \"flex items-center space-x-1 hover:bg-gray-100 dark:hover:bg-gray-700 px-2 py-1 rounded transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"職銜\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 976,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: sortField === \"category\" ? sortDirection === \"asc\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M5 15l7-7 7 7\",\n                                                                        className: \"text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 980,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M19 9l-7 7-7-7\",\n                                                                        className: \"text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 988,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M8 9l4-4 4 4m0 6l-4 4-4-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 997,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 977,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 972,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 971,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleSort(\"joinDate\"),\n                                                            className: \"flex items-center space-x-1 hover:bg-gray-100 dark:hover:bg-gray-700 px-2 py-1 rounded transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"加入日期\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1012,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: sortField === \"joinDate\" ? sortDirection === \"asc\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M5 15l7-7 7 7\",\n                                                                        className: \"text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1016,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M19 9l-7 7-7-7\",\n                                                                        className: \"text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1024,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M8 9l4-4 4 4m0 6l-4 4-4-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1033,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1013,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1008,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1007,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleSort(\"attendanceRate\"),\n                                                            className: \"flex items-center space-x-1 hover:bg-gray-100 dark:hover:bg-gray-700 px-2 py-1 rounded transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"活躍狀態\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1048,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: sortField === \"attendanceRate\" ? sortDirection === \"asc\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M5 15l7-7 7 7\",\n                                                                        className: \"text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1052,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M19 9l-7 7-7-7\",\n                                                                        className: \"text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1060,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M8 9l4-4 4 4m0 6l-4 4-4-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1069,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1049,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1044,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1043,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                                                        children: \"參與度詳情\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1079,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                                                        children: \"歷史屆別\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1082,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                                                        children: \"操作\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1085,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 934,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                            lineNumber: 933,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            className: \"bg-white dark:bg-gray-700 divide-y divide-gray-200 dark:divide-gray-600\",\n                                            children: filteredSessionParticipants.length > 0 ? filteredSessionParticipants.map((sessionParticipant)=>{\n                                                var _sessionParticipant_attendanceRate;\n                                                const history = getParticipantHistory(sessionParticipant.participantId);\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"hover:bg-gray-50 dark:hover:bg-gray-650\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\",\n                                                            children: sessionParticipant.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1096,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded\",\n                                                                children: sessionParticipant.category || \"無職銜\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 1100,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1099,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\",\n                                                            children: sessionParticipant.joinDate || \"-\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1104,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-col space-y-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-2 py-1 text-xs rounded font-medium\", (0,_utils_activity_level__WEBPACK_IMPORTED_MODULE_5__.getActivityLevelColor)(sessionParticipant.activityLevel)),\n                                                                        children: (0,_utils_activity_level__WEBPACK_IMPORTED_MODULE_5__.getActivityLevelText)(sessionParticipant.activityLevel)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1109,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: [\n                                                                            \"出席率: \",\n                                                                            ((_sessionParticipant_attendanceRate = sessionParticipant.attendanceRate) === null || _sessionParticipant_attendanceRate === void 0 ? void 0 : _sessionParticipant_attendanceRate.toFixed(1)) || 0,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1117,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: [\n                                                                            \"(\",\n                                                                            sessionParticipant.totalAttended || 0,\n                                                                            \"/\",\n                                                                            sessionParticipant.totalParticipated || 0,\n                                                                            \")\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1120,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 1108,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1107,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 text-sm text-gray-900 dark:text-white\",\n                                                            children: (()=>{\n                                                                const participantStat = statistics.participantStats.find((p)=>p.id === sessionParticipant.participantId);\n                                                                if (!participantStat) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                    children: \"無資料\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1128,\n                                                                    columnNumber: 60\n                                                                }, this);\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-col space-y-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs font-medium text-gray-700 dark:text-gray-300\",\n                                                                                    children: [\n                                                                                        \"總體: \",\n                                                                                        participantStat.participationDetails.participationRatio\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                                    lineNumber: 1133,\n                                                                                    columnNumber: 37\n                                                                                }, this),\n                                                                                participantStat.isHighParticipation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"px-1 py-0.5 bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 text-xs rounded\",\n                                                                                    children: \"高參與\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                                    lineNumber: 1137,\n                                                                                    columnNumber: 39\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                            lineNumber: 1132,\n                                                                            columnNumber: 35\n                                                                        }, this),\n                                                                        selectedSessionId && participantStat.sessionParticipationDetails[selectedSessionId] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-blue-600 dark:text-blue-400\",\n                                                                            children: [\n                                                                                \"本屆: \",\n                                                                                participantStat.sessionParticipationDetails[selectedSessionId].participationRatio\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                            lineNumber: 1143,\n                                                                            columnNumber: 37\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                            children: [\n                                                                                \"參與率: \",\n                                                                                participantStat.participationRate.toFixed(1),\n                                                                                \"%\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                            lineNumber: 1147,\n                                                                            columnNumber: 35\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1131,\n                                                                    columnNumber: 33\n                                                                }, this);\n                                                            })()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1125,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 text-sm text-gray-900 dark:text-white\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-wrap gap-1\",\n                                                                children: [\n                                                                    history.slice(0, 3).map((h)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-1 py-0.5 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 text-xs rounded\",\n                                                                            title: \"\".concat(h.sessionName, \": \").concat(h.category),\n                                                                            children: h.sessionName\n                                                                        }, h.id, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                            lineNumber: 1157,\n                                                                            columnNumber: 33\n                                                                        }, this)),\n                                                                    history.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: [\n                                                                            \"+\",\n                                                                            history.length - 3\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1166,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 1155,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1154,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>{\n                                                                        const globalParticipant = allParticipants.find((p)=>p.id === sessionParticipant.participantId);\n                                                                        if (globalParticipant) {\n                                                                            setViewingAttendanceHistory(globalParticipant);\n                                                                        }\n                                                                    },\n                                                                    className: \"text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 mr-4\",\n                                                                    children: \"出席記錄\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1171,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>setEditingSessionParticipant(sessionParticipant),\n                                                                    className: \"text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 mr-4\",\n                                                                    children: \"編輯\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1184,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>{\n                                                                        if (window.confirm('確定要將 \"'.concat(sessionParticipant.name, '\" 從當前屆別中移除嗎？'))) {\n                                                                            onRemoveFromSession(sessionParticipant.id);\n                                                                        }\n                                                                    },\n                                                                    className: \"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300\",\n                                                                    children: \"移除\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1190,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1170,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, sessionParticipant.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 1095,\n                                                    columnNumber: 25\n                                                }, this);\n                                            }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    colSpan: 6,\n                                                    className: \"px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400\",\n                                                    children: \"沒有找到符合條件的參加者\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 1206,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1205,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                            lineNumber: 1090,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                    lineNumber: 932,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 931,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 925,\n                        columnNumber: 11\n                    }, this),\n                    availableGlobalParticipants.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-700 rounded-lg shadow p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 dark:text-white\",\n                                        children: [\n                                            \"添加現有參加者到 \",\n                                            currentSession === null || currentSession === void 0 ? void 0 : currentSession.name\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1220,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowAddExisting(!showAddExisting),\n                                        className: \"px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                className: \"h-4 w-4 mr-2\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M12 4v16m8-8H4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 1234,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1227,\n                                                columnNumber: 19\n                                            }, this),\n                                            showAddExisting ? \"收起\" : \"添加成員 (\".concat(availableGlobalParticipants.length, \")\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1223,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1219,\n                                columnNumber: 15\n                            }, this),\n                            showAddExisting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                                        children: \"搜索成員\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1245,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: addExistingSearchTerm,\n                                                        onChange: (e)=>{\n                                                            setAddExistingSearchTerm(e.target.value);\n                                                            setCurrentPage(1);\n                                                        },\n                                                        placeholder: \"輸入成員姓名\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1248,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1244,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                                        children: \"職銜篩選\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1260,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: addExistingCategoryFilter,\n                                                        onChange: (e)=>{\n                                                            setAddExistingCategoryFilter(e.target.value);\n                                                            setCurrentPage(1);\n                                                        },\n                                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"全部職銜\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 1271,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            allCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: category,\n                                                                    children: category\n                                                                }, category, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1273,\n                                                                    columnNumber: 27\n                                                                }, this))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1263,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1259,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1243,\n                                        columnNumber: 19\n                                    }, this),\n                                    selectedParticipants.size > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-blue-800 dark:text-blue-200\",\n                                                children: [\n                                                    \"已選擇 \",\n                                                    selectedParticipants.size,\n                                                    \" 位成員\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1284,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setSelectedParticipants(new Set()),\n                                                        className: \"px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600\",\n                                                        children: \"清除選擇\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1288,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleBatchAddToSession,\n                                                        className: \"px-3 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600\",\n                                                        children: \"批量添加\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1294,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1287,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1283,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3\",\n                                        children: filteredAvailableParticipants.participants.map((participant)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-3 border rounded-md cursor-pointer transition-colors\", selectedParticipants.has(participant.id) ? \"border-blue-500 bg-blue-50 dark:bg-blue-900/20\" : \"border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-650\"),\n                                                onClick: ()=>{\n                                                    const newSelected = new Set(selectedParticipants);\n                                                    if (newSelected.has(participant.id)) {\n                                                        newSelected.delete(participant.id);\n                                                    } else {\n                                                        newSelected.add(participant.id);\n                                                    }\n                                                    setSelectedParticipants(newSelected);\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-gray-900 dark:text-white\",\n                                                                    children: participant.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1327,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                participant.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                    children: [\n                                                                        \"職銜: \",\n                                                                        participant.category\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1329,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                (()=>{\n                                                                    const participantStat = statistics.participantStats.find((p)=>p.id === participant.id);\n                                                                    if (!participantStat) return null;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mt-1 space-y-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                                                                        children: [\n                                                                                            \"總體: \",\n                                                                                            participantStat.participationDetails.participationRatio\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                                        lineNumber: 1338,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    participantStat.isHighParticipation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"px-1 py-0.5 bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 text-xs rounded\",\n                                                                                        children: \"高參與\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                                        lineNumber: 1342,\n                                                                                        columnNumber: 39\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                                lineNumber: 1337,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            selectedSessionId && participantStat.sessionParticipationDetails[selectedSessionId] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs text-blue-600 dark:text-blue-400\",\n                                                                                children: [\n                                                                                    \"本屆: \",\n                                                                                    participantStat.sessionParticipationDetails[selectedSessionId].participationRatio\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                                lineNumber: 1348,\n                                                                                columnNumber: 37\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1336,\n                                                                        columnNumber: 33\n                                                                    }, this);\n                                                                })()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1326,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: selectedParticipants.has(participant.id),\n                                                            onChange: ()=>{},\n                                                            className: \"ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1356,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 1325,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, participant.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1307,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1305,\n                                        columnNumber: 19\n                                    }, this),\n                                    filteredAvailableParticipants.totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                children: [\n                                                    \"顯示 \",\n                                                    (currentPage - 1) * pageSize + 1,\n                                                    \" -\",\n                                                    \" \",\n                                                    Math.min(currentPage * pageSize, filteredAvailableParticipants.total),\n                                                    \" /\",\n                                                    \" \",\n                                                    filteredAvailableParticipants.total,\n                                                    \" 位成員\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1370,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setCurrentPage(Math.max(1, currentPage - 1)),\n                                                        disabled: currentPage === 1,\n                                                        className: \"px-3 py-1 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded text-sm hover:bg-gray-300 dark:hover:bg-gray-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                        children: \"上一頁\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1376,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-3 py-1 text-sm text-gray-700 dark:text-gray-300\",\n                                                        children: [\n                                                            currentPage,\n                                                            \" / \",\n                                                            filteredAvailableParticipants.totalPages\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1383,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setCurrentPage(Math.min(filteredAvailableParticipants.totalPages, currentPage + 1)),\n                                                        disabled: currentPage === filteredAvailableParticipants.totalPages,\n                                                        className: \"px-3 py-1 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded text-sm hover:bg-gray-300 dark:hover:bg-gray-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                        children: \"下一頁\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1386,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1375,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1369,\n                                        columnNumber: 21\n                                    }, this),\n                                    filteredAvailableParticipants.participants.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500 dark:text-gray-400\",\n                                        children: availableGlobalParticipants.length > 0 ? \"沒有找到符合篩選條件的成員\" : \"沒有可添加的成員\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1400,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1241,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 1218,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true) : /* 全局參加者視圖 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-700 rounded-lg shadow overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-4 border-b border-gray-200 dark:border-gray-600\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 dark:text-white\",\n                            children: [\n                                \"所有成員列表 (\",\n                                allParticipants.length,\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                            lineNumber: 1413,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 1412,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"min-w-full divide-y divide-gray-200 dark:divide-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    className: \"bg-gray-50 dark:bg-gray-800\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleSort(\"name\"),\n                                                    className: \"flex items-center space-x-1 hover:bg-gray-100 dark:hover:bg-gray-700 px-2 py-1 rounded transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"姓名\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1426,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: sortField === \"name\" ? sortDirection === \"asc\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M5 15l7-7 7 7\",\n                                                                className: \"text-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 1430,\n                                                                columnNumber: 29\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M19 9l-7 7-7-7\",\n                                                                className: \"text-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 1438,\n                                                                columnNumber: 29\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M8 9l4-4 4 4m0 6l-4 4-4-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 1447,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1427,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 1422,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1421,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                                                children: \"參與屆別\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1457,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                                                children: \"操作\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1460,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1420,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                    lineNumber: 1419,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    className: \"bg-white dark:bg-gray-700 divide-y divide-gray-200 dark:divide-gray-600\",\n                                    children: sortedAllParticipants.map((participant)=>{\n                                        const history = getParticipantHistory(participant.id);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"hover:bg-gray-50 dark:hover:bg-gray-650\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\",\n                                                    children: participant.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 1470,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 text-sm text-gray-900 dark:text-white\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-1\",\n                                                        children: [\n                                                            history.map((h)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded\",\n                                                                    title: \"\".concat(h.sessionName, \": \").concat(h.category),\n                                                                    children: h.sessionName\n                                                                }, h.id, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1476,\n                                                                    columnNumber: 29\n                                                                }, this)),\n                                                            history.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                children: \"未參與任何屆別\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 1485,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1474,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 1473,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setViewingAttendanceHistory(participant),\n                                                            className: \"text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 mr-4\",\n                                                            children: \"出席記錄\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1490,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setEditingParticipant(participant),\n                                                            className: \"text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 mr-4\",\n                                                            children: \"編輯\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1496,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                if (window.confirm('確定要刪除參加者 \"'.concat(participant.name, '\" 嗎？這將刪除所有相關數據。'))) {\n                                                                    onDeleteParticipant(participant.id);\n                                                                }\n                                                            },\n                                                            className: \"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300\",\n                                                            children: \"刪除\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1502,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 1489,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, participant.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                            lineNumber: 1469,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                    lineNumber: 1465,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                            lineNumber: 1418,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 1417,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                lineNumber: 1411,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-700 rounded-lg shadow p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 dark:text-white mb-4\",\n                        children: editingParticipant ? \"編輯成員\" : editingSessionParticipant ? \"編輯屆別參加者\" : \"新增參加者\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 1524,\n                        columnNumber: 9\n                    }, this),\n                    editingSessionParticipant ? /* 編輯屆別參加者表單 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                        children: \"姓名\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1532,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: editingSessionParticipant.name,\n                                        onChange: (e)=>setEditingSessionParticipant({\n                                                ...editingSessionParticipant,\n                                                name: e.target.value\n                                            }),\n                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1533,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1531,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                        children: \"職銜\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1541,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: editingSessionParticipant.category,\n                                        onChange: (e)=>setEditingSessionParticipant({\n                                                ...editingSessionParticipant,\n                                                category: e.target.value\n                                            }),\n                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"選擇職銜\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1549,\n                                                columnNumber: 17\n                                            }, this),\n                                            allCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: category,\n                                                    children: category\n                                                }, category, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 1551,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1542,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1540,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                        children: \"加入日期\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1558,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"date\",\n                                        value: editingSessionParticipant.joinDate || \"\",\n                                        onChange: (e)=>setEditingSessionParticipant({\n                                                ...editingSessionParticipant,\n                                                joinDate: e.target.value\n                                            }),\n                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1559,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1557,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: editingSessionParticipant.isActive,\n                                            onChange: (e)=>setEditingSessionParticipant({\n                                                    ...editingSessionParticipant,\n                                                    isActive: e.target.checked\n                                                }),\n                                            className: \"mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                            lineNumber: 1570,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                            children: \"在此屆別中活躍\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                            lineNumber: 1578,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                    lineNumber: 1569,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1568,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 1530,\n                        columnNumber: 11\n                    }, this) : /* 新增/編輯全局參加者表單 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                        children: \"姓名\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1586,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: editingParticipant ? editingParticipant.name : newParticipant.name,\n                                        onChange: (e)=>editingParticipant ? setEditingParticipant({\n                                                ...editingParticipant,\n                                                name: e.target.value\n                                            }) : setNewParticipant({\n                                                ...newParticipant,\n                                                name: e.target.value\n                                            }),\n                                        placeholder: \"輸入參加者姓名\",\n                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1587,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1585,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                        children: \"默認職銜\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1600,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: editingParticipant ? editingParticipant.category || \"\" : newParticipant.category,\n                                        onChange: (e)=>editingParticipant ? setEditingParticipant({\n                                                ...editingParticipant,\n                                                category: e.target.value\n                                            }) : setNewParticipant({\n                                                ...newParticipant,\n                                                category: e.target.value\n                                            }),\n                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"選擇職銜\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1610,\n                                                columnNumber: 17\n                                            }, this),\n                                            allCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: category,\n                                                    children: category\n                                                }, category, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 1612,\n                                                    columnNumber: 19\n                                                }, this)),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"新增職銜\",\n                                                children: \"+ 新增職銜\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1616,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1601,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1599,\n                                columnNumber: 13\n                            }, this),\n                            ((editingParticipant === null || editingParticipant === void 0 ? void 0 : editingParticipant.category) === \"新增職銜\" || newParticipant.category === \"新增職銜\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"md:col-span-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                        children: \"新職銜名稱\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1621,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: editingParticipant ? editingParticipant.category === \"新增職銜\" ? \"\" : editingParticipant.category : newParticipantTitle,\n                                        placeholder: \"輸入新職銜名稱\",\n                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\",\n                                        onChange: (e)=>editingParticipant ? setEditingParticipant({\n                                                ...editingParticipant,\n                                                category: e.target.value\n                                            }) : setNewParticipantTitle(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1622,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1620,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 1584,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 flex justify-end space-x-3\",\n                        children: [\n                            (editingParticipant || editingSessionParticipant) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setEditingParticipant(null);\n                                    setEditingSessionParticipant(null);\n                                },\n                                className: \"px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors\",\n                                children: \"取消\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1640,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: editingSessionParticipant ? handleUpdateSessionParticipant : editingParticipant ? handleUpdateParticipant : handleAddParticipant,\n                                className: \"px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors\",\n                                disabled: editingSessionParticipant ? !editingSessionParticipant.name.trim() || !editingSessionParticipant.category : editingParticipant ? !editingParticipant.name.trim() : !newParticipant.name.trim() || newParticipant.category === \"新增職銜\" && !newParticipantTitle.trim(),\n                                children: editingSessionParticipant ? \"更新屆別參加者\" : editingParticipant ? \"更新成員\" : \"新增參加者\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1650,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 1638,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                lineNumber: 1523,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-700 rounded-lg shadow p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 dark:text-white mb-4\",\n                        children: \"統計信息\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 1674,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-blue-600 dark:text-blue-400\",\n                                        children: allParticipants.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1677,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                        children: \"所有成員總數\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1678,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1676,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-green-600 dark:text-green-400\",\n                                        children: currentSessionParticipants.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1681,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                        children: \"當前屆別參加者\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1682,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1680,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-purple-600 dark:text-purple-400\",\n                                        children: currentSessionParticipants.filter((sp)=>sp.isActive).length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1685,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                        children: \"活躍參加者\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1688,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1684,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-orange-600 dark:text-orange-400\",\n                                        children: currentSessionCategories.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1691,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                        children: \"當前屆別職銜數\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1692,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1690,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 1675,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                lineNumber: 1673,\n                columnNumber: 7\n            }, this),\n            viewingAttendanceHistory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_participant_attendance_history__WEBPACK_IMPORTED_MODULE_4__.ParticipantAttendanceHistory, {\n                participant: viewingAttendanceHistory,\n                activities: activities,\n                sessionParticipants: sessionParticipants,\n                sessions: sessions,\n                onClose: ()=>setViewingAttendanceHistory(null)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                lineNumber: 1699,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n        lineNumber: 566,\n        columnNumber: 5\n    }, this);\n}\n_s(ParticipantManagementPage, \"8Ah4ZwH2CyQSLTciY+cPZmXrnsY=\");\n_c = ParticipantManagementPage;\nvar _c;\n$RefreshReg$(_c, \"ParticipantManagementPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/participant-management-page.tsx\n"));

/***/ })

});