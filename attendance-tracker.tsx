"use client"

import { useState } from "react"
import { cn } from "@/lib/utils"
import { ActivityManagement } from "./components/activity-management"
import { ParticipantManagement } from "./components/participant-management"
import { ParticipantManagementPage } from "./components/participant-management-page"
import { Dashboard } from "./components/dashboard"
import { StatisticsDashboard } from "./components/statistics-dashboard"
import TestStatisticsDashboard from "./components/test-statistics-dashboard"
import { CommitteeManagement } from "./components/committee-management"
import { SessionManagement } from "./components/session-management"
import { useAttendanceData } from "./hooks/use-attendance-data"
import { useActivityHandlers } from "./hooks/use-activity-handlers"
import { useParticipantHandlers } from "./hooks/use-participant-handlers"
import { useCommitteeHandlers } from "./hooks/use-committee-handlers"
import { useSessionHandlers } from "./hooks/use-session-handlers"
import type { ActivityLevelSettings } from "./types"
import { DEFAULT_ACTIVITY_LEVEL_SETTINGS } from "./utils/activity-level"

interface AttendanceTrackerProps {
  className?: string
}

type ViewMode = "dashboard" | "activities" | "statistics" | "committees" | "sessions" | "participants" | "test"

export function AttendanceTracker({ className }: AttendanceTrackerProps) {
  const [selectedActivityId, setSelectedActivityId] = useState<string | null>(null)
  const [viewMode, setViewMode] = useState<ViewMode>("dashboard")
  const [activityLevelSettings, setActivityLevelSettings] = useState<ActivityLevelSettings>(
    DEFAULT_ACTIVITY_LEVEL_SETTINGS,
  )

  const {
    activities,
    allParticipants,
    sessionParticipants,
    committees,
    sessions,
    selectedSessionId,
    setActivities,
    setAllParticipants,
    setSessionParticipants,
    setCommittees,
    setSessions,
    setSelectedSessionId,
    selectedActivity,
    currentSessionCommittees,
    currentSessionActivities,
  } = useAttendanceData(selectedActivityId)

  const activityHandlers = useActivityHandlers({
    activities,
    allParticipants,
    sessionParticipants,
    sessions,
    selectedSessionId,
    setActivities,
    setAllParticipants,
    setSessionParticipants,
  })

  const participantHandlers = useParticipantHandlers({
    allParticipants,
    sessionParticipants,
    activities,
    setAllParticipants,
    setSessionParticipants,
    setActivities,
  })

  const committeeHandlers = useCommitteeHandlers({
    committees,
    activities,
    setCommittees,
    setActivities,
  })

  const sessionHandlers = useSessionHandlers({
    sessions,
    activities,
    sessionParticipants,
    selectedSessionId,
    setSessions,
    setActivities,
    setSessionParticipants,
    setSelectedSessionId,
  })

  const handleBackToMain = () => {
    setSelectedActivityId(null)
    setViewMode("dashboard")
  }

  const renderContent = () => {
    if (selectedActivity) {
      return (
        <ParticipantManagement
          activity={selectedActivity}
          allParticipants={allParticipants}
          onAddParticipant={activityHandlers.handleAddParticipantToActivity}
          onRemoveParticipant={activityHandlers.handleRemoveParticipantFromActivity}
          onUpdateParticipant={activityHandlers.handleUpdateParticipantInActivity}
          onToggleAttendance={activityHandlers.handleToggleAttendance}
          onBulkAddParticipants={activityHandlers.handleBulkAddParticipantsToActivity}
          onBack={handleBackToMain}
        />
      )
    }

    switch (viewMode) {
      case "dashboard":
        return (
          <Dashboard
            activities={currentSessionActivities}
            allParticipants={allParticipants}
            sessions={sessions}
            onViewActivities={() => setViewMode("activities")}
            onViewStatistics={() => setViewMode("statistics")}
            onAddActivity={() => setViewMode("activities")}
            activityLevelSettings={activityLevelSettings}
            onUpdateActivityLevelSettings={setActivityLevelSettings}
          />
        )

      case "participants":
        return (
          <ParticipantManagementPage
            allParticipants={allParticipants}
            sessionParticipants={sessionParticipants}
            sessions={sessions}
            selectedSessionId={selectedSessionId}
            activities={activities}
            onAddParticipant={participantHandlers.handleAddParticipant}
            onAddSessionParticipant={participantHandlers.handleAddSessionParticipant}
            onBulkAddSessionParticipants={participantHandlers.handleBulkAddSessionParticipants}
            onBulkAddParticipants={participantHandlers.handleBulkAddParticipants}
            onUpdateParticipant={participantHandlers.handleUpdateParticipant}
            onUpdateSessionParticipant={participantHandlers.handleUpdateSessionParticipant}
            onDeleteParticipant={participantHandlers.handleDeleteParticipant}
            onRemoveFromSession={participantHandlers.handleRemoveFromSession}
            onBulkDeleteTitle={participantHandlers.handleBulkDeleteTitle}
            onBack={handleBackToMain}
            activityLevelSettings={activityLevelSettings}
          />
        )

      case "statistics":
        return (
          <StatisticsDashboard
            activities={currentSessionActivities}
            allParticipants={allParticipants}
            sessions={sessions}
            onBack={handleBackToMain}
          />
        )

      case "committees":
        return (
          <CommitteeManagement
            committees={committees}
            sessions={sessions}
            selectedSessionId={selectedSessionId}
            onAddCommittee={committeeHandlers.handleAddCommittee}
            onBulkAddCommittees={committeeHandlers.handleBulkAddCommittees}
            onEditCommittee={committeeHandlers.handleEditCommittee}
            onDeleteCommittee={committeeHandlers.handleDeleteCommittee}
            onBack={handleBackToMain}
          />
        )

      case "sessions":
        return (
          <SessionManagement
            sessions={sessions}
            onAddSession={sessionHandlers.handleAddSession}
            onEditSession={sessionHandlers.handleEditSession}
            onDeleteSession={sessionHandlers.handleDeleteSession}
            onSetActiveSession={sessionHandlers.handleSetActiveSession}
            onBack={handleBackToMain}
          />
        )

      case "test":
        return <TestStatisticsDashboard />

      default:
        return (
          <ActivityManagement
            activities={currentSessionActivities}
            committees={currentSessionCommittees}
            sessions={sessions}
            selectedSessionId={selectedSessionId}
            onAddActivity={activityHandlers.handleAddActivity}
            onBulkAddActivitiesWithParticipants={activityHandlers.handleBulkAddActivitiesWithParticipants}
            onSelectActivity={setSelectedActivityId}
            onDeleteActivity={activityHandlers.handleDeleteActivity}
            onEditActivity={activityHandlers.handleEditActivity}
            onViewStatistics={() => setViewMode("statistics")}
            onManageCommittees={() => setViewMode("committees")}
          />
        )
    }
  }

  return (
    <div className={cn("p-4 bg-white dark:bg-gray-800 rounded-lg shadow", className)}>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">活動出席管理系統</h2>

        {!selectedActivity && (
          <div className="flex space-x-2">
            {[
              { key: "dashboard", label: "儀表板" },
              { key: "activities", label: "活動管理" },
              { key: "participants", label: "參加者管理" },
              { key: "statistics", label: "出席統計" },
              { key: "committees", label: "委員會管理" },
              { key: "sessions", label: "屆別管理" },
              { key: "test", label: "功能測試" },
            ].map(({ key, label }) => (
              <button
                key={key}
                onClick={() => setViewMode(key as ViewMode)}
                className={cn(
                  "px-4 py-2 rounded-md transition-colors",
                  viewMode === key
                    ? "bg-blue-500 text-white"
                    : "bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-500",
                )}
              >
                {label}
              </button>
            ))}
          </div>
        )}
      </div>

      {renderContent()}
    </div>
  )
}
