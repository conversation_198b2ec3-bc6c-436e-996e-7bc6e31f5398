"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/participant-management-page.tsx":
/*!****************************************************!*\
  !*** ./components/participant-management-page.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ParticipantManagementPage: () => (/* binding */ ParticipantManagementPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var _universal_bulk_import__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./universal-bulk-import */ \"(app-pages-browser)/./components/universal-bulk-import.tsx\");\n/* harmony import */ var _participant_attendance_history__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./participant-attendance-history */ \"(app-pages-browser)/./components/participant-attendance-history.tsx\");\n/* harmony import */ var _utils_activity_level__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils/activity-level */ \"(app-pages-browser)/./utils/activity-level.ts\");\n/* harmony import */ var _utils_statistics__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../utils/statistics */ \"(app-pages-browser)/./utils/statistics.ts\");\n/* __next_internal_client_entry_do_not_use__ ParticipantManagementPage auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction ParticipantManagementPage(param) {\n    let { allParticipants, sessionParticipants, sessions, selectedSessionId, onAddParticipant, onAddSessionParticipant, onBulkAddParticipants, onUpdateParticipant, onUpdateSessionParticipant, onDeleteParticipant, onRemoveFromSession, onBulkDeleteTitle, onBack, activities, activityLevelSettings = _utils_activity_level__WEBPACK_IMPORTED_MODULE_5__.DEFAULT_ACTIVITY_LEVEL_SETTINGS } = param;\n    _s();\n    const [newParticipant, setNewParticipant] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        category: \"\"\n    });\n    const [newParticipantTitle, setNewParticipantTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\") // 新增職銜時的臨時輸入\n    ;\n    const [editingParticipant, setEditingParticipant] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingSessionParticipant, setEditingSessionParticipant] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [categoryFilter, setCategoryFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [showBulkImport, setShowBulkImport] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"session\");\n    const [viewingAttendanceHistory, setViewingAttendanceHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 職銜管理相關狀態\n    const [showTitleManagement, setShowTitleManagement] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingTitle, setEditingTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [newTitle, setNewTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 添加現有參加者優化相關狀態\n    const [showAddExisting, setShowAddExisting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [addExistingSearchTerm, setAddExistingSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [addExistingCategoryFilter, setAddExistingCategoryFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedParticipants, setSelectedParticipants] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [pageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(20);\n    const [sortField, setSortField] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [sortDirection, setSortDirection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"asc\");\n    // 獲取當前屆別\n    const currentSession = sessions.find((s)=>s.id === selectedSessionId);\n    // 獲取當前屆別的參加者\n    const currentSessionParticipants = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ParticipantManagementPage.useMemo[currentSessionParticipants]\": ()=>{\n            return sessionParticipants.filter({\n                \"ParticipantManagementPage.useMemo[currentSessionParticipants]\": (sp)=>sp.sessionId === selectedSessionId\n            }[\"ParticipantManagementPage.useMemo[currentSessionParticipants]\"]);\n        }\n    }[\"ParticipantManagementPage.useMemo[currentSessionParticipants]\"], [\n        sessionParticipants,\n        selectedSessionId\n    ]);\n    // 獲取當前屆別的所有職銜\n    const currentSessionCategories = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ParticipantManagementPage.useMemo[currentSessionCategories]\": ()=>{\n            const categories = new Set(currentSessionParticipants.map({\n                \"ParticipantManagementPage.useMemo[currentSessionCategories]\": (sp)=>sp.category\n            }[\"ParticipantManagementPage.useMemo[currentSessionCategories]\"]).filter(Boolean));\n            return Array.from(categories);\n        }\n    }[\"ParticipantManagementPage.useMemo[currentSessionCategories]\"], [\n        currentSessionParticipants\n    ]);\n    // 獲取全局所有職銜\n    const allCategories = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ParticipantManagementPage.useMemo[allCategories]\": ()=>{\n            const globalCategories = new Set(allParticipants.map({\n                \"ParticipantManagementPage.useMemo[allCategories]\": (p)=>p.category\n            }[\"ParticipantManagementPage.useMemo[allCategories]\"]).filter(Boolean));\n            const sessionCategories = new Set(sessionParticipants.map({\n                \"ParticipantManagementPage.useMemo[allCategories]\": (sp)=>sp.category\n            }[\"ParticipantManagementPage.useMemo[allCategories]\"]).filter(Boolean));\n            return Array.from(new Set([\n                ...globalCategories,\n                ...sessionCategories\n            ]));\n        }\n    }[\"ParticipantManagementPage.useMemo[allCategories]\"], [\n        allParticipants,\n        sessionParticipants\n    ]);\n    // 計算統計數據\n    const statistics = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ParticipantManagementPage.useMemo[statistics]\": ()=>{\n            return (0,_utils_statistics__WEBPACK_IMPORTED_MODULE_6__.calculateStatistics)(activities, allParticipants, sessions);\n        }\n    }[\"ParticipantManagementPage.useMemo[statistics]\"], [\n        activities,\n        allParticipants,\n        sessions\n    ]);\n    const handleSort = (field)=>{\n        if (sortField === field) {\n            setSortDirection(sortDirection === \"asc\" ? \"desc\" : \"asc\");\n        } else {\n            setSortField(field);\n            setSortDirection(\"asc\");\n        }\n    };\n    // 過濾參加者\n    const filteredSessionParticipants = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ParticipantManagementPage.useMemo[filteredSessionParticipants]\": ()=>{\n            const filtered = currentSessionParticipants.filter({\n                \"ParticipantManagementPage.useMemo[filteredSessionParticipants].filtered\": (sp)=>{\n                    const matchesSearch = sp.name.toLowerCase().includes(searchTerm.toLowerCase());\n                    const matchesCategory = categoryFilter ? sp.category === categoryFilter : true;\n                    const matchesStatus = statusFilter === \"all\" || statusFilter === \"active\" && sp.isActive || statusFilter === \"inactive\" && !sp.isActive;\n                    return matchesSearch && matchesCategory && matchesStatus;\n                }\n            }[\"ParticipantManagementPage.useMemo[filteredSessionParticipants].filtered\"]);\n            // 計算每個參加者的出席率和活躍等級\n            const participantsWithActivityLevel = filtered.map({\n                \"ParticipantManagementPage.useMemo[filteredSessionParticipants].participantsWithActivityLevel\": (sp)=>{\n                    // 獲取該參加者在當前屆別的所有活動\n                    const participantActivities = activities.filter({\n                        \"ParticipantManagementPage.useMemo[filteredSessionParticipants].participantsWithActivityLevel.participantActivities\": (activity)=>activity.sessionId === selectedSessionId\n                    }[\"ParticipantManagementPage.useMemo[filteredSessionParticipants].participantsWithActivityLevel.participantActivities\"]);\n                    // 計算該參加者參與的活動數量和出席數量\n                    let totalParticipated = 0;\n                    let totalAttended = 0;\n                    participantActivities.forEach({\n                        \"ParticipantManagementPage.useMemo[filteredSessionParticipants].participantsWithActivityLevel\": (activity)=>{\n                            // 檢查該參加者是否參與了這個活動\n                            const participantInActivity = activity.participants.find({\n                                \"ParticipantManagementPage.useMemo[filteredSessionParticipants].participantsWithActivityLevel.participantInActivity\": (p)=>p.id === sp.participantId\n                            }[\"ParticipantManagementPage.useMemo[filteredSessionParticipants].participantsWithActivityLevel.participantInActivity\"]);\n                            if (participantInActivity) {\n                                totalParticipated++;\n                                // 檢查出席記錄 - 嘗試多種可能的鍵\n                                const attendanceRecord = participantInActivity.attendance;\n                                let isAttended = false;\n                                // 嘗試不同的鍵格式\n                                if (attendanceRecord[activity.id] === true) {\n                                    isAttended = true;\n                                } else if (attendanceRecord[activity.date] === true) {\n                                    isAttended = true;\n                                } else if (attendanceRecord[\"\".concat(activity.date)] === true) {\n                                    isAttended = true;\n                                }\n                                if (isAttended) {\n                                    totalAttended++;\n                                }\n                            }\n                        }\n                    }[\"ParticipantManagementPage.useMemo[filteredSessionParticipants].participantsWithActivityLevel\"]);\n                    const attendanceRate = totalParticipated > 0 ? totalAttended / totalParticipated * 100 : 0;\n                    const activityLevel = (0,_utils_activity_level__WEBPACK_IMPORTED_MODULE_5__.getActivityLevel)(attendanceRate, activityLevelSettings);\n                    return {\n                        ...sp,\n                        attendanceRate,\n                        activityLevel,\n                        totalParticipated,\n                        totalAttended\n                    };\n                }\n            }[\"ParticipantManagementPage.useMemo[filteredSessionParticipants].participantsWithActivityLevel\"]);\n            // 排序邏輯\n            if (sortField) {\n                participantsWithActivityLevel.sort({\n                    \"ParticipantManagementPage.useMemo[filteredSessionParticipants]\": (a, b)=>{\n                        let aValue = \"\";\n                        let bValue = \"\";\n                        switch(sortField){\n                            case \"name\":\n                                aValue = a.name.toLowerCase();\n                                bValue = b.name.toLowerCase();\n                                break;\n                            case \"category\":\n                                aValue = a.category.toLowerCase();\n                                bValue = b.category.toLowerCase();\n                                break;\n                            case \"joinDate\":\n                                aValue = a.joinDate || \"\";\n                                bValue = b.joinDate || \"\";\n                                break;\n                            case \"status\":\n                                aValue = a.isActive ? \"active\" : \"inactive\";\n                                bValue = b.isActive ? \"active\" : \"inactive\";\n                                break;\n                            case \"attendanceRate\":\n                                aValue = a.attendanceRate;\n                                bValue = b.attendanceRate;\n                                break;\n                            default:\n                                return 0;\n                        }\n                        if (aValue < bValue) return sortDirection === \"asc\" ? -1 : 1;\n                        if (aValue > bValue) return sortDirection === \"asc\" ? 1 : -1;\n                        return 0;\n                    }\n                }[\"ParticipantManagementPage.useMemo[filteredSessionParticipants]\"]);\n            }\n            return participantsWithActivityLevel;\n        }\n    }[\"ParticipantManagementPage.useMemo[filteredSessionParticipants]\"], [\n        currentSessionParticipants,\n        searchTerm,\n        categoryFilter,\n        statusFilter,\n        sortField,\n        sortDirection,\n        activities,\n        selectedSessionId,\n        activityLevelSettings\n    ]);\n    // 獲取不在當前屆別的全局參加者（優化版）\n    const availableGlobalParticipants = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ParticipantManagementPage.useMemo[availableGlobalParticipants]\": ()=>{\n            const sessionParticipantIds = new Set(currentSessionParticipants.map({\n                \"ParticipantManagementPage.useMemo[availableGlobalParticipants]\": (sp)=>sp.participantId\n            }[\"ParticipantManagementPage.useMemo[availableGlobalParticipants]\"]));\n            return allParticipants.filter({\n                \"ParticipantManagementPage.useMemo[availableGlobalParticipants]\": (p)=>!sessionParticipantIds.has(p.id)\n            }[\"ParticipantManagementPage.useMemo[availableGlobalParticipants]\"]);\n        }\n    }[\"ParticipantManagementPage.useMemo[availableGlobalParticipants]\"], [\n        allParticipants,\n        currentSessionParticipants\n    ]);\n    // 過濾和分頁的可用參加者\n    const filteredAvailableParticipants = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ParticipantManagementPage.useMemo[filteredAvailableParticipants]\": ()=>{\n            const filtered = availableGlobalParticipants.filter({\n                \"ParticipantManagementPage.useMemo[filteredAvailableParticipants].filtered\": (participant)=>{\n                    const matchesSearch = participant.name.toLowerCase().includes(addExistingSearchTerm.toLowerCase());\n                    const matchesCategory = addExistingCategoryFilter ? participant.category === addExistingCategoryFilter : true;\n                    return matchesSearch && matchesCategory;\n                }\n            }[\"ParticipantManagementPage.useMemo[filteredAvailableParticipants].filtered\"]);\n            const startIndex = (currentPage - 1) * pageSize;\n            const endIndex = startIndex + pageSize;\n            return {\n                participants: filtered.slice(startIndex, endIndex),\n                total: filtered.length,\n                totalPages: Math.ceil(filtered.length / pageSize)\n            };\n        }\n    }[\"ParticipantManagementPage.useMemo[filteredAvailableParticipants]\"], [\n        availableGlobalParticipants,\n        addExistingSearchTerm,\n        addExistingCategoryFilter,\n        currentPage,\n        pageSize\n    ]);\n    // 處理新增參加者\n    const handleAddParticipant = ()=>{\n        if (newParticipant.name.trim()) {\n            try {\n                // 處理新增職銜的情況\n                let finalCategory = newParticipant.category;\n                if (finalCategory === \"新增職銜\") {\n                    finalCategory = newParticipantTitle.trim() // 使用新輸入的職銜\n                    ;\n                }\n                const participantToAdd = {\n                    name: newParticipant.name.trim(),\n                    category: finalCategory\n                };\n                console.log('Adding participant:', participantToAdd) // 調試日誌\n                ;\n                onAddParticipant(participantToAdd);\n                setNewParticipant({\n                    name: \"\",\n                    category: \"\"\n                });\n                setNewParticipantTitle(\"\") // 清空新職銜輸入\n                ;\n                // 顯示成功提示\n                alert(\"成功新增參加者「\".concat(participantToAdd.name, \"」\").concat(finalCategory ? \"，職銜：\".concat(finalCategory) : ''));\n            } catch (error) {\n                console.error('新增參加者時發生錯誤:', error);\n                alert(\"新增參加者時發生錯誤: \".concat(error instanceof Error ? error.message : '未知錯誤'));\n            }\n        }\n    };\n    // 處理更新參加者\n    const handleUpdateParticipant = ()=>{\n        if (editingParticipant) {\n            onUpdateParticipant(editingParticipant);\n            setEditingParticipant(null);\n        }\n    };\n    // 處理更新屆別參加者\n    const handleUpdateSessionParticipant = ()=>{\n        if (editingSessionParticipant) {\n            onUpdateSessionParticipant(editingSessionParticipant);\n            setEditingSessionParticipant(null);\n        }\n    };\n    // 處理批量導入\n    const handleBulkImport = (participants)=>{\n        onBulkAddParticipants(participants);\n        setShowBulkImport(false);\n    };\n    // 處理添加現有參加者到當前屆別\n    const handleAddToSession = (participant, category)=>{\n        if (selectedSessionId) {\n            onAddSessionParticipant({\n                participantId: participant.id,\n                sessionId: selectedSessionId,\n                name: participant.name,\n                category: category,\n                joinDate: new Date().toISOString().split(\"T\")[0],\n                isActive: true\n            });\n        }\n    };\n    // 批量添加選中的參加者\n    const handleBatchAddToSession = ()=>{\n        if (selectedSessionId && selectedParticipants.size > 0) {\n            selectedParticipants.forEach((participantId)=>{\n                const participant = allParticipants.find((p)=>p.id === participantId);\n                if (participant) {\n                    onAddSessionParticipant({\n                        participantId: participant.id,\n                        sessionId: selectedSessionId,\n                        name: participant.name,\n                        category: participant.category || \"\",\n                        joinDate: new Date().toISOString().split(\"T\")[0],\n                        isActive: true\n                    });\n                }\n            });\n            setSelectedParticipants(new Set());\n            setShowAddExisting(false);\n        }\n    };\n    // 處理職銜編輯\n    const handleUpdateTitle = (oldTitle, newTitle)=>{\n        if (newTitle.trim() && newTitle !== oldTitle) {\n            try {\n                // 檢查新職銜是否已存在（不區分大小寫）\n                if (allCategories.some((category)=>category.toLowerCase() === newTitle.trim().toLowerCase() && category.toLowerCase() !== oldTitle.toLowerCase())) {\n                    alert(\"職銜「\".concat(newTitle.trim(), \"」已存在\"));\n                    setEditingTitle(\"\");\n                    return;\n                }\n                // 找出所有使用該職銜的參加者\n                const participantsToUpdate = allParticipants.filter((p)=>p.category === oldTitle && !p.name.startsWith('職銜佔位符-') // 排除佔位符參加者\n                );\n                // 找出佔位符參加者（如果有）\n                const placeholders = allParticipants.filter((p)=>p.category === oldTitle && p.name.startsWith('職銜佔位符-'));\n                // 找出所有使用該職銜的屆別參加者\n                const sessionParticipantsToUpdate = sessionParticipants.filter((sp)=>sp.category === oldTitle);\n                // 顯示進度提示\n                const totalUpdates = participantsToUpdate.length;\n                const hasPlaceholders = placeholders.length > 0;\n                // 確認是否繼續\n                if (totalUpdates > 0 || hasPlaceholders || sessionParticipantsToUpdate.length > 0) {\n                    if (!window.confirm(\"將更新職銜從「\".concat(oldTitle, \"」到「\").concat(newTitle, \"」，\").concat(totalUpdates > 0 ? \"影響 \".concat(totalUpdates, \" 位參加者\") : '', \"。\\n\\n確定繼續嗎？\"))) {\n                        setEditingTitle(\"\");\n                        return;\n                    }\n                    // 批量更新全局參加者\n                    if (participantsToUpdate.length > 0) {\n                        const batchUpdatePromises = participantsToUpdate.map((participant)=>{\n                            const updatedParticipant = {\n                                ...participant,\n                                category: newTitle\n                            };\n                            return new Promise((resolve)=>{\n                                onUpdateParticipant(updatedParticipant);\n                                resolve(null);\n                            });\n                        });\n                        Promise.all(batchUpdatePromises).catch((err)=>{\n                            console.error('批量更新參加者時發生錯誤:', err);\n                        });\n                    }\n                    // 批量更新佔位符參加者\n                    if (placeholders.length > 0) {\n                        const batchUpdatePlaceholderPromises = placeholders.map((placeholder)=>{\n                            const updatedPlaceholder = {\n                                ...placeholder,\n                                name: \"職銜佔位符-\".concat(newTitle),\n                                category: newTitle\n                            };\n                            return new Promise((resolve)=>{\n                                onUpdateParticipant(updatedPlaceholder);\n                                resolve(null);\n                            });\n                        });\n                        Promise.all(batchUpdatePlaceholderPromises).catch((err)=>{\n                            console.error('批量更新佔位符時發生錯誤:', err);\n                        });\n                    }\n                    // 批量更新屆別參加者\n                    if (sessionParticipantsToUpdate.length > 0) {\n                        const batchUpdateSessionPromises = sessionParticipantsToUpdate.map((sessionParticipant)=>{\n                            if ('category' in sessionParticipant) {\n                                const updatedSessionParticipant = {\n                                    ...sessionParticipant,\n                                    category: newTitle\n                                };\n                                return new Promise((resolve)=>{\n                                    onUpdateSessionParticipant(updatedSessionParticipant);\n                                    resolve(null);\n                                });\n                            }\n                            return Promise.resolve(null);\n                        });\n                        Promise.all(batchUpdateSessionPromises).catch((err)=>{\n                            console.error('批量更新屆別參加者時發生錯誤:', err);\n                        });\n                    }\n                    // 顯示完成提示\n                    if (totalUpdates > 0) {\n                        alert(\"成功更新 \".concat(totalUpdates, \" 位參加者的職銜從「\").concat(oldTitle, \"」到「\").concat(newTitle, \"」\"));\n                    } else if (hasPlaceholders) {\n                        alert(\"成功更新職銜「\".concat(oldTitle, \"」為「\").concat(newTitle, \"」\"));\n                    } else {\n                        alert(\"成功更新職銜「\".concat(oldTitle, \"」為「\").concat(newTitle, \"」，沒有參加者使用此職銜\"));\n                    }\n                } else {\n                    alert(\"沒有找到使用「\".concat(oldTitle, \"」職銜的參加者\"));\n                }\n            } catch (error) {\n                console.error('更新職銜時發生錯誤:', error);\n                alert(\"更新職銜時發生錯誤: \".concat(error instanceof Error ? error.message : '未知錯誤'));\n            }\n        }\n        setEditingTitle(\"\");\n    };\n    // 處理職銜刪除\n    const handleDeleteTitle = (title)=>{\n        try {\n            // 找出所有使用該職銜的實際參加者（排除佔位符）\n            const participantsToUpdate = allParticipants.filter((p)=>p.category === title && !p.name.startsWith('職銜佔位符-'));\n            // 找出使用該職銜的佔位符參加者\n            const placeholders = allParticipants.filter((p)=>p.category === title && p.name.startsWith('職銜佔位符-'));\n            // 找出所有使用該職銜的屆別參加者\n            const sessionParticipantsToUpdate = sessionParticipants.filter((sp)=>sp.category === title);\n            const totalUpdates = participantsToUpdate.length;\n            const hasPlaceholders = placeholders.length > 0;\n            let confirmMessage = '確定要刪除職銜 \"'.concat(title, '\" 嗎？');\n            if (totalUpdates > 0) {\n                confirmMessage += \"\\n\\n這將會：\";\n                confirmMessage += \"\\n• 清除 \".concat(totalUpdates, \" 位成員的職銜\");\n                confirmMessage += \"\\n• 將他們的職銜設為空白\";\n            }\n            if (hasPlaceholders) {\n                confirmMessage += totalUpdates > 0 ? \"\\n• 移除職銜佔位符\" : \"\\n\\n這將移除職銜佔位符。\";\n            }\n            if (totalUpdates === 0 && !hasPlaceholders) {\n                confirmMessage += \"\\n\\n沒有成員使用此職銜，將直接移除。\";\n            }\n            if (window.confirm(confirmMessage)) {\n                try {\n                    // 使用新的批量刪除函數\n                    const result = onBulkDeleteTitle(title);\n                    // 顯示完成提示\n                    let successMessage = '成功刪除職銜 \"'.concat(title, '\"');\n                    if (result.participantsUpdated > 0) {\n                        successMessage += \"\\n\\n已完成：\";\n                        successMessage += \"\\n• 清除了 \".concat(result.participantsUpdated, \" 位成員的職銜\");\n                        successMessage += \"\\n• 這些成員的職銜現在為空白\";\n                    }\n                    if (result.placeholdersRemoved > 0) {\n                        successMessage += result.participantsUpdated > 0 ? \"\\n• 移除了職銜佔位符\" : \"\\n\\n已移除職銜佔位符。\";\n                    }\n                    if (result.participantsUpdated === 0 && result.placeholdersRemoved === 0) {\n                        successMessage += \"\\n\\n沒有成員使用此職銜，已直接移除。\";\n                    }\n                    alert(successMessage);\n                } catch (error) {\n                    console.error('刪除職銜時發生錯誤:', error);\n                    alert(\"刪除職銜時發生錯誤: \".concat(error instanceof Error ? error.message : '未知錯誤'));\n                }\n            }\n        } catch (error) {\n            console.error('刪除職銜時發生錯誤:', error);\n            alert(\"刪除職銜時發生錯誤: \".concat(error instanceof Error ? error.message : '未知錯誤'));\n        }\n    };\n    // 獲取參加者的歷史屆別信息\n    const getParticipantHistory = (participantId)=>{\n        return sessionParticipants.filter((sp)=>sp.participantId === participantId).map((sp)=>{\n            var _sessions_find;\n            return {\n                ...sp,\n                sessionName: ((_sessions_find = sessions.find((s)=>s.id === sp.sessionId)) === null || _sessions_find === void 0 ? void 0 : _sessions_find.name) || \"未知屆別\"\n            };\n        }).sort((a, b)=>b.sessionId.localeCompare(a.sessionId));\n    };\n    const sortedAllParticipants = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ParticipantManagementPage.useMemo[sortedAllParticipants]\": ()=>{\n            if (!sortField) return allParticipants;\n            return [\n                ...allParticipants\n            ].sort({\n                \"ParticipantManagementPage.useMemo[sortedAllParticipants]\": (a, b)=>{\n                    let aValue = \"\";\n                    let bValue = \"\";\n                    switch(sortField){\n                        case \"name\":\n                            aValue = a.name.toLowerCase();\n                            bValue = b.name.toLowerCase();\n                            break;\n                        default:\n                            return 0;\n                    }\n                    if (aValue < bValue) return sortDirection === \"asc\" ? -1 : 1;\n                    if (aValue > bValue) return sortDirection === \"asc\" ? 1 : -1;\n                    return 0;\n                }\n            }[\"ParticipantManagementPage.useMemo[sortedAllParticipants]\"]);\n        }\n    }[\"ParticipantManagementPage.useMemo[sortedAllParticipants]\"], [\n        allParticipants,\n        sortField,\n        sortDirection\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onBack,\n                        className: \"px-3 py-1 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors text-sm\",\n                        children: \"← 返回\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 555,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-gray-900 dark:text-white\",\n                        children: \"參加者管理\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 561,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowTitleManagement(true),\n                                className: \"px-3 py-1 bg-purple-500 text-white rounded-md hover:bg-purple-600 transition-colors text-sm flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        className: \"h-4 w-4 mr-1\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        stroke: \"currentColor\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 574,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 580,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 567,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"職銜管理\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 563,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowBulkImport(true),\n                                className: \"px-3 py-1 bg-indigo-500 text-white rounded-md hover:bg-indigo-600 transition-colors text-sm flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        className: \"h-4 w-4 mr-1\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        stroke: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                            lineNumber: 595,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 588,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"批量導入\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 584,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 562,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                lineNumber: 554,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm text-blue-800 dark:text-blue-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: \"當前活躍設定：\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                            lineNumber: 610,\n                            columnNumber: 11\n                        }, this),\n                        \"非常活躍 ≥ \",\n                        activityLevelSettings.veryActive,\n                        \"%， 活躍 \",\n                        activityLevelSettings.active,\n                        \"%-\",\n                        activityLevelSettings.veryActive - 1,\n                        \"%， 不活躍 < \",\n                        activityLevelSettings.active,\n                        \"%\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                    lineNumber: 609,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                lineNumber: 608,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-700 rounded-lg shadow p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-md font-medium text-gray-900 dark:text-white mb-4\",\n                        children: \"參與度統計\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 618,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-medium text-blue-700 dark:text-blue-300\",\n                                        children: \"整體平均參與度\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 621,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-blue-600 dark:text-blue-400\",\n                                        children: [\n                                            statistics.participationStats.averageParticipationRate.toFixed(1),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 622,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-blue-600 dark:text-blue-400 mt-1\",\n                                        children: \"成員參與活動的平均比例\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 625,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 620,\n                                columnNumber: 11\n                            }, this),\n                            selectedSessionId && statistics.participationStats.sessionParticipationStats[selectedSessionId] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 dark:bg-green-900/20 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-medium text-green-700 dark:text-green-300\",\n                                        children: \"當前屆別參與度\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 632,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-green-600 dark:text-green-400\",\n                                        children: [\n                                            statistics.participationStats.sessionParticipationStats[selectedSessionId].averageParticipationRate.toFixed(1),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 633,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-green-600 dark:text-green-400 mt-1\",\n                                        children: statistics.participationStats.sessionParticipationStats[selectedSessionId].sessionName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 636,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 631,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-medium text-purple-700 dark:text-purple-300\",\n                                        children: \"活躍成員數\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 643,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-purple-600 dark:text-purple-400\",\n                                        children: selectedSessionId && statistics.participationStats.sessionParticipationStats[selectedSessionId] ? statistics.participationStats.sessionParticipationStats[selectedSessionId].activeParticipants : statistics.participantStats.filter((p)=>p.participationRate > 0).length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 644,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-purple-600 dark:text-purple-400 mt-1\",\n                                        children: \"參與過活動的成員\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 649,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 642,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-medium text-orange-700 dark:text-orange-300\",\n                                        children: \"高參與度成員\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 655,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-orange-600 dark:text-orange-400\",\n                                        children: statistics.participationStats.highParticipationStats.count\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 656,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-orange-600 dark:text-orange-400 mt-1\",\n                                        children: [\n                                            \"≥\",\n                                            statistics.participationStats.highParticipationStats.threshold,\n                                            \"% (\",\n                                            statistics.participationStats.highParticipationStats.percentage.toFixed(1),\n                                            \"%)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 659,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 654,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 619,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                lineNumber: 617,\n                columnNumber: 7\n            }, this),\n            showTitleManagement && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 dark:text-white\",\n                                        children: \"職銜管理\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 672,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowTitleManagement(false),\n                                        className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M6 18L18 6M6 6l12 12\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 678,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                            lineNumber: 677,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 673,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 671,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                        children: \"新增職銜\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 685,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: newTitle,\n                                                onChange: (e)=>setNewTitle(e.target.value),\n                                                placeholder: \"輸入新職銜名稱\",\n                                                className: \"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 687,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    if (newTitle.trim()) {\n                                                        try {\n                                                            // 檢查職銜是否已存在（不區分大小寫）\n                                                            if (allCategories.some((category)=>category.toLowerCase() === newTitle.trim().toLowerCase())) {\n                                                                alert(\"職銜「\".concat(newTitle.trim(), \"」已存在\"));\n                                                                return;\n                                                            }\n                                                            // 新增職銜 - 由於職銜是從參加者資料中提取的，\n                                                            // 我們創建一個使用這個職銜的佔位符參加者\n                                                            const newParticipantWithTitle = {\n                                                                id: \"temp-\".concat(Date.now()),\n                                                                name: \"職銜佔位符-\".concat(newTitle.trim()),\n                                                                category: newTitle.trim(),\n                                                                isActive: true\n                                                            };\n                                                            // 添加到全局參加者列表\n                                                            onAddParticipant(newParticipantWithTitle);\n                                                            alert(\"成功新增職銜「\".concat(newTitle.trim(), \"」\"));\n                                                            setNewTitle(\"\");\n                                                        } catch (error) {\n                                                            console.error('新增職銜時發生錯誤:', error);\n                                                            alert(\"新增職銜時發生錯誤: \".concat(error instanceof Error ? error.message : '未知錯誤'));\n                                                        }\n                                                    }\n                                                },\n                                                className: \"px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors\",\n                                                disabled: !newTitle.trim(),\n                                                children: \"新增\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 694,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 686,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 684,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-md font-medium text-gray-900 dark:text-white mb-3\",\n                                        children: \"現有職銜\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 736,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            allCategories.map((title)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-md\",\n                                                    children: editingTitle === title ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 flex space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                defaultValue: title,\n                                                                className: \"flex-1 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-600 dark:text-white\",\n                                                                onKeyDown: (e)=>{\n                                                                    if (e.key === \"Enter\") {\n                                                                        handleUpdateTitle(title, e.currentTarget.value);\n                                                                    } else if (e.key === \"Escape\") {\n                                                                        setEditingTitle(\"\");\n                                                                    }\n                                                                },\n                                                                autoFocus: true\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 745,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setEditingTitle(\"\"),\n                                                                className: \"px-2 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600\",\n                                                                children: \"取消\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 758,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 744,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-900 dark:text-white\",\n                                                                children: title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 767,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>setEditingTitle(title),\n                                                                        className: \"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm\",\n                                                                        children: \"編輯\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 769,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleDeleteTitle(title),\n                                                                        className: \"text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 text-sm\",\n                                                                        children: \"刪除\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 775,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 768,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true)\n                                                }, title, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 739,\n                                                    columnNumber: 21\n                                                }, this)),\n                                            allCategories.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-500 dark:text-gray-400 text-center py-4\",\n                                                children: \"暫無職銜\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 787,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 737,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 735,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 670,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                    lineNumber: 669,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                lineNumber: 668,\n                columnNumber: 9\n            }, this),\n            showBulkImport && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_universal_bulk_import__WEBPACK_IMPORTED_MODULE_3__.UniversalBulkImport, {\n                        dataType: \"participants\",\n                        sessions: sessions,\n                        selectedSessionId: selectedSessionId,\n                        onImport: handleBulkImport,\n                        onCancel: ()=>setShowBulkImport(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 800,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                    lineNumber: 799,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                lineNumber: 798,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-700 rounded-lg shadow p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-md font-medium text-gray-900 dark:text-white\",\n                                    children: \"當前屆別\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                    lineNumber: 815,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full text-sm font-medium\",\n                                    children: (currentSession === null || currentSession === void 0 ? void 0 : currentSession.name) || \"未選擇屆別\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                    lineNumber: 816,\n                                    columnNumber: 13\n                                }, this),\n                                currentSession && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                    children: [\n                                        currentSession.startDate,\n                                        \" - \",\n                                        currentSession.endDate\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                    lineNumber: 820,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                            lineNumber: 814,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                            children: [\n                                \"屆別參加者: \",\n                                currentSessionParticipants.length,\n                                \" 人\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                    lineNumber: 827,\n                                    columnNumber: 13\n                                }, this),\n                                \"所有成員: \",\n                                allParticipants.length,\n                                \" 人\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                            lineNumber: 825,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                    lineNumber: 813,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                lineNumber: 812,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-700 rounded-lg shadow p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setViewMode(\"session\"),\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-4 py-2 rounded-md transition-colors\", viewMode === \"session\" ? \"bg-blue-500 text-white\" : \"bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-500\"),\n                                    children: \"屆別參加者\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                    lineNumber: 837,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setViewMode(\"global\"),\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-4 py-2 rounded-md transition-colors\", viewMode === \"global\" ? \"bg-blue-500 text-white\" : \"bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-500\"),\n                                    children: \"所有成員\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                    lineNumber: 848,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                            lineNumber: 836,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                            children: viewMode === \"session\" ? \"顯示 \".concat(filteredSessionParticipants.length, \" / \").concat(currentSessionParticipants.length, \" 位屆別參加者\") : \"顯示 \".concat(allParticipants.length, \" 位所有成員\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                            lineNumber: 860,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                    lineNumber: 835,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                lineNumber: 834,\n                columnNumber: 7\n            }, this),\n            viewMode === \"session\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                        children: \"搜索參加者\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 873,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        placeholder: \"輸入參加者姓名\",\n                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 874,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 872,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                        children: \"職銜過濾\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 883,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: categoryFilter,\n                                        onChange: (e)=>setCategoryFilter(e.target.value),\n                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"全部職銜\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 889,\n                                                columnNumber: 17\n                                            }, this),\n                                            currentSessionCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: category,\n                                                    children: category\n                                                }, category, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 891,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 884,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 882,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                        children: \"狀態過濾\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 898,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: statusFilter,\n                                        onChange: (e)=>setStatusFilter(e.target.value),\n                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"all\",\n                                                children: \"全部狀態\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 904,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"active\",\n                                                children: \"活躍\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 905,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"inactive\",\n                                                children: \"非活躍\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 906,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 899,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 897,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 871,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-700 rounded-lg shadow overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-6 py-4 border-b border-gray-200 dark:border-gray-600\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 dark:text-white\",\n                                    children: [\n                                        currentSession === null || currentSession === void 0 ? void 0 : currentSession.name,\n                                        \" 參加者列表 (\",\n                                        filteredSessionParticipants.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                    lineNumber: 914,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 913,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"min-w-full divide-y divide-gray-200 dark:divide-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            className: \"bg-gray-50 dark:bg-gray-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleSort(\"name\"),\n                                                            className: \"flex items-center space-x-1 hover:bg-gray-100 dark:hover:bg-gray-700 px-2 py-1 rounded transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"姓名\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 927,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: sortField === \"name\" ? sortDirection === \"asc\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M5 15l7-7 7 7\",\n                                                                        className: \"text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 931,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M19 9l-7 7-7-7\",\n                                                                        className: \"text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 939,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M8 9l4-4 4 4m0 6l-4 4-4-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 948,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 928,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 923,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 922,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleSort(\"category\"),\n                                                            className: \"flex items-center space-x-1 hover:bg-gray-100 dark:hover:bg-gray-700 px-2 py-1 rounded transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"職銜\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 963,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: sortField === \"category\" ? sortDirection === \"asc\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M5 15l7-7 7 7\",\n                                                                        className: \"text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 967,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M19 9l-7 7-7-7\",\n                                                                        className: \"text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 975,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M8 9l4-4 4 4m0 6l-4 4-4-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 984,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 964,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 959,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 958,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleSort(\"joinDate\"),\n                                                            className: \"flex items-center space-x-1 hover:bg-gray-100 dark:hover:bg-gray-700 px-2 py-1 rounded transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"加入日期\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 999,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: sortField === \"joinDate\" ? sortDirection === \"asc\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M5 15l7-7 7 7\",\n                                                                        className: \"text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1003,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M19 9l-7 7-7-7\",\n                                                                        className: \"text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1011,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M8 9l4-4 4 4m0 6l-4 4-4-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1020,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1000,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 995,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 994,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleSort(\"attendanceRate\"),\n                                                            className: \"flex items-center space-x-1 hover:bg-gray-100 dark:hover:bg-gray-700 px-2 py-1 rounded transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"活躍狀態\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1035,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: sortField === \"attendanceRate\" ? sortDirection === \"asc\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M5 15l7-7 7 7\",\n                                                                        className: \"text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1039,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M19 9l-7 7-7-7\",\n                                                                        className: \"text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1047,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M8 9l4-4 4 4m0 6l-4 4-4-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1056,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1036,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1031,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1030,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                                                        children: \"參與度詳情\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1066,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                                                        children: \"歷史屆別\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1069,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                                                        children: \"操作\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1072,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 921,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                            lineNumber: 920,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            className: \"bg-white dark:bg-gray-700 divide-y divide-gray-200 dark:divide-gray-600\",\n                                            children: filteredSessionParticipants.length > 0 ? filteredSessionParticipants.map((sessionParticipant)=>{\n                                                var _sessionParticipant_attendanceRate;\n                                                const history = getParticipantHistory(sessionParticipant.participantId);\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"hover:bg-gray-50 dark:hover:bg-gray-650\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\",\n                                                            children: sessionParticipant.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1083,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded\",\n                                                                children: sessionParticipant.category || \"無職銜\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 1087,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1086,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\",\n                                                            children: sessionParticipant.joinDate || \"-\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1091,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-col space-y-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-2 py-1 text-xs rounded font-medium\", (0,_utils_activity_level__WEBPACK_IMPORTED_MODULE_5__.getActivityLevelColor)(sessionParticipant.activityLevel)),\n                                                                        children: (0,_utils_activity_level__WEBPACK_IMPORTED_MODULE_5__.getActivityLevelText)(sessionParticipant.activityLevel)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1096,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: [\n                                                                            \"出席率: \",\n                                                                            ((_sessionParticipant_attendanceRate = sessionParticipant.attendanceRate) === null || _sessionParticipant_attendanceRate === void 0 ? void 0 : _sessionParticipant_attendanceRate.toFixed(1)) || 0,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1104,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: [\n                                                                            \"(\",\n                                                                            sessionParticipant.totalAttended || 0,\n                                                                            \"/\",\n                                                                            sessionParticipant.totalParticipated || 0,\n                                                                            \")\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1107,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 1095,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1094,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 text-sm text-gray-900 dark:text-white\",\n                                                            children: (()=>{\n                                                                const participantStat = statistics.participantStats.find((p)=>p.id === sessionParticipant.participantId);\n                                                                if (!participantStat) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                    children: \"無資料\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1115,\n                                                                    columnNumber: 60\n                                                                }, this);\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-col space-y-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs font-medium text-gray-700 dark:text-gray-300\",\n                                                                                    children: [\n                                                                                        \"總體: \",\n                                                                                        participantStat.participationDetails.participationRatio\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                                    lineNumber: 1120,\n                                                                                    columnNumber: 37\n                                                                                }, this),\n                                                                                participantStat.isHighParticipation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"px-1 py-0.5 bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 text-xs rounded\",\n                                                                                    children: \"高參與\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                                    lineNumber: 1124,\n                                                                                    columnNumber: 39\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                            lineNumber: 1119,\n                                                                            columnNumber: 35\n                                                                        }, this),\n                                                                        selectedSessionId && participantStat.sessionParticipationDetails[selectedSessionId] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-blue-600 dark:text-blue-400\",\n                                                                            children: [\n                                                                                \"本屆: \",\n                                                                                participantStat.sessionParticipationDetails[selectedSessionId].participationRatio\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                            lineNumber: 1130,\n                                                                            columnNumber: 37\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                            children: [\n                                                                                \"參與率: \",\n                                                                                participantStat.participationRate.toFixed(1),\n                                                                                \"%\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                            lineNumber: 1134,\n                                                                            columnNumber: 35\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1118,\n                                                                    columnNumber: 33\n                                                                }, this);\n                                                            })()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1112,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 text-sm text-gray-900 dark:text-white\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-wrap gap-1\",\n                                                                children: [\n                                                                    history.slice(0, 3).map((h)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-1 py-0.5 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 text-xs rounded\",\n                                                                            title: \"\".concat(h.sessionName, \": \").concat(h.category),\n                                                                            children: h.sessionName\n                                                                        }, h.id, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                            lineNumber: 1144,\n                                                                            columnNumber: 33\n                                                                        }, this)),\n                                                                    history.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: [\n                                                                            \"+\",\n                                                                            history.length - 3\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1153,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 1142,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1141,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>{\n                                                                        const globalParticipant = allParticipants.find((p)=>p.id === sessionParticipant.participantId);\n                                                                        if (globalParticipant) {\n                                                                            setViewingAttendanceHistory(globalParticipant);\n                                                                        }\n                                                                    },\n                                                                    className: \"text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 mr-4\",\n                                                                    children: \"出席記錄\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1158,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>setEditingSessionParticipant(sessionParticipant),\n                                                                    className: \"text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 mr-4\",\n                                                                    children: \"編輯\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1171,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>{\n                                                                        if (window.confirm('確定要將 \"'.concat(sessionParticipant.name, '\" 從當前屆別中移除嗎？'))) {\n                                                                            onRemoveFromSession(sessionParticipant.id);\n                                                                        }\n                                                                    },\n                                                                    className: \"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300\",\n                                                                    children: \"移除\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1177,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1157,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, sessionParticipant.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 1082,\n                                                    columnNumber: 25\n                                                }, this);\n                                            }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    colSpan: 6,\n                                                    className: \"px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400\",\n                                                    children: \"沒有找到符合條件的參加者\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 1193,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1192,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                            lineNumber: 1077,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                    lineNumber: 919,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 918,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 912,\n                        columnNumber: 11\n                    }, this),\n                    availableGlobalParticipants.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-700 rounded-lg shadow p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 dark:text-white\",\n                                        children: [\n                                            \"添加現有參加者到 \",\n                                            currentSession === null || currentSession === void 0 ? void 0 : currentSession.name\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1207,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowAddExisting(!showAddExisting),\n                                        className: \"px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                className: \"h-4 w-4 mr-2\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M12 4v16m8-8H4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 1221,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1214,\n                                                columnNumber: 19\n                                            }, this),\n                                            showAddExisting ? \"收起\" : \"添加成員 (\".concat(availableGlobalParticipants.length, \")\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1210,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1206,\n                                columnNumber: 15\n                            }, this),\n                            showAddExisting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                                        children: \"搜索成員\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1232,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: addExistingSearchTerm,\n                                                        onChange: (e)=>{\n                                                            setAddExistingSearchTerm(e.target.value);\n                                                            setCurrentPage(1);\n                                                        },\n                                                        placeholder: \"輸入成員姓名\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1235,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1231,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                                        children: \"職銜篩選\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1247,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: addExistingCategoryFilter,\n                                                        onChange: (e)=>{\n                                                            setAddExistingCategoryFilter(e.target.value);\n                                                            setCurrentPage(1);\n                                                        },\n                                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"全部職銜\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 1258,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            allCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: category,\n                                                                    children: category\n                                                                }, category, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1260,\n                                                                    columnNumber: 27\n                                                                }, this))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1250,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1246,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1230,\n                                        columnNumber: 19\n                                    }, this),\n                                    selectedParticipants.size > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-blue-800 dark:text-blue-200\",\n                                                children: [\n                                                    \"已選擇 \",\n                                                    selectedParticipants.size,\n                                                    \" 位成員\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1271,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setSelectedParticipants(new Set()),\n                                                        className: \"px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600\",\n                                                        children: \"清除選擇\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1275,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleBatchAddToSession,\n                                                        className: \"px-3 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600\",\n                                                        children: \"批量添加\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1281,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1274,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1270,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3\",\n                                        children: filteredAvailableParticipants.participants.map((participant)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-3 border rounded-md cursor-pointer transition-colors\", selectedParticipants.has(participant.id) ? \"border-blue-500 bg-blue-50 dark:bg-blue-900/20\" : \"border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-650\"),\n                                                onClick: ()=>{\n                                                    const newSelected = new Set(selectedParticipants);\n                                                    if (newSelected.has(participant.id)) {\n                                                        newSelected.delete(participant.id);\n                                                    } else {\n                                                        newSelected.add(participant.id);\n                                                    }\n                                                    setSelectedParticipants(newSelected);\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-gray-900 dark:text-white\",\n                                                                    children: participant.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1314,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                participant.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                    children: [\n                                                                        \"職銜: \",\n                                                                        participant.category\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1316,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                (()=>{\n                                                                    const participantStat = statistics.participantStats.find((p)=>p.id === participant.id);\n                                                                    if (!participantStat) return null;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mt-1 space-y-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                                                                        children: [\n                                                                                            \"總體: \",\n                                                                                            participantStat.participationDetails.participationRatio\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                                        lineNumber: 1325,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    participantStat.isHighParticipation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"px-1 py-0.5 bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 text-xs rounded\",\n                                                                                        children: \"高參與\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                                        lineNumber: 1329,\n                                                                                        columnNumber: 39\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                                lineNumber: 1324,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            selectedSessionId && participantStat.sessionParticipationDetails[selectedSessionId] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs text-blue-600 dark:text-blue-400\",\n                                                                                children: [\n                                                                                    \"本屆: \",\n                                                                                    participantStat.sessionParticipationDetails[selectedSessionId].participationRatio\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                                lineNumber: 1335,\n                                                                                columnNumber: 37\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1323,\n                                                                        columnNumber: 33\n                                                                    }, this);\n                                                                })()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1313,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: selectedParticipants.has(participant.id),\n                                                            onChange: ()=>{},\n                                                            className: \"ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1343,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 1312,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, participant.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1294,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1292,\n                                        columnNumber: 19\n                                    }, this),\n                                    filteredAvailableParticipants.totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                children: [\n                                                    \"顯示 \",\n                                                    (currentPage - 1) * pageSize + 1,\n                                                    \" -\",\n                                                    \" \",\n                                                    Math.min(currentPage * pageSize, filteredAvailableParticipants.total),\n                                                    \" /\",\n                                                    \" \",\n                                                    filteredAvailableParticipants.total,\n                                                    \" 位成員\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1357,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setCurrentPage(Math.max(1, currentPage - 1)),\n                                                        disabled: currentPage === 1,\n                                                        className: \"px-3 py-1 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded text-sm hover:bg-gray-300 dark:hover:bg-gray-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                        children: \"上一頁\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1363,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-3 py-1 text-sm text-gray-700 dark:text-gray-300\",\n                                                        children: [\n                                                            currentPage,\n                                                            \" / \",\n                                                            filteredAvailableParticipants.totalPages\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1370,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setCurrentPage(Math.min(filteredAvailableParticipants.totalPages, currentPage + 1)),\n                                                        disabled: currentPage === filteredAvailableParticipants.totalPages,\n                                                        className: \"px-3 py-1 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded text-sm hover:bg-gray-300 dark:hover:bg-gray-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                        children: \"下一頁\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1373,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1362,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1356,\n                                        columnNumber: 21\n                                    }, this),\n                                    filteredAvailableParticipants.participants.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500 dark:text-gray-400\",\n                                        children: availableGlobalParticipants.length > 0 ? \"沒有找到符合篩選條件的成員\" : \"沒有可添加的成員\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1387,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1228,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 1205,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true) : /* 全局參加者視圖 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-700 rounded-lg shadow overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-4 border-b border-gray-200 dark:border-gray-600\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 dark:text-white\",\n                            children: [\n                                \"所有成員列表 (\",\n                                allParticipants.length,\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                            lineNumber: 1400,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 1399,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"min-w-full divide-y divide-gray-200 dark:divide-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    className: \"bg-gray-50 dark:bg-gray-800\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleSort(\"name\"),\n                                                    className: \"flex items-center space-x-1 hover:bg-gray-100 dark:hover:bg-gray-700 px-2 py-1 rounded transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"姓名\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1413,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: sortField === \"name\" ? sortDirection === \"asc\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M5 15l7-7 7 7\",\n                                                                className: \"text-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 1417,\n                                                                columnNumber: 29\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M19 9l-7 7-7-7\",\n                                                                className: \"text-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 1425,\n                                                                columnNumber: 29\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M8 9l4-4 4 4m0 6l-4 4-4-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 1434,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1414,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 1409,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1408,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                                                children: \"參與屆別\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1444,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                                                children: \"操作\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1447,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1407,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                    lineNumber: 1406,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    className: \"bg-white dark:bg-gray-700 divide-y divide-gray-200 dark:divide-gray-600\",\n                                    children: sortedAllParticipants.map((participant)=>{\n                                        const history = getParticipantHistory(participant.id);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"hover:bg-gray-50 dark:hover:bg-gray-650\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\",\n                                                    children: participant.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 1457,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 text-sm text-gray-900 dark:text-white\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-1\",\n                                                        children: [\n                                                            history.map((h)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded\",\n                                                                    title: \"\".concat(h.sessionName, \": \").concat(h.category),\n                                                                    children: h.sessionName\n                                                                }, h.id, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1463,\n                                                                    columnNumber: 29\n                                                                }, this)),\n                                                            history.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                children: \"未參與任何屆別\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 1472,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1461,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 1460,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setViewingAttendanceHistory(participant),\n                                                            className: \"text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 mr-4\",\n                                                            children: \"出席記錄\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1477,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setEditingParticipant(participant),\n                                                            className: \"text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 mr-4\",\n                                                            children: \"編輯\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1483,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                if (window.confirm('確定要刪除參加者 \"'.concat(participant.name, '\" 嗎？這將刪除所有相關數據。'))) {\n                                                                    onDeleteParticipant(participant.id);\n                                                                }\n                                                            },\n                                                            className: \"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300\",\n                                                            children: \"刪除\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1489,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 1476,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, participant.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                            lineNumber: 1456,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                    lineNumber: 1452,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                            lineNumber: 1405,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 1404,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                lineNumber: 1398,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-700 rounded-lg shadow p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 dark:text-white mb-4\",\n                        children: editingParticipant ? \"編輯成員\" : editingSessionParticipant ? \"編輯屆別參加者\" : \"新增參加者\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 1511,\n                        columnNumber: 9\n                    }, this),\n                    editingSessionParticipant ? /* 編輯屆別參加者表單 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                        children: \"姓名\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1519,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: editingSessionParticipant.name,\n                                        onChange: (e)=>setEditingSessionParticipant({\n                                                ...editingSessionParticipant,\n                                                name: e.target.value\n                                            }),\n                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1520,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1518,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                        children: \"職銜\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1528,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: editingSessionParticipant.category,\n                                        onChange: (e)=>setEditingSessionParticipant({\n                                                ...editingSessionParticipant,\n                                                category: e.target.value\n                                            }),\n                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"選擇職銜\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1536,\n                                                columnNumber: 17\n                                            }, this),\n                                            allCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: category,\n                                                    children: category\n                                                }, category, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 1538,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1529,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1527,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                        children: \"加入日期\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1545,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"date\",\n                                        value: editingSessionParticipant.joinDate || \"\",\n                                        onChange: (e)=>setEditingSessionParticipant({\n                                                ...editingSessionParticipant,\n                                                joinDate: e.target.value\n                                            }),\n                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1546,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1544,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: editingSessionParticipant.isActive,\n                                            onChange: (e)=>setEditingSessionParticipant({\n                                                    ...editingSessionParticipant,\n                                                    isActive: e.target.checked\n                                                }),\n                                            className: \"mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                            lineNumber: 1557,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                            children: \"在此屆別中活躍\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                            lineNumber: 1565,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                    lineNumber: 1556,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1555,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 1517,\n                        columnNumber: 11\n                    }, this) : /* 新增/編輯全局參加者表單 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                        children: \"姓名\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1573,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: editingParticipant ? editingParticipant.name : newParticipant.name,\n                                        onChange: (e)=>editingParticipant ? setEditingParticipant({\n                                                ...editingParticipant,\n                                                name: e.target.value\n                                            }) : setNewParticipant({\n                                                ...newParticipant,\n                                                name: e.target.value\n                                            }),\n                                        placeholder: \"輸入參加者姓名\",\n                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1574,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1572,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                        children: \"默認職銜\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1587,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: editingParticipant ? editingParticipant.category || \"\" : newParticipant.category,\n                                        onChange: (e)=>editingParticipant ? setEditingParticipant({\n                                                ...editingParticipant,\n                                                category: e.target.value\n                                            }) : setNewParticipant({\n                                                ...newParticipant,\n                                                category: e.target.value\n                                            }),\n                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"選擇職銜\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1597,\n                                                columnNumber: 17\n                                            }, this),\n                                            allCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: category,\n                                                    children: category\n                                                }, category, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 1599,\n                                                    columnNumber: 19\n                                                }, this)),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"新增職銜\",\n                                                children: \"+ 新增職銜\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1603,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1588,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1586,\n                                columnNumber: 13\n                            }, this),\n                            ((editingParticipant === null || editingParticipant === void 0 ? void 0 : editingParticipant.category) === \"新增職銜\" || newParticipant.category === \"新增職銜\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"md:col-span-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                        children: \"新職銜名稱\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1608,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"輸入新職銜名稱\",\n                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\",\n                                        onChange: (e)=>editingParticipant ? setEditingParticipant({\n                                                ...editingParticipant,\n                                                category: e.target.value\n                                            }) : setNewParticipant({\n                                                ...newParticipant,\n                                                category: e.target.value\n                                            })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1609,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1607,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 1571,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 flex justify-end space-x-3\",\n                        children: [\n                            (editingParticipant || editingSessionParticipant) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setEditingParticipant(null);\n                                    setEditingSessionParticipant(null);\n                                },\n                                className: \"px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors\",\n                                children: \"取消\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1626,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: editingSessionParticipant ? handleUpdateSessionParticipant : editingParticipant ? handleUpdateParticipant : handleAddParticipant,\n                                className: \"px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors\",\n                                disabled: editingSessionParticipant ? !editingSessionParticipant.name.trim() || !editingSessionParticipant.category : editingParticipant ? !editingParticipant.name.trim() : !newParticipant.name.trim(),\n                                children: editingSessionParticipant ? \"更新屆別參加者\" : editingParticipant ? \"更新成員\" : \"新增參加者\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1636,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 1624,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                lineNumber: 1510,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-700 rounded-lg shadow p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 dark:text-white mb-4\",\n                        children: \"統計信息\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 1660,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-blue-600 dark:text-blue-400\",\n                                        children: allParticipants.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1663,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                        children: \"所有成員總數\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1664,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1662,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-green-600 dark:text-green-400\",\n                                        children: currentSessionParticipants.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1667,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                        children: \"當前屆別參加者\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1668,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1666,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-purple-600 dark:text-purple-400\",\n                                        children: currentSessionParticipants.filter((sp)=>sp.isActive).length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1671,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                        children: \"活躍參加者\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1674,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1670,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-orange-600 dark:text-orange-400\",\n                                        children: currentSessionCategories.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1677,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                        children: \"當前屆別職銜數\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1678,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1676,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 1661,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                lineNumber: 1659,\n                columnNumber: 7\n            }, this),\n            viewingAttendanceHistory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_participant_attendance_history__WEBPACK_IMPORTED_MODULE_4__.ParticipantAttendanceHistory, {\n                participant: viewingAttendanceHistory,\n                activities: activities,\n                sessionParticipants: sessionParticipants,\n                sessions: sessions,\n                onClose: ()=>setViewingAttendanceHistory(null)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                lineNumber: 1685,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n        lineNumber: 553,\n        columnNumber: 5\n    }, this);\n}\n_s(ParticipantManagementPage, \"akn4qh8TnURYKpen77++XQNRukE=\");\n_c = ParticipantManagementPage;\nvar _c;\n$RefreshReg$(_c, \"ParticipantManagementPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/participant-management-page.tsx\n"));

/***/ })

});