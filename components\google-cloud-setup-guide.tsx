// Google Cloud Console 設置指南組件
import React, { useState } from 'react'
import { ExternalLink, Copy, CheckCircle, AlertCircle, Info } from 'lucide-react'

interface GoogleCloudSetupGuideProps {
  clientId: string
  currentOrigin?: string
}

export function GoogleCloudSetupGuide({ 
  clientId, 
  currentOrigin = typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000'
}: GoogleCloudSetupGuideProps) {
  const [copiedItem, setCopiedItem] = useState<string | null>(null)

  const copyToClipboard = (text: string, item: string) => {
    navigator.clipboard.writeText(text)
    setCopiedItem(item)
    setTimeout(() => setCopiedItem(null), 2000)
  }

  const projectId = 'hkuya-attendance'
  const consoleUrl = `https://console.cloud.google.com/apis/credentials?project=${projectId}`
  const apiUrl = `https://console.cloud.google.com/apis/library?project=${projectId}`

  const requiredOrigins = [
    'http://localhost:3000',
    'http://localhost:3001',
    currentOrigin
  ].filter((origin, index, arr) => arr.indexOf(origin) === index) // 去重

  const requiredRedirectUris = [
    'http://localhost:3000',
    'http://localhost:3001',
    currentOrigin
  ].filter((uri, index, arr) => arr.indexOf(uri) === index) // 去重

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
      <div className="flex items-center space-x-2 mb-4">
        <Info className="h-5 w-5 text-blue-500" />
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Google Cloud Console 設置指南
        </h3>
      </div>

      <div className="space-y-6">
        {/* 第一步：檢查項目和 API */}
        <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-3">
            <div className="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
              1
            </div>
            <h4 className="font-medium text-gray-900 dark:text-white">
              啟用必要的 API
            </h4>
          </div>
          
          <div className="ml-8 space-y-2">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              確保以下 API 已在項目中啟用：
            </p>
            <ul className="text-sm space-y-1">
              <li className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span>Google Sheets API</span>
              </li>
              <li className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span>Google Drive API（可選）</span>
              </li>
            </ul>
            <button
              onClick={() => window.open(apiUrl, '_blank')}
              className="inline-flex items-center space-x-1 text-blue-600 hover:text-blue-800 text-sm"
            >
              <ExternalLink className="h-4 w-4" />
              <span>前往 API 庫</span>
            </button>
          </div>
        </div>

        {/* 第二步：OAuth2 客戶端設置 */}
        <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-3">
            <div className="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
              2
            </div>
            <h4 className="font-medium text-gray-900 dark:text-white">
              配置 OAuth2 客戶端
            </h4>
          </div>
          
          <div className="ml-8 space-y-4">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                當前 Client ID：
              </p>
              <div className="flex items-center space-x-2">
                <code className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded text-xs">
                  {clientId}
                </code>
                <button
                  onClick={() => copyToClipboard(clientId, 'clientId')}
                  className="text-gray-500 hover:text-gray-700"
                >
                  {copiedItem === 'clientId' ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </button>
              </div>
            </div>

            <div>
              <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                授權的 JavaScript 來源：
              </p>
              <div className="space-y-2">
                {requiredOrigins.map((origin, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <code className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded text-xs flex-1">
                      {origin}
                    </code>
                    <button
                      onClick={() => copyToClipboard(origin, `origin-${index}`)}
                      className="text-gray-500 hover:text-gray-700"
                    >
                      {copiedItem === `origin-${index}` ? (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      ) : (
                        <Copy className="h-4 w-4" />
                      )}
                    </button>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                授權重定向 URI：
              </p>
              <div className="space-y-2">
                {requiredRedirectUris.map((uri, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <code className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded text-xs flex-1">
                      {uri}
                    </code>
                    <button
                      onClick={() => copyToClipboard(uri, `redirect-${index}`)}
                      className="text-gray-500 hover:text-gray-700"
                    >
                      {copiedItem === `redirect-${index}` ? (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      ) : (
                        <Copy className="h-4 w-4" />
                      )}
                    </button>
                  </div>
                ))}
              </div>
            </div>

            <button
              onClick={() => window.open(consoleUrl, '_blank')}
              className="inline-flex items-center space-x-1 text-blue-600 hover:text-blue-800 text-sm"
            >
              <ExternalLink className="h-4 w-4" />
              <span>前往 Google Cloud Console</span>
            </button>
          </div>
        </div>

        {/* 第三步：驗證設置 */}
        <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-3">
            <div className="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
              3
            </div>
            <h4 className="font-medium text-gray-900 dark:text-white">
              驗證設置
            </h4>
          </div>
          
          <div className="ml-8">
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
              完成上述設置後：
            </p>
            <ol className="text-sm space-y-1 list-decimal list-inside">
              <li>重新整理此頁面</li>
              <li>查看 Google API 連接測試結果</li>
              <li>嘗試 Google 登入</li>
            </ol>
          </div>
        </div>

        {/* 常見問題 */}
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
          <div className="flex items-start space-x-2">
            <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-yellow-800 dark:text-yellow-200 mb-2">
                常見問題
              </h4>
              <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                <li>• 設置更改可能需要幾分鐘才能生效</li>
                <li>• 確保使用正確的 Google 帳戶登入 Cloud Console</li>
                <li>• 如果問題持續，請嘗試清除瀏覽器快取</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
