/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { adsensehost_v4_1 } from './v4.1';
export declare const VERSIONS: {
    'v4.1': typeof adsensehost_v4_1.Adsensehost;
};
export declare function adsensehost(version: 'v4.1'): adsensehost_v4_1.Adsensehost;
export declare function adsensehost(options: adsensehost_v4_1.Options): adsensehost_v4_1.Adsensehost;
declare const auth: AuthPlus;
export { auth };
export { adsensehost_v4_1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
