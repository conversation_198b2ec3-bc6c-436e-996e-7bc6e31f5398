/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { certificatemanager_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof certificatemanager_v1.Certificatemanager;
};
export declare function certificatemanager(version: 'v1'): certificatemanager_v1.Certificatemanager;
export declare function certificatemanager(options: certificatemanager_v1.Options): certificatemanager_v1.Certificatemanager;
declare const auth: AuthPlus;
export { auth };
export { certificatemanager_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
