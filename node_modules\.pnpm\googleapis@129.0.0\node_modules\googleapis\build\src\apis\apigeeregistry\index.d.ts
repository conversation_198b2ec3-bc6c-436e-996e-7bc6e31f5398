/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { apigeeregistry_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof apigeeregistry_v1.Apigeeregistry;
};
export declare function apigeeregistry(version: 'v1'): apigeeregistry_v1.Apigeeregistry;
export declare function apigeeregistry(options: apigeeregistry_v1.Options): apigeeregistry_v1.Apigeeregistry;
declare const auth: AuthPlus;
export { auth };
export { apigeeregistry_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
