/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/google-sheets/connect/route";
exports.ids = ["app/api/google-sheets/connect/route"];
exports.modules = {

/***/ "(rsc)/./app/api/google-sheets/connect/route.ts":
/*!************************************************!*\
  !*** ./app/api/google-sheets/connect/route.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_google_sheets__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../lib/google-sheets */ \"(rsc)/./lib/google-sheets.ts\");\n// Google Sheets 連接 API 路由\n\n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { config, accessToken } = body;\n        // 驗證必要的配置\n        if (!config.spreadsheetId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: '缺少 Spreadsheet ID'\n            }, {\n                status: 400\n            });\n        }\n        // 檢查認證方式：Service Account 或 OAuth2 Access Token\n        const hasServiceAccount = config.serviceAccountEmail && config.privateKey;\n        const hasAccessToken = accessToken;\n        if (!hasServiceAccount && !hasAccessToken) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: '缺少認證配置：需要 Service Account 或 OAuth2 Access Token'\n            }, {\n                status: 400\n            });\n        }\n        const sheetNames = {\n            participants: \"參加者資料\" || 0,\n            activities: \"活動資料\" || 0,\n            attendance: \"出席記錄\" || 0,\n            sessions: \"屆別資料\" || 0\n        };\n        // 初始化 Google Sheets 服務\n        const googleSheetsService = new _lib_google_sheets__WEBPACK_IMPORTED_MODULE_1__.GoogleSheetsService(config, sheetNames);\n        // 測試連接（傳入 access token 如果有的話）\n        const initResult = await googleSheetsService.initialize(accessToken);\n        if (!initResult.success) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: initResult.error\n            }, {\n                status: 500\n            });\n        }\n        // 確保工作表存在\n        const sheetsResult = await googleSheetsService.ensureSheetsExist();\n        if (!sheetsResult.success) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: sheetsResult.error\n            }, {\n                status: 500\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Google Sheets 連接成功'\n        });\n    } catch (error) {\n        console.error('Google Sheets 連接錯誤:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: error instanceof Error ? error.message : '未知錯誤'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        message: 'Google Sheets 連接 API'\n    }, {\n        status: 200\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/google-sheets/connect/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/google-sheets.ts":
/*!******************************!*\
  !*** ./lib/google-sheets.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GoogleSheetsService: () => (/* binding */ GoogleSheetsService)\n/* harmony export */ });\n/* harmony import */ var google_auth_library__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! google-auth-library */ \"(rsc)/./node_modules/.pnpm/google-auth-library@9.15.1/node_modules/google-auth-library/build/src/index.js\");\n/* harmony import */ var googleapis__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! googleapis */ \"(rsc)/./node_modules/.pnpm/googleapis@129.0.0/node_modules/googleapis/build/src/index.js\");\n// Google Sheets API 服務層\n\n\nclass GoogleSheetsService {\n    constructor(config, sheetNames){\n        this.sheets = null;\n        this.auth = null;\n        this.config = config;\n        this.sheetNames = sheetNames;\n    }\n    /**\n   * 初始化 Google Sheets API 連接\n   */ async initialize(accessToken) {\n        try {\n            // 使用 Service Account 認證\n            if (this.config.serviceAccountEmail && this.config.privateKey) {\n                this.auth = new google_auth_library__WEBPACK_IMPORTED_MODULE_0__.GoogleAuth({\n                    credentials: {\n                        client_email: this.config.serviceAccountEmail,\n                        private_key: this.config.privateKey.replace(/\\\\n/g, '\\n'),\n                        project_id: this.config.projectId\n                    },\n                    scopes: [\n                        'https://www.googleapis.com/auth/spreadsheets'\n                    ]\n                });\n                this.sheets = googleapis__WEBPACK_IMPORTED_MODULE_1__.google.sheets({\n                    version: 'v4',\n                    auth: this.auth\n                });\n            } else if (accessToken) {\n                // 直接使用 access token 創建 sheets 實例\n                this.sheets = googleapis__WEBPACK_IMPORTED_MODULE_1__.google.sheets({\n                    version: 'v4',\n                    auth: accessToken // 直接使用 access token\n                });\n            } else {\n                throw new Error('缺少認證配置：需要 Service Account 憑證或 OAuth2 Access Token');\n            }\n            // 測試連接\n            await this.testConnection();\n            return {\n                success: true,\n                data: true,\n                timestamp: new Date()\n            };\n        } catch (error) {\n            console.error('Google Sheets 初始化失敗:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : '未知錯誤',\n                timestamp: new Date()\n            };\n        }\n    }\n    /**\n   * 測試連接\n   */ async testConnection() {\n        if (!this.sheets) {\n            throw new Error('Google Sheets API 未初始化');\n        }\n        try {\n            await this.sheets.spreadsheets.get({\n                spreadsheetId: this.config.spreadsheetId\n            });\n        } catch (error) {\n            throw new Error(`無法連接到 Google Sheets: ${error}`);\n        }\n    }\n    /**\n   * 確保工作表存在，如果不存在則創建\n   */ async ensureSheetsExist() {\n        try {\n            if (!this.sheets) {\n                throw new Error('Google Sheets API 未初始化');\n            }\n            const spreadsheet = await this.sheets.spreadsheets.get({\n                spreadsheetId: this.config.spreadsheetId\n            });\n            const existingSheets = spreadsheet.data.sheets?.map((sheet)=>sheet.properties?.title) || [];\n            const requiredSheets = Object.values(this.sheetNames);\n            for (const sheetName of requiredSheets){\n                if (!existingSheets.includes(sheetName)) {\n                    await this.createSheet(sheetName);\n                }\n            }\n            // 初始化表頭\n            await this.initializeHeaders();\n            return {\n                success: true,\n                data: true,\n                timestamp: new Date()\n            };\n        } catch (error) {\n            console.error('確保工作表存在時發生錯誤:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : '未知錯誤',\n                timestamp: new Date()\n            };\n        }\n    }\n    /**\n   * 創建新工作表\n   */ async createSheet(title) {\n        if (!this.sheets) {\n            throw new Error('Google Sheets API 未初始化');\n        }\n        await this.sheets.spreadsheets.batchUpdate({\n            spreadsheetId: this.config.spreadsheetId,\n            requestBody: {\n                requests: [\n                    {\n                        addSheet: {\n                            properties: {\n                                title,\n                                gridProperties: {\n                                    rowCount: 1000,\n                                    columnCount: 20\n                                }\n                            }\n                        }\n                    }\n                ]\n            }\n        });\n    }\n    /**\n   * 初始化表頭\n   */ async initializeHeaders() {\n        const headers = {\n            [this.sheetNames.participants]: [\n                'ID',\n                '姓名',\n                '職銜',\n                '電子郵件',\n                '電話',\n                '加入日期',\n                '是否活躍',\n                '備註',\n                '創建時間',\n                '更新時間'\n            ],\n            [this.sheetNames.activities]: [\n                'ID',\n                '活動名稱',\n                '日期',\n                '地點',\n                '描述',\n                '委員會',\n                '類型',\n                '最大參與人數',\n                '是否活躍',\n                '創建時間',\n                '更新時間'\n            ],\n            [this.sheetNames.attendance]: [\n                'ID',\n                '參加者ID',\n                '參加者姓名',\n                '活動ID',\n                '活動名稱',\n                '出席狀態',\n                '簽到時間',\n                '備註',\n                '記錄者',\n                '創建時間',\n                '更新時間'\n            ],\n            [this.sheetNames.sessions]: [\n                'ID',\n                '屆別名稱',\n                '開始日期',\n                '結束日期',\n                '描述',\n                '是否活躍',\n                '創建時間',\n                '更新時間'\n            ]\n        };\n        for (const [sheetName, headerRow] of Object.entries(headers)){\n            await this.updateRange(sheetName, 'A1:Z1', [\n                headerRow\n            ]);\n        }\n    }\n    /**\n   * 讀取指定範圍的數據\n   */ async readRange(sheetName, range) {\n        try {\n            if (!this.sheets) {\n                throw new Error('Google Sheets API 未初始化');\n            }\n            const response = await this.sheets.spreadsheets.values.get({\n                spreadsheetId: this.config.spreadsheetId,\n                range: `${sheetName}!${range}`\n            });\n            return {\n                success: true,\n                data: response.data.values || [],\n                timestamp: new Date()\n            };\n        } catch (error) {\n            console.error('讀取數據時發生錯誤:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : '未知錯誤',\n                timestamp: new Date()\n            };\n        }\n    }\n    /**\n   * 更新指定範圍的數據\n   */ async updateRange(sheetName, range, values) {\n        try {\n            if (!this.sheets) {\n                throw new Error('Google Sheets API 未初始化');\n            }\n            await this.sheets.spreadsheets.values.update({\n                spreadsheetId: this.config.spreadsheetId,\n                range: `${sheetName}!${range}`,\n                valueInputOption: 'RAW',\n                requestBody: {\n                    values\n                }\n            });\n            return {\n                success: true,\n                data: true,\n                timestamp: new Date()\n            };\n        } catch (error) {\n            console.error('更新數據時發生錯誤:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : '未知錯誤',\n                timestamp: new Date()\n            };\n        }\n    }\n    /**\n   * 追加數據到工作表末尾\n   */ async appendData(sheetName, values) {\n        try {\n            if (!this.sheets) {\n                throw new Error('Google Sheets API 未初始化');\n            }\n            await this.sheets.spreadsheets.values.append({\n                spreadsheetId: this.config.spreadsheetId,\n                range: `${sheetName}!A:Z`,\n                valueInputOption: 'RAW',\n                requestBody: {\n                    values\n                }\n            });\n            return {\n                success: true,\n                data: true,\n                timestamp: new Date()\n            };\n        } catch (error) {\n            console.error('追加數據時發生錯誤:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : '未知錯誤',\n                timestamp: new Date()\n            };\n        }\n    }\n    /**\n   * 清空工作表數據（保留表頭）\n   */ async clearSheet(sheetName) {\n        try {\n            if (!this.sheets) {\n                throw new Error('Google Sheets API 未初始化');\n            }\n            await this.sheets.spreadsheets.values.clear({\n                spreadsheetId: this.config.spreadsheetId,\n                range: `${sheetName}!A2:Z`\n            });\n            return {\n                success: true,\n                data: true,\n                timestamp: new Date()\n            };\n        } catch (error) {\n            console.error('清空工作表時發生錯誤:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : '未知錯誤',\n                timestamp: new Date()\n            };\n        }\n    }\n    /**\n   * 批量操作\n   */ async batchOperations(operations) {\n        try {\n            let successful = 0;\n            let failed = 0;\n            const errors = [];\n            for (const operation of operations){\n                try {\n                    switch(operation.operation){\n                        case 'create':\n                            await this.appendData(operation.sheetName, operation.data);\n                            break;\n                        case 'update':\n                            break;\n                        case 'delete':\n                            break;\n                    }\n                    successful++;\n                } catch (error) {\n                    failed++;\n                    errors.push(`${operation.operation} 操作失敗: ${error}`);\n                }\n            }\n            return {\n                success: true,\n                data: {\n                    successful,\n                    failed,\n                    errors\n                },\n                timestamp: new Date()\n            };\n        } catch (error) {\n            console.error('批量操作時發生錯誤:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : '未知錯誤',\n                timestamp: new Date()\n            };\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/google-sheets.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgoogle-sheets%2Fconnect%2Froute&page=%2Fapi%2Fgoogle-sheets%2Fconnect%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgoogle-sheets%2Fconnect%2Froute.ts&appDir=C%3A%5CUsers%5CPC-LPK%5CDocuments%5CHKUYA%5Cattendance%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CPC-LPK%5CDocuments%5CHKUYA%5Cattendance&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgoogle-sheets%2Fconnect%2Froute&page=%2Fapi%2Fgoogle-sheets%2Fconnect%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgoogle-sheets%2Fconnect%2Froute.ts&appDir=C%3A%5CUsers%5CPC-LPK%5CDocuments%5CHKUYA%5Cattendance%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CPC-LPK%5CDocuments%5CHKUYA%5Cattendance&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_PC_LPK_Documents_HKUYA_attendance_app_api_google_sheets_connect_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/google-sheets/connect/route.ts */ \"(rsc)/./app/api/google-sheets/connect/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/google-sheets/connect/route\",\n        pathname: \"/api/google-sheets/connect\",\n        filename: \"route\",\n        bundlePath: \"app/api/google-sheets/connect/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\app\\\\api\\\\google-sheets\\\\connect\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_PC_LPK_Documents_HKUYA_attendance_app_api_google_sheets_connect_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgoogle-sheets%2Fconnect%2Froute&page=%2Fapi%2Fgoogle-sheets%2Fconnect%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgoogle-sheets%2Fconnect%2Froute.ts&appDir=C%3A%5CUsers%5CPC-LPK%5CDocuments%5CHKUYA%5Cattendance%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CPC-LPK%5CDocuments%5CHKUYA%5Cattendance&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?96cf":
/*!********************************!*\
  !*** supports-color (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("http2");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1","vendor-chunks/googleapis@129.0.0","vendor-chunks/google-auth-library@9.15.1","vendor-chunks/tr46@0.0.3","vendor-chunks/bignumber.js@9.3.0","vendor-chunks/node-fetch@2.7.0","vendor-chunks/googleapis-common@7.2.0","vendor-chunks/whatwg-url@5.0.0","vendor-chunks/gaxios@6.7.1","vendor-chunks/qs@6.14.0","vendor-chunks/json-bigint@1.0.0","vendor-chunks/google-logging-utils@0.0.2","vendor-chunks/object-inspect@1.13.4","vendor-chunks/gcp-metadata@6.1.1","vendor-chunks/debug@4.4.1","vendor-chunks/get-intrinsic@1.3.0","vendor-chunks/https-proxy-agent@7.0.6","vendor-chunks/gtoken@7.1.0","vendor-chunks/uuid@9.0.1","vendor-chunks/agent-base@7.1.3","vendor-chunks/jws@4.0.0","vendor-chunks/jwa@2.0.1","vendor-chunks/url-template@2.0.8","vendor-chunks/ecdsa-sig-formatter@1.0.11","vendor-chunks/webidl-conversions@3.0.1","vendor-chunks/base64-js@1.5.1","vendor-chunks/side-channel-list@1.0.0","vendor-chunks/extend@3.0.2","vendor-chunks/ms@2.1.3","vendor-chunks/side-channel-weakmap@1.0.2","vendor-chunks/has-symbols@1.1.0","vendor-chunks/function-bind@1.1.2","vendor-chunks/side-channel-map@1.0.1","vendor-chunks/safe-buffer@5.2.1","vendor-chunks/side-channel@1.1.0","vendor-chunks/get-proto@1.0.1","vendor-chunks/call-bind-apply-helpers@1.0.2","vendor-chunks/buffer-equal-constant-time@1.0.1","vendor-chunks/dunder-proto@1.0.1","vendor-chunks/math-intrinsics@1.1.0","vendor-chunks/call-bound@1.0.4","vendor-chunks/is-stream@2.0.1","vendor-chunks/es-errors@1.3.0","vendor-chunks/gopd@1.2.0","vendor-chunks/es-define-property@1.0.1","vendor-chunks/hasown@2.0.2","vendor-chunks/es-object-atoms@1.1.1"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgoogle-sheets%2Fconnect%2Froute&page=%2Fapi%2Fgoogle-sheets%2Fconnect%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgoogle-sheets%2Fconnect%2Froute.ts&appDir=C%3A%5CUsers%5CPC-LPK%5CDocuments%5CHKUYA%5Cattendance%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CPC-LPK%5CDocuments%5CHKUYA%5Cattendance&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();