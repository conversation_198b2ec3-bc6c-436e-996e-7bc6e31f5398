"use client"

import { useState, useMemo } from "react"
import {
  initialActivities,
  initialParticipants,
  committees as initialCommittees,
  initialSessions,
} from "../data/initial-data"
import type { Activity, Participant, Session, SessionParticipant } from "../types"

export function useAttendanceData(selectedActivityId: string | null) {
  // 基礎數據狀態
  const [activities, setActivities] = useState<Activity[]>(initialActivities)
  const [allParticipants, setAllParticipants] = useState<Participant[]>(initialParticipants)
  const [sessionParticipants, setSessionParticipants] = useState<SessionParticipant[]>([
    // 初始化屆別參加者數據
    {
      id: "sp1",
      participantId: "1",
      sessionId: "31",
      name: "張三",
      category: "核心成員",
      joinDate: "2024-01-01",
      isActive: true,
    },
    {
      id: "sp2",
      participantId: "2",
      sessionId: "31",
      name: "李四",
      category: "核心成員",
      joinDate: "2024-01-01",
      isActive: true,
    },
    {
      id: "sp3",
      participantId: "3",
      sessionId: "31",
      name: "王五",
      category: "一般成員",
      joinDate: "2024-01-15",
      isActive: true,
    },
    {
      id: "sp4",
      participantId: "3",
      sessionId: "30",
      name: "王五",
      category: "核心成員",
      joinDate: "2023-01-01",
      isActive: false,
    },
    {
      id: "sp5",
      participantId: "4",
      sessionId: "31",
      name: "趙六",
      category: "一般成員",
      joinDate: "2024-01-01",
      isActive: true,
    },
    {
      id: "sp6",
      participantId: "5",
      sessionId: "31",
      name: "劉七",
      category: "新成員",
      joinDate: "2024-02-01",
      isActive: true,
    },
  ])
  const [committees, setCommittees] = useState<string[]>(initialCommittees)
  const [sessions, setSessions] = useState<Session[]>(initialSessions)
  const [selectedSessionId, setSelectedSessionId] = useState<string | null>(
    initialSessions.find((s) => s.isActive)?.id || initialSessions[0]?.id || null,
  )

  // 計算衍生數據
  const selectedActivity = useMemo(
    () => activities.find((activity) => activity.id === selectedActivityId) || null,
    [activities, selectedActivityId],
  )

  const currentSessionCommittees = useMemo(() => {
    const currentSession = sessions.find((s) => s.id === selectedSessionId)
    return currentSession?.committees || []
  }, [sessions, selectedSessionId])

  const currentSessionActivities = useMemo(() => {
    return activities.filter((a) => a.sessionId === selectedSessionId)
  }, [activities, selectedSessionId])

  return {
    // 狀態
    activities,
    allParticipants,
    sessionParticipants,
    committees,
    sessions,
    selectedSessionId,

    // 狀態設置函數
    setActivities,
    setAllParticipants,
    setSessionParticipants,
    setCommittees,
    setSessions,
    setSelectedSessionId,

    // 計算值
    selectedActivity,
    currentSessionCommittees,
    currentSessionActivities,
  }
}
