{"name": "attendance", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"autoprefixer": "^10.4.20", "clsx": "^2.1.1", "dotenv": "^16.3.1", "google-auth-library": "^9.4.0", "googleapis": "^129.0.0", "lucide-react": "^0.454.0", "next": "15.2.4", "papaparse": "latest", "react": "^18.2.0", "react-dom": "^18.2.0", "recharts": "latest", "tailwind-merge": "^2.5.5", "xlsx": "latest"}, "devDependencies": {"@types/node": "^18.19.104", "@types/papaparse": "^5.3.16", "@types/react": "^18.3.23", "@types/react-dom": "^18", "postcss": "^8", "tailwindcss": "^3.4.17", "typescript": "^5.8.3"}}