/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { businessprofileperformance_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof businessprofileperformance_v1.Businessprofileperformance;
};
export declare function businessprofileperformance(version: 'v1'): businessprofileperformance_v1.Businessprofileperformance;
export declare function businessprofileperformance(options: businessprofileperformance_v1.Options): businessprofileperformance_v1.Businessprofileperformance;
declare const auth: AuthPlus;
export { auth };
export { businessprofileperformance_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
