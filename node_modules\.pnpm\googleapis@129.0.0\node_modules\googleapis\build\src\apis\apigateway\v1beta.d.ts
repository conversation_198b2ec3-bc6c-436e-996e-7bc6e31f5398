/// <reference types="node" />
import { OAuth2Client, JW<PERSON>, Compute, UserRefreshClient, BaseExternalAccountClient, GaxiosPromise, GoogleConfigurable, MethodOptions, StreamMethodOptions, GlobalOptions, GoogleAuth, BodyResponseCallback, APIRequestContext } from 'googleapis-common';
import { Readable } from 'stream';
export declare namespace apigateway_v1beta {
    export interface Options extends GlobalOptions {
        version: 'v1beta';
    }
    interface StandardParameters {
        /**
         * Auth client or API Key for the request
         */
        auth?: string | OAuth2Client | JWT | Compute | UserRefreshClient | BaseExternalAccountClient | GoogleAuth;
        /**
         * V1 error format.
         */
        '$.xgafv'?: string;
        /**
         * OAuth access token.
         */
        access_token?: string;
        /**
         * Data format for response.
         */
        alt?: string;
        /**
         * JSONP
         */
        callback?: string;
        /**
         * Selector specifying which fields to include in a partial response.
         */
        fields?: string;
        /**
         * API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.
         */
        key?: string;
        /**
         * OAuth 2.0 token for the current user.
         */
        oauth_token?: string;
        /**
         * Returns response with indentations and line breaks.
         */
        prettyPrint?: boolean;
        /**
         * Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.
         */
        quotaUser?: string;
        /**
         * Legacy upload protocol for media (e.g. "media", "multipart").
         */
        uploadType?: string;
        /**
         * Upload protocol for media (e.g. "raw", "multipart").
         */
        upload_protocol?: string;
    }
    /**
     * API Gateway API
     *
     *
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const apigateway = google.apigateway('v1beta');
     * ```
     */
    export class Apigateway {
        context: APIRequestContext;
        projects: Resource$Projects;
        constructor(options: GlobalOptions, google?: GoogleConfigurable);
    }
    /**
     * An API that can be served by one or more Gateways.
     */
    export interface Schema$ApigatewayApi {
        /**
         * Output only. Created time.
         */
        createTime?: string | null;
        /**
         * Optional. Display name.
         */
        displayName?: string | null;
        /**
         * Optional. Resource labels to represent user-provided metadata. Refer to cloud documentation on labels for more details. https://cloud.google.com/compute/docs/labeling-resources
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Optional. Immutable. The name of a Google Managed Service ( https://cloud.google.com/service-infrastructure/docs/glossary#managed). If not specified, a new Service will automatically be created in the same project as this API.
         */
        managedService?: string | null;
        /**
         * Output only. Resource name of the API. Format: projects/{project\}/locations/global/apis/{api\}
         */
        name?: string | null;
        /**
         * Output only. State of the API.
         */
        state?: string | null;
        /**
         * Output only. Updated time.
         */
        updateTime?: string | null;
    }
    /**
     * An API Configuration is a combination of settings for both the Managed Service and Gateways serving this API Config.
     */
    export interface Schema$ApigatewayApiConfig {
        /**
         * Output only. Created time.
         */
        createTime?: string | null;
        /**
         * Optional. Display name.
         */
        displayName?: string | null;
        /**
         * Immutable. Gateway specific configuration.
         */
        gatewayConfig?: Schema$ApigatewayGatewayConfig;
        /**
         * Immutable. The Google Cloud IAM Service Account that Gateways serving this config should use to authenticate to other services. This may either be the Service Account's email (`{ACCOUNT_ID\}@{PROJECT\}.iam.gserviceaccount.com`) or its full resource name (`projects/{PROJECT\}/accounts/{UNIQUE_ID\}`). This is most often used when the service is a GCP resource such as a Cloud Run Service or an IAP-secured service.
         */
        gatewayServiceAccount?: string | null;
        /**
         * Optional. gRPC service definition files. If specified, openapi_documents must not be included.
         */
        grpcServices?: Schema$ApigatewayApiConfigGrpcServiceDefinition[];
        /**
         * Optional. Resource labels to represent user-provided metadata. Refer to cloud documentation on labels for more details. https://cloud.google.com/compute/docs/labeling-resources
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Optional. Service Configuration files. At least one must be included when using gRPC service definitions. See https://cloud.google.com/endpoints/docs/grpc/grpc-service-config#service_configuration_overview for the expected file contents. If multiple files are specified, the files are merged with the following rules: * All singular scalar fields are merged using "last one wins" semantics in the order of the files uploaded. * Repeated fields are concatenated. * Singular embedded messages are merged using these rules for nested fields.
         */
        managedServiceConfigs?: Schema$ApigatewayApiConfigFile[];
        /**
         * Output only. Resource name of the API Config. Format: projects/{project\}/locations/global/apis/{api\}/configs/{api_config\}
         */
        name?: string | null;
        /**
         * Optional. OpenAPI specification documents. If specified, grpc_services and managed_service_configs must not be included.
         */
        openapiDocuments?: Schema$ApigatewayApiConfigOpenApiDocument[];
        /**
         * Output only. The ID of the associated Service Config ( https://cloud.google.com/service-infrastructure/docs/glossary#config).
         */
        serviceConfigId?: string | null;
        /**
         * Output only. State of the API Config.
         */
        state?: string | null;
        /**
         * Output only. Updated time.
         */
        updateTime?: string | null;
    }
    /**
     * A lightweight description of a file.
     */
    export interface Schema$ApigatewayApiConfigFile {
        /**
         * The bytes that constitute the file.
         */
        contents?: string | null;
        /**
         * The file path (full or relative path). This is typically the path of the file when it is uploaded.
         */
        path?: string | null;
    }
    /**
     * A gRPC service definition.
     */
    export interface Schema$ApigatewayApiConfigGrpcServiceDefinition {
        /**
         * Input only. File descriptor set, generated by protoc. To generate, use protoc with imports and source info included. For an example test.proto file, the following command would put the value in a new file named out.pb. $ protoc --include_imports --include_source_info test.proto -o out.pb
         */
        fileDescriptorSet?: Schema$ApigatewayApiConfigFile;
        /**
         * Optional. Uncompiled proto files associated with the descriptor set, used for display purposes (server-side compilation is not supported). These should match the inputs to 'protoc' command used to generate file_descriptor_set.
         */
        source?: Schema$ApigatewayApiConfigFile[];
    }
    /**
     * An OpenAPI Specification Document describing an API.
     */
    export interface Schema$ApigatewayApiConfigOpenApiDocument {
        /**
         * The OpenAPI Specification document file.
         */
        document?: Schema$ApigatewayApiConfigFile;
    }
    /**
     * Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs. If there are AuditConfigs for both `allServices` and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted. Example Policy with multiple AuditConfigs: { "audit_configs": [ { "service": "allServices", "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [ "user:<EMAIL>" ] \}, { "log_type": "DATA_WRITE" \}, { "log_type": "ADMIN_READ" \} ] \}, { "service": "sampleservice.googleapis.com", "audit_log_configs": [ { "log_type": "DATA_READ" \}, { "log_type": "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] \} ] \} ] \} For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It also exempts `<EMAIL>` from DATA_READ logging, and `<EMAIL>` from DATA_WRITE logging.
     */
    export interface Schema$ApigatewayAuditConfig {
        /**
         * The configuration for logging of each type of permission.
         */
        auditLogConfigs?: Schema$ApigatewayAuditLogConfig[];
        /**
         * Specifies a service that will be enabled for audit logging. For example, `storage.googleapis.com`, `cloudsql.googleapis.com`. `allServices` is a special value that covers all services.
         */
        service?: string | null;
    }
    /**
     * Provides the configuration for logging a type of permissions. Example: { "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [ "user:<EMAIL>" ] \}, { "log_type": "DATA_WRITE" \} ] \} This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging.
     */
    export interface Schema$ApigatewayAuditLogConfig {
        /**
         * Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.
         */
        exemptedMembers?: string[] | null;
        /**
         * The log type that this config enables.
         */
        logType?: string | null;
    }
    /**
     * Configuration for all backends.
     */
    export interface Schema$ApigatewayBackendConfig {
        /**
         * Google Cloud IAM service account used to sign OIDC tokens for backends that have authentication configured (https://cloud.google.com/service-infrastructure/docs/service-management/reference/rest/v1/services.configs#backend). This may either be the Service Account's email (i.e. "{ACCOUNT_ID\}@{PROJECT\}.iam.gserviceaccount.com") or its full resource name (i.e. "projects/{PROJECT\}/accounts/{UNIQUE_ID\}"). This is most often used when the backend is a GCP resource such as a Cloud Run Service or an IAP-secured service. Note that this token is always sent as an authorization header bearer token. The audience of the OIDC token is configured in the associated Service Config in the BackendRule option (https://github.com/googleapis/googleapis/blob/master/google/api/backend.proto#L125).
         */
        googleServiceAccount?: string | null;
    }
    /**
     * Associates `members`, or principals, with a `role`.
     */
    export interface Schema$ApigatewayBinding {
        /**
         * The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        condition?: Schema$ApigatewayExpr;
        /**
         * Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid\}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid\}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid\}.svc.id.goog[{namespace\}/{kubernetes-sa\}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid\}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain\}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `deleted:user:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid\}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid\}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid\}` and the recovered group retains the role in the binding.
         */
        members?: string[] | null;
        /**
         * Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`.
         */
        role?: string | null;
    }
    /**
     * The request message for Operations.CancelOperation.
     */
    export interface Schema$ApigatewayCancelOperationRequest {
    }
    /**
     * Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: "Summary size limit" description: "Determines if a summary is less than 100 chars" expression: "document.summary.size() < 100" Example (Equality): title: "Requestor is owner" description: "Determines if requestor is the document owner" expression: "document.owner == request.auth.claims.email" Example (Logic): title: "Public documents" description: "Determine whether the document should be publicly visible" expression: "document.type != 'private' && document.type != 'internal'" Example (Data Manipulation): title: "Notification string" description: "Create a notification string with a timestamp." expression: "'New message received at ' + string(document.create_time)" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.
     */
    export interface Schema$ApigatewayExpr {
        /**
         * Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.
         */
        description?: string | null;
        /**
         * Textual representation of an expression in Common Expression Language syntax.
         */
        expression?: string | null;
        /**
         * Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.
         */
        location?: string | null;
        /**
         * Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.
         */
        title?: string | null;
    }
    /**
     * A Gateway is an API-aware HTTP proxy. It performs API-Method and/or API-Consumer specific actions based on an API Config such as authentication, policy enforcement, and backend selection.
     */
    export interface Schema$ApigatewayGateway {
        /**
         * Required. Resource name of the API Config for this Gateway. Format: projects/{project\}/locations/global/apis/{api\}/configs/{apiConfig\}
         */
        apiConfig?: string | null;
        /**
         * Output only. Created time.
         */
        createTime?: string | null;
        /**
         * Output only. The default API Gateway host name of the form `{gateway_id\}-{hash\}.{region_code\}.gateway.dev`.
         */
        defaultHostname?: string | null;
        /**
         * Optional. Display name.
         */
        displayName?: string | null;
        /**
         * Optional. Resource labels to represent user-provided metadata. Refer to cloud documentation on labels for more details. https://cloud.google.com/compute/docs/labeling-resources
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Output only. Resource name of the Gateway. Format: projects/{project\}/locations/{location\}/gateways/{gateway\}
         */
        name?: string | null;
        /**
         * Output only. The current state of the Gateway.
         */
        state?: string | null;
        /**
         * Output only. Updated time.
         */
        updateTime?: string | null;
    }
    /**
     * Configuration settings for Gateways.
     */
    export interface Schema$ApigatewayGatewayConfig {
        /**
         * Required. Backend settings that are applied to all backends of the Gateway.
         */
        backendConfig?: Schema$ApigatewayBackendConfig;
    }
    /**
     * Response message for ApiGatewayService.ListApiConfigs
     */
    export interface Schema$ApigatewayListApiConfigsResponse {
        /**
         * API Configs.
         */
        apiConfigs?: Schema$ApigatewayApiConfig[];
        /**
         * Next page token.
         */
        nextPageToken?: string | null;
        /**
         * Locations that could not be reached.
         */
        unreachableLocations?: string[] | null;
    }
    /**
     * Response message for ApiGatewayService.ListApis
     */
    export interface Schema$ApigatewayListApisResponse {
        /**
         * APIs.
         */
        apis?: Schema$ApigatewayApi[];
        /**
         * Next page token.
         */
        nextPageToken?: string | null;
        /**
         * Locations that could not be reached.
         */
        unreachableLocations?: string[] | null;
    }
    /**
     * Response message for ApiGatewayService.ListGateways
     */
    export interface Schema$ApigatewayListGatewaysResponse {
        /**
         * Gateways.
         */
        gateways?: Schema$ApigatewayGateway[];
        /**
         * Next page token.
         */
        nextPageToken?: string | null;
        /**
         * Locations that could not be reached.
         */
        unreachableLocations?: string[] | null;
    }
    /**
     * The response message for Locations.ListLocations.
     */
    export interface Schema$ApigatewayListLocationsResponse {
        /**
         * A list of locations that matches the specified filter in the request.
         */
        locations?: Schema$ApigatewayLocation[];
        /**
         * The standard List next-page token.
         */
        nextPageToken?: string | null;
    }
    /**
     * The response message for Operations.ListOperations.
     */
    export interface Schema$ApigatewayListOperationsResponse {
        /**
         * The standard List next-page token.
         */
        nextPageToken?: string | null;
        /**
         * A list of operations that matches the specified filter in the request.
         */
        operations?: Schema$ApigatewayOperation[];
    }
    /**
     * A resource that represents a Google Cloud location.
     */
    export interface Schema$ApigatewayLocation {
        /**
         * The friendly name for this location, typically a nearby city name. For example, "Tokyo".
         */
        displayName?: string | null;
        /**
         * Cross-service attributes for the location. For example {"cloud.googleapis.com/region": "us-east1"\}
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * The canonical id for this location. For example: `"us-east1"`.
         */
        locationId?: string | null;
        /**
         * Service-specific metadata. For example the available capacity at the given location.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * Resource name for the location, which may vary between implementations. For example: `"projects/example-project/locations/us-east1"`
         */
        name?: string | null;
    }
    /**
     * This resource represents a long-running operation that is the result of a network API call.
     */
    export interface Schema$ApigatewayOperation {
        /**
         * If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.
         */
        done?: boolean | null;
        /**
         * The error result of the operation in case of failure or cancellation.
         */
        error?: Schema$ApigatewayStatus;
        /**
         * Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id\}`.
         */
        name?: string | null;
        /**
         * The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
         */
        response?: {
            [key: string]: any;
        } | null;
    }
    /**
     * Represents the metadata of the long-running operation.
     */
    export interface Schema$ApigatewayOperationMetadata {
        /**
         * Output only. API version used to start the operation.
         */
        apiVersion?: string | null;
        /**
         * Output only. The time the operation was created.
         */
        createTime?: string | null;
        /**
         * Output only. Diagnostics generated during processing of configuration source files.
         */
        diagnostics?: Schema$ApigatewayOperationMetadataDiagnostic[];
        /**
         * Output only. The time the operation finished running.
         */
        endTime?: string | null;
        /**
         * Output only. Identifies whether the user has requested cancellation of the operation. Operations that have successfully been cancelled have Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
         */
        requestedCancellation?: boolean | null;
        /**
         * Output only. Human-readable status of the operation, if any.
         */
        statusMessage?: string | null;
        /**
         * Output only. Server-defined resource path for the target of the operation.
         */
        target?: string | null;
        /**
         * Output only. Name of the verb executed by the operation.
         */
        verb?: string | null;
    }
    /**
     * Diagnostic information from configuration processing.
     */
    export interface Schema$ApigatewayOperationMetadataDiagnostic {
        /**
         * Location of the diagnostic.
         */
        location?: string | null;
        /**
         * The diagnostic message.
         */
        message?: string | null;
    }
    /**
     * An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { "bindings": [ { "role": "roles/resourcemanager.organizationAdmin", "members": [ "user:<EMAIL>", "group:<EMAIL>", "domain:google.com", "serviceAccount:<EMAIL>" ] \}, { "role": "roles/resourcemanager.organizationViewer", "members": [ "user:<EMAIL>" ], "condition": { "title": "expirable access", "description": "Does not grant access after Sep 2020", "expression": "request.time < timestamp('2020-10-01T00:00:00.000Z')", \} \} ], "etag": "BwWWja0YfJA=", "version": 3 \} ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).
     */
    export interface Schema$ApigatewayPolicy {
        /**
         * Specifies cloud audit logging configuration for this policy.
         */
        auditConfigs?: Schema$ApigatewayAuditConfig[];
        /**
         * Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.
         */
        bindings?: Schema$ApigatewayBinding[];
        /**
         * `etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.
         */
        etag?: string | null;
        /**
         * Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        version?: number | null;
    }
    /**
     * Request message for `SetIamPolicy` method.
     */
    export interface Schema$ApigatewaySetIamPolicyRequest {
        /**
         * REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them.
         */
        policy?: Schema$ApigatewayPolicy;
        /**
         * OPTIONAL: A FieldMask specifying which fields of the policy to modify. Only the fields in the mask will be modified. If no mask is provided, the following default mask is used: `paths: "bindings, etag"`
         */
        updateMask?: string | null;
    }
    /**
     * The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).
     */
    export interface Schema$ApigatewayStatus {
        /**
         * The status code, which should be an enum value of google.rpc.Code.
         */
        code?: number | null;
        /**
         * A list of messages that carry the error details. There is a common set of message types for APIs to use.
         */
        details?: Array<{
            [key: string]: any;
        }> | null;
        /**
         * A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.
         */
        message?: string | null;
    }
    /**
     * Request message for `TestIamPermissions` method.
     */
    export interface Schema$ApigatewayTestIamPermissionsRequest {
        /**
         * The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).
         */
        permissions?: string[] | null;
    }
    /**
     * Response message for `TestIamPermissions` method.
     */
    export interface Schema$ApigatewayTestIamPermissionsResponse {
        /**
         * A subset of `TestPermissionsRequest.permissions` that the caller is allowed.
         */
        permissions?: string[] | null;
    }
    /**
     * A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); \}
     */
    export interface Schema$Empty {
    }
    export class Resource$Projects {
        context: APIRequestContext;
        locations: Resource$Projects$Locations;
        constructor(context: APIRequestContext);
    }
    export class Resource$Projects$Locations {
        context: APIRequestContext;
        apis: Resource$Projects$Locations$Apis;
        gateways: Resource$Projects$Locations$Gateways;
        operations: Resource$Projects$Locations$Operations;
        constructor(context: APIRequestContext);
        /**
         * Gets information about a location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Get, options?: MethodOptions): GaxiosPromise<Schema$ApigatewayLocation>;
        get(params: Params$Resource$Projects$Locations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Get, options: MethodOptions | BodyResponseCallback<Schema$ApigatewayLocation>, callback: BodyResponseCallback<Schema$ApigatewayLocation>): void;
        get(params: Params$Resource$Projects$Locations$Get, callback: BodyResponseCallback<Schema$ApigatewayLocation>): void;
        get(callback: BodyResponseCallback<Schema$ApigatewayLocation>): void;
        /**
         * Lists information about the supported locations for this service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$List, options?: MethodOptions): GaxiosPromise<Schema$ApigatewayListLocationsResponse>;
        list(params: Params$Resource$Projects$Locations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$List, options: MethodOptions | BodyResponseCallback<Schema$ApigatewayListLocationsResponse>, callback: BodyResponseCallback<Schema$ApigatewayListLocationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$List, callback: BodyResponseCallback<Schema$ApigatewayListLocationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ApigatewayListLocationsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Get extends StandardParameters {
        /**
         * Resource name for the location.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$List extends StandardParameters {
        /**
         * A filter to narrow down results to a preferred subset. The filtering language accepts strings like `"displayName=tokyo"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).
         */
        filter?: string;
        /**
         * The resource that owns the locations collection, if applicable.
         */
        name?: string;
        /**
         * The maximum number of results to return. If not set, the service selects a default.
         */
        pageSize?: number;
        /**
         * A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.
         */
        pageToken?: string;
    }
    export class Resource$Projects$Locations$Apis {
        context: APIRequestContext;
        configs: Resource$Projects$Locations$Apis$Configs;
        constructor(context: APIRequestContext);
        /**
         * Creates a new Api in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Apis$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Locations$Apis$Create, options?: MethodOptions): GaxiosPromise<Schema$ApigatewayOperation>;
        create(params: Params$Resource$Projects$Locations$Apis$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Apis$Create, options: MethodOptions | BodyResponseCallback<Schema$ApigatewayOperation>, callback: BodyResponseCallback<Schema$ApigatewayOperation>): void;
        create(params: Params$Resource$Projects$Locations$Apis$Create, callback: BodyResponseCallback<Schema$ApigatewayOperation>): void;
        create(callback: BodyResponseCallback<Schema$ApigatewayOperation>): void;
        /**
         * Deletes a single Api.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Apis$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Locations$Apis$Delete, options?: MethodOptions): GaxiosPromise<Schema$ApigatewayOperation>;
        delete(params: Params$Resource$Projects$Locations$Apis$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Apis$Delete, options: MethodOptions | BodyResponseCallback<Schema$ApigatewayOperation>, callback: BodyResponseCallback<Schema$ApigatewayOperation>): void;
        delete(params: Params$Resource$Projects$Locations$Apis$Delete, callback: BodyResponseCallback<Schema$ApigatewayOperation>): void;
        delete(callback: BodyResponseCallback<Schema$ApigatewayOperation>): void;
        /**
         * Gets details of a single Api.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Apis$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Apis$Get, options?: MethodOptions): GaxiosPromise<Schema$ApigatewayApi>;
        get(params: Params$Resource$Projects$Locations$Apis$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Apis$Get, options: MethodOptions | BodyResponseCallback<Schema$ApigatewayApi>, callback: BodyResponseCallback<Schema$ApigatewayApi>): void;
        get(params: Params$Resource$Projects$Locations$Apis$Get, callback: BodyResponseCallback<Schema$ApigatewayApi>): void;
        get(callback: BodyResponseCallback<Schema$ApigatewayApi>): void;
        /**
         * Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getIamPolicy(params: Params$Resource$Projects$Locations$Apis$Getiampolicy, options: StreamMethodOptions): GaxiosPromise<Readable>;
        getIamPolicy(params?: Params$Resource$Projects$Locations$Apis$Getiampolicy, options?: MethodOptions): GaxiosPromise<Schema$ApigatewayPolicy>;
        getIamPolicy(params: Params$Resource$Projects$Locations$Apis$Getiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Apis$Getiampolicy, options: MethodOptions | BodyResponseCallback<Schema$ApigatewayPolicy>, callback: BodyResponseCallback<Schema$ApigatewayPolicy>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Apis$Getiampolicy, callback: BodyResponseCallback<Schema$ApigatewayPolicy>): void;
        getIamPolicy(callback: BodyResponseCallback<Schema$ApigatewayPolicy>): void;
        /**
         * Lists Apis in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Apis$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Apis$List, options?: MethodOptions): GaxiosPromise<Schema$ApigatewayListApisResponse>;
        list(params: Params$Resource$Projects$Locations$Apis$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Apis$List, options: MethodOptions | BodyResponseCallback<Schema$ApigatewayListApisResponse>, callback: BodyResponseCallback<Schema$ApigatewayListApisResponse>): void;
        list(params: Params$Resource$Projects$Locations$Apis$List, callback: BodyResponseCallback<Schema$ApigatewayListApisResponse>): void;
        list(callback: BodyResponseCallback<Schema$ApigatewayListApisResponse>): void;
        /**
         * Updates the parameters of a single Api.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Apis$Patch, options: StreamMethodOptions): GaxiosPromise<Readable>;
        patch(params?: Params$Resource$Projects$Locations$Apis$Patch, options?: MethodOptions): GaxiosPromise<Schema$ApigatewayOperation>;
        patch(params: Params$Resource$Projects$Locations$Apis$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Apis$Patch, options: MethodOptions | BodyResponseCallback<Schema$ApigatewayOperation>, callback: BodyResponseCallback<Schema$ApigatewayOperation>): void;
        patch(params: Params$Resource$Projects$Locations$Apis$Patch, callback: BodyResponseCallback<Schema$ApigatewayOperation>): void;
        patch(callback: BodyResponseCallback<Schema$ApigatewayOperation>): void;
        /**
         * Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setIamPolicy(params: Params$Resource$Projects$Locations$Apis$Setiampolicy, options: StreamMethodOptions): GaxiosPromise<Readable>;
        setIamPolicy(params?: Params$Resource$Projects$Locations$Apis$Setiampolicy, options?: MethodOptions): GaxiosPromise<Schema$ApigatewayPolicy>;
        setIamPolicy(params: Params$Resource$Projects$Locations$Apis$Setiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Apis$Setiampolicy, options: MethodOptions | BodyResponseCallback<Schema$ApigatewayPolicy>, callback: BodyResponseCallback<Schema$ApigatewayPolicy>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Apis$Setiampolicy, callback: BodyResponseCallback<Schema$ApigatewayPolicy>): void;
        setIamPolicy(callback: BodyResponseCallback<Schema$ApigatewayPolicy>): void;
        /**
         * Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        testIamPermissions(params: Params$Resource$Projects$Locations$Apis$Testiampermissions, options: StreamMethodOptions): GaxiosPromise<Readable>;
        testIamPermissions(params?: Params$Resource$Projects$Locations$Apis$Testiampermissions, options?: MethodOptions): GaxiosPromise<Schema$ApigatewayTestIamPermissionsResponse>;
        testIamPermissions(params: Params$Resource$Projects$Locations$Apis$Testiampermissions, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Apis$Testiampermissions, options: MethodOptions | BodyResponseCallback<Schema$ApigatewayTestIamPermissionsResponse>, callback: BodyResponseCallback<Schema$ApigatewayTestIamPermissionsResponse>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Apis$Testiampermissions, callback: BodyResponseCallback<Schema$ApigatewayTestIamPermissionsResponse>): void;
        testIamPermissions(callback: BodyResponseCallback<Schema$ApigatewayTestIamPermissionsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Apis$Create extends StandardParameters {
        /**
         * Required. Identifier to assign to the API. Must be unique within scope of the parent resource.
         */
        apiId?: string;
        /**
         * Required. Parent resource of the API, of the form: `projects/x/locations/global`
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ApigatewayApi;
    }
    export interface Params$Resource$Projects$Locations$Apis$Delete extends StandardParameters {
        /**
         * Required. Resource name of the form: `projects/x/locations/global/apis/x`
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Apis$Get extends StandardParameters {
        /**
         * Required. Resource name of the form: `projects/x/locations/global/apis/x`
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Apis$Getiampolicy extends StandardParameters {
        /**
         * Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        'options.requestedPolicyVersion'?: number;
        /**
         * REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
    }
    export interface Params$Resource$Projects$Locations$Apis$List extends StandardParameters {
        /**
         * Filter.
         */
        filter?: string;
        /**
         * Order by parameters.
         */
        orderBy?: string;
        /**
         * Page size.
         */
        pageSize?: number;
        /**
         * Page token.
         */
        pageToken?: string;
        /**
         * Required. Parent resource of the API, of the form: `projects/x/locations/global`
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Apis$Patch extends StandardParameters {
        /**
         * Output only. Resource name of the API. Format: projects/{project\}/locations/global/apis/{api\}
         */
        name?: string;
        /**
         * Field mask is used to specify the fields to be overwritten in the Api resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ApigatewayApi;
    }
    export interface Params$Resource$Projects$Locations$Apis$Setiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ApigatewaySetIamPolicyRequest;
    }
    export interface Params$Resource$Projects$Locations$Apis$Testiampermissions extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ApigatewayTestIamPermissionsRequest;
    }
    export class Resource$Projects$Locations$Apis$Configs {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a new ApiConfig in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Apis$Configs$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Locations$Apis$Configs$Create, options?: MethodOptions): GaxiosPromise<Schema$ApigatewayOperation>;
        create(params: Params$Resource$Projects$Locations$Apis$Configs$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Apis$Configs$Create, options: MethodOptions | BodyResponseCallback<Schema$ApigatewayOperation>, callback: BodyResponseCallback<Schema$ApigatewayOperation>): void;
        create(params: Params$Resource$Projects$Locations$Apis$Configs$Create, callback: BodyResponseCallback<Schema$ApigatewayOperation>): void;
        create(callback: BodyResponseCallback<Schema$ApigatewayOperation>): void;
        /**
         * Deletes a single ApiConfig.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Apis$Configs$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Locations$Apis$Configs$Delete, options?: MethodOptions): GaxiosPromise<Schema$ApigatewayOperation>;
        delete(params: Params$Resource$Projects$Locations$Apis$Configs$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Apis$Configs$Delete, options: MethodOptions | BodyResponseCallback<Schema$ApigatewayOperation>, callback: BodyResponseCallback<Schema$ApigatewayOperation>): void;
        delete(params: Params$Resource$Projects$Locations$Apis$Configs$Delete, callback: BodyResponseCallback<Schema$ApigatewayOperation>): void;
        delete(callback: BodyResponseCallback<Schema$ApigatewayOperation>): void;
        /**
         * Gets details of a single ApiConfig.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Apis$Configs$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Apis$Configs$Get, options?: MethodOptions): GaxiosPromise<Schema$ApigatewayApiConfig>;
        get(params: Params$Resource$Projects$Locations$Apis$Configs$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Apis$Configs$Get, options: MethodOptions | BodyResponseCallback<Schema$ApigatewayApiConfig>, callback: BodyResponseCallback<Schema$ApigatewayApiConfig>): void;
        get(params: Params$Resource$Projects$Locations$Apis$Configs$Get, callback: BodyResponseCallback<Schema$ApigatewayApiConfig>): void;
        get(callback: BodyResponseCallback<Schema$ApigatewayApiConfig>): void;
        /**
         * Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getIamPolicy(params: Params$Resource$Projects$Locations$Apis$Configs$Getiampolicy, options: StreamMethodOptions): GaxiosPromise<Readable>;
        getIamPolicy(params?: Params$Resource$Projects$Locations$Apis$Configs$Getiampolicy, options?: MethodOptions): GaxiosPromise<Schema$ApigatewayPolicy>;
        getIamPolicy(params: Params$Resource$Projects$Locations$Apis$Configs$Getiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Apis$Configs$Getiampolicy, options: MethodOptions | BodyResponseCallback<Schema$ApigatewayPolicy>, callback: BodyResponseCallback<Schema$ApigatewayPolicy>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Apis$Configs$Getiampolicy, callback: BodyResponseCallback<Schema$ApigatewayPolicy>): void;
        getIamPolicy(callback: BodyResponseCallback<Schema$ApigatewayPolicy>): void;
        /**
         * Lists ApiConfigs in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Apis$Configs$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Apis$Configs$List, options?: MethodOptions): GaxiosPromise<Schema$ApigatewayListApiConfigsResponse>;
        list(params: Params$Resource$Projects$Locations$Apis$Configs$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Apis$Configs$List, options: MethodOptions | BodyResponseCallback<Schema$ApigatewayListApiConfigsResponse>, callback: BodyResponseCallback<Schema$ApigatewayListApiConfigsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Apis$Configs$List, callback: BodyResponseCallback<Schema$ApigatewayListApiConfigsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ApigatewayListApiConfigsResponse>): void;
        /**
         * Updates the parameters of a single ApiConfig.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Apis$Configs$Patch, options: StreamMethodOptions): GaxiosPromise<Readable>;
        patch(params?: Params$Resource$Projects$Locations$Apis$Configs$Patch, options?: MethodOptions): GaxiosPromise<Schema$ApigatewayOperation>;
        patch(params: Params$Resource$Projects$Locations$Apis$Configs$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Apis$Configs$Patch, options: MethodOptions | BodyResponseCallback<Schema$ApigatewayOperation>, callback: BodyResponseCallback<Schema$ApigatewayOperation>): void;
        patch(params: Params$Resource$Projects$Locations$Apis$Configs$Patch, callback: BodyResponseCallback<Schema$ApigatewayOperation>): void;
        patch(callback: BodyResponseCallback<Schema$ApigatewayOperation>): void;
        /**
         * Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setIamPolicy(params: Params$Resource$Projects$Locations$Apis$Configs$Setiampolicy, options: StreamMethodOptions): GaxiosPromise<Readable>;
        setIamPolicy(params?: Params$Resource$Projects$Locations$Apis$Configs$Setiampolicy, options?: MethodOptions): GaxiosPromise<Schema$ApigatewayPolicy>;
        setIamPolicy(params: Params$Resource$Projects$Locations$Apis$Configs$Setiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Apis$Configs$Setiampolicy, options: MethodOptions | BodyResponseCallback<Schema$ApigatewayPolicy>, callback: BodyResponseCallback<Schema$ApigatewayPolicy>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Apis$Configs$Setiampolicy, callback: BodyResponseCallback<Schema$ApigatewayPolicy>): void;
        setIamPolicy(callback: BodyResponseCallback<Schema$ApigatewayPolicy>): void;
        /**
         * Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        testIamPermissions(params: Params$Resource$Projects$Locations$Apis$Configs$Testiampermissions, options: StreamMethodOptions): GaxiosPromise<Readable>;
        testIamPermissions(params?: Params$Resource$Projects$Locations$Apis$Configs$Testiampermissions, options?: MethodOptions): GaxiosPromise<Schema$ApigatewayTestIamPermissionsResponse>;
        testIamPermissions(params: Params$Resource$Projects$Locations$Apis$Configs$Testiampermissions, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Apis$Configs$Testiampermissions, options: MethodOptions | BodyResponseCallback<Schema$ApigatewayTestIamPermissionsResponse>, callback: BodyResponseCallback<Schema$ApigatewayTestIamPermissionsResponse>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Apis$Configs$Testiampermissions, callback: BodyResponseCallback<Schema$ApigatewayTestIamPermissionsResponse>): void;
        testIamPermissions(callback: BodyResponseCallback<Schema$ApigatewayTestIamPermissionsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Apis$Configs$Create extends StandardParameters {
        /**
         * Required. Identifier to assign to the API Config. Must be unique within scope of the parent resource.
         */
        apiConfigId?: string;
        /**
         * Required. Parent resource of the API Config, of the form: `projects/x/locations/global/apis/x`
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ApigatewayApiConfig;
    }
    export interface Params$Resource$Projects$Locations$Apis$Configs$Delete extends StandardParameters {
        /**
         * Required. Resource name of the form: `projects/x/locations/global/apis/x/configs/x`
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Apis$Configs$Get extends StandardParameters {
        /**
         * Required. Resource name of the form: `projects/x/locations/global/apis/x/configs/x`
         */
        name?: string;
        /**
         * Specifies which fields of the API Config are returned in the response. Defaults to `BASIC` view.
         */
        view?: string;
    }
    export interface Params$Resource$Projects$Locations$Apis$Configs$Getiampolicy extends StandardParameters {
        /**
         * Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        'options.requestedPolicyVersion'?: number;
        /**
         * REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
    }
    export interface Params$Resource$Projects$Locations$Apis$Configs$List extends StandardParameters {
        /**
         * Filter.
         */
        filter?: string;
        /**
         * Order by parameters.
         */
        orderBy?: string;
        /**
         * Page size.
         */
        pageSize?: number;
        /**
         * Page token.
         */
        pageToken?: string;
        /**
         * Required. Parent resource of the API Config, of the form: `projects/x/locations/global/apis/x`
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Apis$Configs$Patch extends StandardParameters {
        /**
         * Output only. Resource name of the API Config. Format: projects/{project\}/locations/global/apis/{api\}/configs/{api_config\}
         */
        name?: string;
        /**
         * Field mask is used to specify the fields to be overwritten in the ApiConfig resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ApigatewayApiConfig;
    }
    export interface Params$Resource$Projects$Locations$Apis$Configs$Setiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ApigatewaySetIamPolicyRequest;
    }
    export interface Params$Resource$Projects$Locations$Apis$Configs$Testiampermissions extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ApigatewayTestIamPermissionsRequest;
    }
    export class Resource$Projects$Locations$Gateways {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a new Gateway in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Gateways$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Locations$Gateways$Create, options?: MethodOptions): GaxiosPromise<Schema$ApigatewayOperation>;
        create(params: Params$Resource$Projects$Locations$Gateways$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Gateways$Create, options: MethodOptions | BodyResponseCallback<Schema$ApigatewayOperation>, callback: BodyResponseCallback<Schema$ApigatewayOperation>): void;
        create(params: Params$Resource$Projects$Locations$Gateways$Create, callback: BodyResponseCallback<Schema$ApigatewayOperation>): void;
        create(callback: BodyResponseCallback<Schema$ApigatewayOperation>): void;
        /**
         * Deletes a single Gateway.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Gateways$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Locations$Gateways$Delete, options?: MethodOptions): GaxiosPromise<Schema$ApigatewayOperation>;
        delete(params: Params$Resource$Projects$Locations$Gateways$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Gateways$Delete, options: MethodOptions | BodyResponseCallback<Schema$ApigatewayOperation>, callback: BodyResponseCallback<Schema$ApigatewayOperation>): void;
        delete(params: Params$Resource$Projects$Locations$Gateways$Delete, callback: BodyResponseCallback<Schema$ApigatewayOperation>): void;
        delete(callback: BodyResponseCallback<Schema$ApigatewayOperation>): void;
        /**
         * Gets details of a single Gateway.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Gateways$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Gateways$Get, options?: MethodOptions): GaxiosPromise<Schema$ApigatewayGateway>;
        get(params: Params$Resource$Projects$Locations$Gateways$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Gateways$Get, options: MethodOptions | BodyResponseCallback<Schema$ApigatewayGateway>, callback: BodyResponseCallback<Schema$ApigatewayGateway>): void;
        get(params: Params$Resource$Projects$Locations$Gateways$Get, callback: BodyResponseCallback<Schema$ApigatewayGateway>): void;
        get(callback: BodyResponseCallback<Schema$ApigatewayGateway>): void;
        /**
         * Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getIamPolicy(params: Params$Resource$Projects$Locations$Gateways$Getiampolicy, options: StreamMethodOptions): GaxiosPromise<Readable>;
        getIamPolicy(params?: Params$Resource$Projects$Locations$Gateways$Getiampolicy, options?: MethodOptions): GaxiosPromise<Schema$ApigatewayPolicy>;
        getIamPolicy(params: Params$Resource$Projects$Locations$Gateways$Getiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Gateways$Getiampolicy, options: MethodOptions | BodyResponseCallback<Schema$ApigatewayPolicy>, callback: BodyResponseCallback<Schema$ApigatewayPolicy>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Gateways$Getiampolicy, callback: BodyResponseCallback<Schema$ApigatewayPolicy>): void;
        getIamPolicy(callback: BodyResponseCallback<Schema$ApigatewayPolicy>): void;
        /**
         * Lists Gateways in a given project and location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Gateways$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Gateways$List, options?: MethodOptions): GaxiosPromise<Schema$ApigatewayListGatewaysResponse>;
        list(params: Params$Resource$Projects$Locations$Gateways$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Gateways$List, options: MethodOptions | BodyResponseCallback<Schema$ApigatewayListGatewaysResponse>, callback: BodyResponseCallback<Schema$ApigatewayListGatewaysResponse>): void;
        list(params: Params$Resource$Projects$Locations$Gateways$List, callback: BodyResponseCallback<Schema$ApigatewayListGatewaysResponse>): void;
        list(callback: BodyResponseCallback<Schema$ApigatewayListGatewaysResponse>): void;
        /**
         * Updates the parameters of a single Gateway.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Gateways$Patch, options: StreamMethodOptions): GaxiosPromise<Readable>;
        patch(params?: Params$Resource$Projects$Locations$Gateways$Patch, options?: MethodOptions): GaxiosPromise<Schema$ApigatewayOperation>;
        patch(params: Params$Resource$Projects$Locations$Gateways$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Gateways$Patch, options: MethodOptions | BodyResponseCallback<Schema$ApigatewayOperation>, callback: BodyResponseCallback<Schema$ApigatewayOperation>): void;
        patch(params: Params$Resource$Projects$Locations$Gateways$Patch, callback: BodyResponseCallback<Schema$ApigatewayOperation>): void;
        patch(callback: BodyResponseCallback<Schema$ApigatewayOperation>): void;
        /**
         * Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setIamPolicy(params: Params$Resource$Projects$Locations$Gateways$Setiampolicy, options: StreamMethodOptions): GaxiosPromise<Readable>;
        setIamPolicy(params?: Params$Resource$Projects$Locations$Gateways$Setiampolicy, options?: MethodOptions): GaxiosPromise<Schema$ApigatewayPolicy>;
        setIamPolicy(params: Params$Resource$Projects$Locations$Gateways$Setiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Gateways$Setiampolicy, options: MethodOptions | BodyResponseCallback<Schema$ApigatewayPolicy>, callback: BodyResponseCallback<Schema$ApigatewayPolicy>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Gateways$Setiampolicy, callback: BodyResponseCallback<Schema$ApigatewayPolicy>): void;
        setIamPolicy(callback: BodyResponseCallback<Schema$ApigatewayPolicy>): void;
        /**
         * Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        testIamPermissions(params: Params$Resource$Projects$Locations$Gateways$Testiampermissions, options: StreamMethodOptions): GaxiosPromise<Readable>;
        testIamPermissions(params?: Params$Resource$Projects$Locations$Gateways$Testiampermissions, options?: MethodOptions): GaxiosPromise<Schema$ApigatewayTestIamPermissionsResponse>;
        testIamPermissions(params: Params$Resource$Projects$Locations$Gateways$Testiampermissions, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Gateways$Testiampermissions, options: MethodOptions | BodyResponseCallback<Schema$ApigatewayTestIamPermissionsResponse>, callback: BodyResponseCallback<Schema$ApigatewayTestIamPermissionsResponse>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Gateways$Testiampermissions, callback: BodyResponseCallback<Schema$ApigatewayTestIamPermissionsResponse>): void;
        testIamPermissions(callback: BodyResponseCallback<Schema$ApigatewayTestIamPermissionsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Gateways$Create extends StandardParameters {
        /**
         * Required. Identifier to assign to the Gateway. Must be unique within scope of the parent resource.
         */
        gatewayId?: string;
        /**
         * Required. Parent resource of the Gateway, of the form: `projects/x/locations/x`
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ApigatewayGateway;
    }
    export interface Params$Resource$Projects$Locations$Gateways$Delete extends StandardParameters {
        /**
         * Required. Resource name of the form: `projects/x/locations/x/gateways/x`
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Gateways$Get extends StandardParameters {
        /**
         * Required. Resource name of the form: `projects/x/locations/x/gateways/x`
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Gateways$Getiampolicy extends StandardParameters {
        /**
         * Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        'options.requestedPolicyVersion'?: number;
        /**
         * REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
    }
    export interface Params$Resource$Projects$Locations$Gateways$List extends StandardParameters {
        /**
         * Filter.
         */
        filter?: string;
        /**
         * Order by parameters.
         */
        orderBy?: string;
        /**
         * Page size.
         */
        pageSize?: number;
        /**
         * Page token.
         */
        pageToken?: string;
        /**
         * Required. Parent resource of the Gateway, of the form: `projects/x/locations/x`
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Gateways$Patch extends StandardParameters {
        /**
         * Output only. Resource name of the Gateway. Format: projects/{project\}/locations/{location\}/gateways/{gateway\}
         */
        name?: string;
        /**
         * Field mask is used to specify the fields to be overwritten in the Gateway resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ApigatewayGateway;
    }
    export interface Params$Resource$Projects$Locations$Gateways$Setiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ApigatewaySetIamPolicyRequest;
    }
    export interface Params$Resource$Projects$Locations$Gateways$Testiampermissions extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ApigatewayTestIamPermissionsRequest;
    }
    export class Resource$Projects$Locations$Operations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, options: StreamMethodOptions): GaxiosPromise<Readable>;
        cancel(params?: Params$Resource$Projects$Locations$Operations$Cancel, options?: MethodOptions): GaxiosPromise<Schema$Empty>;
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        cancel(params: Params$Resource$Projects$Locations$Operations$Cancel, callback: BodyResponseCallback<Schema$Empty>): void;
        cancel(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Locations$Operations$Delete, options?: MethodOptions): GaxiosPromise<Schema$Empty>;
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(params: Params$Resource$Projects$Locations$Operations$Delete, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Operations$Get, options?: MethodOptions): GaxiosPromise<Schema$ApigatewayOperation>;
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Operations$Get, options: MethodOptions | BodyResponseCallback<Schema$ApigatewayOperation>, callback: BodyResponseCallback<Schema$ApigatewayOperation>): void;
        get(params: Params$Resource$Projects$Locations$Operations$Get, callback: BodyResponseCallback<Schema$ApigatewayOperation>): void;
        get(callback: BodyResponseCallback<Schema$ApigatewayOperation>): void;
        /**
         * Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Operations$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Operations$List, options?: MethodOptions): GaxiosPromise<Schema$ApigatewayListOperationsResponse>;
        list(params: Params$Resource$Projects$Locations$Operations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Operations$List, options: MethodOptions | BodyResponseCallback<Schema$ApigatewayListOperationsResponse>, callback: BodyResponseCallback<Schema$ApigatewayListOperationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Operations$List, callback: BodyResponseCallback<Schema$ApigatewayListOperationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ApigatewayListOperationsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Operations$Cancel extends StandardParameters {
        /**
         * The name of the operation resource to be cancelled.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ApigatewayCancelOperationRequest;
    }
    export interface Params$Resource$Projects$Locations$Operations$Delete extends StandardParameters {
        /**
         * The name of the operation resource to be deleted.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Operations$Get extends StandardParameters {
        /**
         * The name of the operation resource.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Operations$List extends StandardParameters {
        /**
         * The standard list filter.
         */
        filter?: string;
        /**
         * The name of the operation's parent resource.
         */
        name?: string;
        /**
         * The standard list page size.
         */
        pageSize?: number;
        /**
         * The standard list page token.
         */
        pageToken?: string;
    }
    export {};
}
