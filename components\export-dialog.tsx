"use client"

import { useState } from "react"
import type { Activity, Participant } from "../types"
import { exportStatistics, exportRawData, type ExportFormat, type ExportDataType } from "../utils/export-data"

interface ExportDialogProps {
  activities: Activity[]
  allParticipants: Participant[]
  onClose: () => void
}

export function ExportDialog({ activities, allParticipants, onClose }: ExportDialogProps) {
  const [exportFormat, setExportFormat] = useState<ExportFormat>("excel")
  const [dataType, setDataType] = useState<ExportDataType>("all")
  const [exportType, setExportType] = useState<"statistics" | "raw">("statistics")

  const handleExport = () => {
    if (exportType === "statistics") {
      exportStatistics(activities, allParticipants, exportFormat, dataType)
    } else {
      exportRawData(activities, allParticipants, exportFormat)
    }
    onClose()
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 w-full max-w-md">
        <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">導出數據</h3>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">導出類型</label>
            <div className="flex space-x-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="exportType"
                  value="statistics"
                  checked={exportType === "statistics"}
                  onChange={() => setExportType("statistics")}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">統計數據</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="exportType"
                  value="raw"
                  checked={exportType === "raw"}
                  onChange={() => setExportType("raw")}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">原始數據</span>
              </label>
            </div>
          </div>

          {exportType === "statistics" && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">數據範圍</label>
              <select
                value={dataType}
                onChange={(e) => setDataType(e.target.value as ExportDataType)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
              >
                <option value="all">全部統計數據</option>
                <option value="overall">僅整體統計</option>
                <option value="participants">僅參加者統計</option>
                <option value="activities">僅活動統計</option>
                <option value="categories">僅類別統計</option>
              </select>
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">文件格式</label>
            <div className="flex space-x-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="exportFormat"
                  value="excel"
                  checked={exportFormat === "excel"}
                  onChange={() => setExportFormat("excel")}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">Excel (.xlsx)</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="exportFormat"
                  value="csv"
                  checked={exportFormat === "csv"}
                  onChange={() => setExportFormat("csv")}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">CSV (.csv)</span>
              </label>
            </div>
            {exportFormat === "csv" && exportType === "statistics" && dataType === "all" && (
              <p className="text-xs text-yellow-600 dark:text-yellow-400 mt-1">
                注意：CSV格式將導出多個文件，每個數據類型一個文件。
              </p>
            )}
          </div>

          {/* 篩選數據提示 */}
          <div className="text-xs text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 p-2 rounded">
            注意：將只導出當前篩選條件下的數據（{activities.length} 個活動，{allParticipants.length} 位參加者）。
          </div>
        </div>

        <div className="mt-6 flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors"
          >
            取消
          </button>
          <button
            onClick={handleExport}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
          >
            導出
          </button>
        </div>
      </div>
    </div>
  )
}
