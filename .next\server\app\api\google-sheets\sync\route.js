/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/google-sheets/sync/route";
exports.ids = ["app/api/google-sheets/sync/route"];
exports.modules = {

/***/ "(rsc)/./app/api/google-sheets/sync/route.ts":
/*!*********************************************!*\
  !*** ./app/api/google-sheets/sync/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_google_sheets__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../lib/google-sheets */ \"(rsc)/./lib/google-sheets.ts\");\n/* harmony import */ var _lib_sync_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../lib/sync-service */ \"(rsc)/./lib/sync-service.ts\");\n// Google Sheets 同步 API 路由\n\n\n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { config, data, direction = 'push', accessToken } = body;\n        // 驗證配置\n        if (!config.spreadsheetId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: '缺少 Spreadsheet ID'\n            }, {\n                status: 400\n            });\n        }\n        const sheetNames = {\n            participants: \"參加者資料\" || 0,\n            activities: \"活動資料\" || 0,\n            attendance: \"出席記錄\" || 0,\n            sessions: \"屆別資料\" || 0\n        };\n        // 初始化服務\n        const googleSheetsService = new _lib_google_sheets__WEBPACK_IMPORTED_MODULE_1__.GoogleSheetsService(config, sheetNames);\n        const syncService = new _lib_sync_service__WEBPACK_IMPORTED_MODULE_2__.SyncService(googleSheetsService);\n        // 初始化連接（傳入 access token 如果有的話）\n        const initResult = await googleSheetsService.initialize(accessToken);\n        if (!initResult.success) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: initResult.error\n            }, {\n                status: 500\n            });\n        }\n        let result;\n        if (direction === 'push') {\n            // 推送數據到 Google Sheets\n            result = await syncService.performFullSync(data);\n        } else if (direction === 'pull') {\n            // 從 Google Sheets 拉取數據\n            const [participantsResult, activitiesResult, sessionsResult] = await Promise.all([\n                syncService.syncParticipantsFromSheets(),\n                syncService.syncActivitiesFromSheets(),\n                syncService.syncSessionsFromSheets()\n            ]);\n            if (participantsResult.success && activitiesResult.success && sessionsResult.success) {\n                result = {\n                    success: true,\n                    message: '數據同步成功',\n                    recordsAffected: (participantsResult.data?.length || 0) + (activitiesResult.data?.length || 0) + (sessionsResult.data?.length || 0),\n                    timestamp: new Date(),\n                    data: {\n                        participants: participantsResult.data || [],\n                        activities: activitiesResult.data || [],\n                        sessions: sessionsResult.data || []\n                    }\n                };\n            } else {\n                result = {\n                    success: false,\n                    message: '部分數據同步失敗',\n                    recordsAffected: 0,\n                    timestamp: new Date()\n                };\n            }\n        } else {\n            // 雙向同步（先拉取，再推送）\n            const pullResult = await syncService.syncParticipantsFromSheets();\n            if (pullResult.success) {\n                result = await syncService.performFullSync(data);\n            } else {\n                result = {\n                    success: false,\n                    message: '雙向同步失敗：無法從 Google Sheets 拉取數據',\n                    recordsAffected: 0,\n                    timestamp: new Date()\n                };\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(result);\n    } catch (error) {\n        console.error('Google Sheets 同步錯誤:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: error instanceof Error ? error.message : '未知錯誤',\n            recordsAffected: 0,\n            timestamp: new Date()\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        message: 'Google Sheets 同步 API'\n    }, {\n        status: 200\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/google-sheets/sync/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/google-sheets.ts":
/*!******************************!*\
  !*** ./lib/google-sheets.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GoogleSheetsService: () => (/* binding */ GoogleSheetsService)\n/* harmony export */ });\n/* harmony import */ var google_auth_library__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! google-auth-library */ \"(rsc)/./node_modules/.pnpm/google-auth-library@9.15.1/node_modules/google-auth-library/build/src/index.js\");\n/* harmony import */ var googleapis__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! googleapis */ \"(rsc)/./node_modules/.pnpm/googleapis@129.0.0/node_modules/googleapis/build/src/index.js\");\n// Google Sheets API 服務層\n\n\nclass GoogleSheetsService {\n    constructor(config, sheetNames){\n        this.sheets = null;\n        this.auth = null;\n        this.config = config;\n        this.sheetNames = sheetNames;\n    }\n    /**\n   * 初始化 Google Sheets API 連接\n   */ async initialize(accessToken) {\n        try {\n            // 使用 Service Account 認證\n            if (this.config.serviceAccountEmail && this.config.privateKey) {\n                this.auth = new google_auth_library__WEBPACK_IMPORTED_MODULE_0__.GoogleAuth({\n                    credentials: {\n                        client_email: this.config.serviceAccountEmail,\n                        private_key: this.config.privateKey.replace(/\\\\n/g, '\\n'),\n                        project_id: this.config.projectId\n                    },\n                    scopes: [\n                        'https://www.googleapis.com/auth/spreadsheets'\n                    ]\n                });\n                this.sheets = googleapis__WEBPACK_IMPORTED_MODULE_1__.google.sheets({\n                    version: 'v4',\n                    auth: this.auth\n                });\n            } else if (accessToken) {\n                // 直接使用 access token 創建 sheets 實例\n                this.sheets = googleapis__WEBPACK_IMPORTED_MODULE_1__.google.sheets({\n                    version: 'v4',\n                    auth: accessToken // 直接使用 access token\n                });\n            } else {\n                throw new Error('缺少認證配置：需要 Service Account 憑證或 OAuth2 Access Token');\n            }\n            // 測試連接\n            await this.testConnection();\n            return {\n                success: true,\n                data: true,\n                timestamp: new Date()\n            };\n        } catch (error) {\n            console.error('Google Sheets 初始化失敗:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : '未知錯誤',\n                timestamp: new Date()\n            };\n        }\n    }\n    /**\n   * 測試連接\n   */ async testConnection() {\n        if (!this.sheets) {\n            throw new Error('Google Sheets API 未初始化');\n        }\n        try {\n            await this.sheets.spreadsheets.get({\n                spreadsheetId: this.config.spreadsheetId\n            });\n        } catch (error) {\n            throw new Error(`無法連接到 Google Sheets: ${error}`);\n        }\n    }\n    /**\n   * 確保工作表存在，如果不存在則創建\n   */ async ensureSheetsExist() {\n        try {\n            if (!this.sheets) {\n                throw new Error('Google Sheets API 未初始化');\n            }\n            const spreadsheet = await this.sheets.spreadsheets.get({\n                spreadsheetId: this.config.spreadsheetId\n            });\n            const existingSheets = spreadsheet.data.sheets?.map((sheet)=>sheet.properties?.title) || [];\n            const requiredSheets = Object.values(this.sheetNames);\n            for (const sheetName of requiredSheets){\n                if (!existingSheets.includes(sheetName)) {\n                    await this.createSheet(sheetName);\n                }\n            }\n            // 初始化表頭\n            await this.initializeHeaders();\n            return {\n                success: true,\n                data: true,\n                timestamp: new Date()\n            };\n        } catch (error) {\n            console.error('確保工作表存在時發生錯誤:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : '未知錯誤',\n                timestamp: new Date()\n            };\n        }\n    }\n    /**\n   * 創建新工作表\n   */ async createSheet(title) {\n        if (!this.sheets) {\n            throw new Error('Google Sheets API 未初始化');\n        }\n        await this.sheets.spreadsheets.batchUpdate({\n            spreadsheetId: this.config.spreadsheetId,\n            requestBody: {\n                requests: [\n                    {\n                        addSheet: {\n                            properties: {\n                                title,\n                                gridProperties: {\n                                    rowCount: 1000,\n                                    columnCount: 20\n                                }\n                            }\n                        }\n                    }\n                ]\n            }\n        });\n    }\n    /**\n   * 初始化表頭\n   */ async initializeHeaders() {\n        const headers = {\n            [this.sheetNames.participants]: [\n                'ID',\n                '姓名',\n                '職銜',\n                '電子郵件',\n                '電話',\n                '加入日期',\n                '是否活躍',\n                '備註',\n                '創建時間',\n                '更新時間'\n            ],\n            [this.sheetNames.activities]: [\n                'ID',\n                '活動名稱',\n                '日期',\n                '地點',\n                '描述',\n                '委員會',\n                '類型',\n                '最大參與人數',\n                '是否活躍',\n                '創建時間',\n                '更新時間'\n            ],\n            [this.sheetNames.attendance]: [\n                'ID',\n                '參加者ID',\n                '參加者姓名',\n                '活動ID',\n                '活動名稱',\n                '出席狀態',\n                '簽到時間',\n                '備註',\n                '記錄者',\n                '創建時間',\n                '更新時間'\n            ],\n            [this.sheetNames.sessions]: [\n                'ID',\n                '屆別名稱',\n                '開始日期',\n                '結束日期',\n                '描述',\n                '是否活躍',\n                '創建時間',\n                '更新時間'\n            ]\n        };\n        for (const [sheetName, headerRow] of Object.entries(headers)){\n            await this.updateRange(sheetName, 'A1:Z1', [\n                headerRow\n            ]);\n        }\n    }\n    /**\n   * 讀取指定範圍的數據\n   */ async readRange(sheetName, range) {\n        try {\n            if (!this.sheets) {\n                throw new Error('Google Sheets API 未初始化');\n            }\n            const response = await this.sheets.spreadsheets.values.get({\n                spreadsheetId: this.config.spreadsheetId,\n                range: `${sheetName}!${range}`\n            });\n            return {\n                success: true,\n                data: response.data.values || [],\n                timestamp: new Date()\n            };\n        } catch (error) {\n            console.error('讀取數據時發生錯誤:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : '未知錯誤',\n                timestamp: new Date()\n            };\n        }\n    }\n    /**\n   * 更新指定範圍的數據\n   */ async updateRange(sheetName, range, values) {\n        try {\n            if (!this.sheets) {\n                throw new Error('Google Sheets API 未初始化');\n            }\n            await this.sheets.spreadsheets.values.update({\n                spreadsheetId: this.config.spreadsheetId,\n                range: `${sheetName}!${range}`,\n                valueInputOption: 'RAW',\n                requestBody: {\n                    values\n                }\n            });\n            return {\n                success: true,\n                data: true,\n                timestamp: new Date()\n            };\n        } catch (error) {\n            console.error('更新數據時發生錯誤:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : '未知錯誤',\n                timestamp: new Date()\n            };\n        }\n    }\n    /**\n   * 追加數據到工作表末尾\n   */ async appendData(sheetName, values) {\n        try {\n            if (!this.sheets) {\n                throw new Error('Google Sheets API 未初始化');\n            }\n            await this.sheets.spreadsheets.values.append({\n                spreadsheetId: this.config.spreadsheetId,\n                range: `${sheetName}!A:Z`,\n                valueInputOption: 'RAW',\n                requestBody: {\n                    values\n                }\n            });\n            return {\n                success: true,\n                data: true,\n                timestamp: new Date()\n            };\n        } catch (error) {\n            console.error('追加數據時發生錯誤:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : '未知錯誤',\n                timestamp: new Date()\n            };\n        }\n    }\n    /**\n   * 清空工作表數據（保留表頭）\n   */ async clearSheet(sheetName) {\n        try {\n            if (!this.sheets) {\n                throw new Error('Google Sheets API 未初始化');\n            }\n            await this.sheets.spreadsheets.values.clear({\n                spreadsheetId: this.config.spreadsheetId,\n                range: `${sheetName}!A2:Z`\n            });\n            return {\n                success: true,\n                data: true,\n                timestamp: new Date()\n            };\n        } catch (error) {\n            console.error('清空工作表時發生錯誤:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : '未知錯誤',\n                timestamp: new Date()\n            };\n        }\n    }\n    /**\n   * 批量操作\n   */ async batchOperations(operations) {\n        try {\n            let successful = 0;\n            let failed = 0;\n            const errors = [];\n            for (const operation of operations){\n                try {\n                    switch(operation.operation){\n                        case 'create':\n                            await this.appendData(operation.sheetName, operation.data);\n                            break;\n                        case 'update':\n                            break;\n                        case 'delete':\n                            break;\n                    }\n                    successful++;\n                } catch (error) {\n                    failed++;\n                    errors.push(`${operation.operation} 操作失敗: ${error}`);\n                }\n            }\n            return {\n                success: true,\n                data: {\n                    successful,\n                    failed,\n                    errors\n                },\n                timestamp: new Date()\n            };\n        } catch (error) {\n            console.error('批量操作時發生錯誤:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : '未知錯誤',\n                timestamp: new Date()\n            };\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/google-sheets.ts\n");

/***/ }),

/***/ "(rsc)/./lib/sync-service.ts":
/*!*****************************!*\
  !*** ./lib/sync-service.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SyncService: () => (/* binding */ SyncService)\n/* harmony export */ });\n// 數據同步服務\nclass SyncService {\n    constructor(googleSheetsService){\n        this.googleSheetsService = googleSheetsService;\n    }\n    /**\n   * 同步參加者數據到 Google Sheets\n   */ async syncParticipantsToSheets(participants) {\n        try {\n            const sheetRows = participants.map((participant)=>[\n                    participant.id,\n                    participant.name,\n                    participant.category || '',\n                    '',\n                    '',\n                    new Date().toISOString().split('T')[0],\n                    'true',\n                    '',\n                    new Date().toISOString(),\n                    new Date().toISOString() // updatedAt\n                ]);\n            // 清空現有數據並寫入新數據\n            await this.googleSheetsService.clearSheet('參加者資料');\n            const result = await this.googleSheetsService.appendData('參加者資料', sheetRows);\n            return {\n                success: result.success,\n                message: result.success ? `成功同步 ${participants.length} 位參加者` : result.error || '同步失敗',\n                recordsAffected: participants.length,\n                timestamp: new Date()\n            };\n        } catch (error) {\n            console.error('同步參加者數據時發生錯誤:', error);\n            return {\n                success: false,\n                message: error instanceof Error ? error.message : '未知錯誤',\n                recordsAffected: 0,\n                timestamp: new Date()\n            };\n        }\n    }\n    /**\n   * 從 Google Sheets 讀取參加者數據\n   */ async syncParticipantsFromSheets() {\n        try {\n            const result = await this.googleSheetsService.readRange('參加者資料', 'A2:J1000');\n            if (!result.success || !result.data) {\n                return {\n                    success: false,\n                    error: result.error || '無法讀取數據',\n                    timestamp: new Date()\n                };\n            }\n            const participants = result.data.filter((row)=>row.length > 0 && row[0]) // 過濾空行\n            .map((row)=>({\n                    id: row[0] || '',\n                    name: row[1] || '',\n                    category: row[2] || '',\n                    attendance: {} // 初始化為空對象\n                }));\n            return {\n                success: true,\n                data: participants,\n                timestamp: new Date()\n            };\n        } catch (error) {\n            console.error('從 Google Sheets 讀取參加者數據時發生錯誤:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : '未知錯誤',\n                timestamp: new Date()\n            };\n        }\n    }\n    /**\n   * 同步活動數據到 Google Sheets\n   */ async syncActivitiesToSheets(activities) {\n        try {\n            const sheetRows = activities.map((activity)=>[\n                    activity.id,\n                    activity.name,\n                    activity.date,\n                    activity.location || '',\n                    activity.description || '',\n                    activity.committee,\n                    activity.type,\n                    activity.maxParticipants?.toString() || '',\n                    'true',\n                    new Date().toISOString(),\n                    new Date().toISOString() // updatedAt\n                ]);\n            await this.googleSheetsService.clearSheet('活動資料');\n            const result = await this.googleSheetsService.appendData('活動資料', sheetRows);\n            return {\n                success: result.success,\n                message: result.success ? `成功同步 ${activities.length} 個活動` : result.error || '同步失敗',\n                recordsAffected: activities.length,\n                timestamp: new Date()\n            };\n        } catch (error) {\n            console.error('同步活動數據時發生錯誤:', error);\n            return {\n                success: false,\n                message: error instanceof Error ? error.message : '未知錯誤',\n                recordsAffected: 0,\n                timestamp: new Date()\n            };\n        }\n    }\n    /**\n   * 從 Google Sheets 讀取活動數據\n   */ async syncActivitiesFromSheets() {\n        try {\n            const result = await this.googleSheetsService.readRange('活動資料', 'A2:K1000');\n            if (!result.success || !result.data) {\n                return {\n                    success: false,\n                    error: result.error || '無法讀取數據',\n                    timestamp: new Date()\n                };\n            }\n            const activities = result.data.filter((row)=>row.length > 0 && row[0]).map((row)=>({\n                    id: row[0] || '',\n                    name: row[1] || '',\n                    date: row[2] || '',\n                    location: row[3] || '',\n                    description: row[4] || '',\n                    committee: row[5] || '',\n                    type: row[6] || '',\n                    maxParticipants: row[7] ? parseInt(row[7]) : undefined,\n                    participants: [] // 初始化為空數組\n                }));\n            return {\n                success: true,\n                data: activities,\n                timestamp: new Date()\n            };\n        } catch (error) {\n            console.error('從 Google Sheets 讀取活動數據時發生錯誤:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : '未知錯誤',\n                timestamp: new Date()\n            };\n        }\n    }\n    /**\n   * 同步屆別數據到 Google Sheets\n   */ async syncSessionsToSheets(sessions) {\n        try {\n            const sheetRows = sessions.map((session)=>[\n                    session.id,\n                    session.name,\n                    session.startDate,\n                    session.endDate,\n                    session.description || '',\n                    session.isActive ? 'true' : 'false',\n                    new Date().toISOString(),\n                    new Date().toISOString() // updatedAt\n                ]);\n            await this.googleSheetsService.clearSheet('屆別資料');\n            const result = await this.googleSheetsService.appendData('屆別資料', sheetRows);\n            return {\n                success: result.success,\n                message: result.success ? `成功同步 ${sessions.length} 個屆別` : result.error || '同步失敗',\n                recordsAffected: sessions.length,\n                timestamp: new Date()\n            };\n        } catch (error) {\n            console.error('同步屆別數據時發生錯誤:', error);\n            return {\n                success: false,\n                message: error instanceof Error ? error.message : '未知錯誤',\n                recordsAffected: 0,\n                timestamp: new Date()\n            };\n        }\n    }\n    /**\n   * 從 Google Sheets 讀取屆別數據\n   */ async syncSessionsFromSheets() {\n        try {\n            const result = await this.googleSheetsService.readRange('屆別資料', 'A2:H1000');\n            if (!result.success || !result.data) {\n                return {\n                    success: false,\n                    error: result.error || '無法讀取數據',\n                    timestamp: new Date()\n                };\n            }\n            const sessions = result.data.filter((row)=>row.length > 0 && row[0]).map((row)=>({\n                    id: row[0] || '',\n                    name: row[1] || '',\n                    startDate: row[2] || '',\n                    endDate: row[3] || '',\n                    description: row[4] || '',\n                    isActive: row[5] === 'true'\n                }));\n            return {\n                success: true,\n                data: sessions,\n                timestamp: new Date()\n            };\n        } catch (error) {\n            console.error('從 Google Sheets 讀取屆別數據時發生錯誤:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : '未知錯誤',\n                timestamp: new Date()\n            };\n        }\n    }\n    /**\n   * 同步出席記錄到 Google Sheets\n   */ async syncAttendanceToSheets(attendanceRecords) {\n        try {\n            const sheetRows = attendanceRecords.map((record)=>[\n                    `${record.participantId}-${record.activityId}`,\n                    record.participantId,\n                    record.participantName || '',\n                    record.activityId,\n                    record.activityName || '',\n                    record.status,\n                    record.checkInTime || '',\n                    record.notes || '',\n                    record.recordedBy || '',\n                    new Date().toISOString(),\n                    new Date().toISOString() // updatedAt\n                ]);\n            await this.googleSheetsService.clearSheet('出席記錄');\n            const result = await this.googleSheetsService.appendData('出席記錄', sheetRows);\n            return {\n                success: result.success,\n                message: result.success ? `成功同步 ${attendanceRecords.length} 條出席記錄` : result.error || '同步失敗',\n                recordsAffected: attendanceRecords.length,\n                timestamp: new Date()\n            };\n        } catch (error) {\n            console.error('同步出席記錄時發生錯誤:', error);\n            return {\n                success: false,\n                message: error instanceof Error ? error.message : '未知錯誤',\n                recordsAffected: 0,\n                timestamp: new Date()\n            };\n        }\n    }\n    /**\n   * 執行完整同步\n   */ async performFullSync(data, options = {}) {\n        try {\n            const results = [];\n            if (!options.sheets || options.sheets.includes('participants')) {\n                const participantResult = await this.syncParticipantsToSheets(data.participants);\n                results.push(participantResult);\n            }\n            if (!options.sheets || options.sheets.includes('activities')) {\n                const activityResult = await this.syncActivitiesToSheets(data.activities);\n                results.push(activityResult);\n            }\n            if (!options.sheets || options.sheets.includes('sessions')) {\n                const sessionResult = await this.syncSessionsToSheets(data.sessions);\n                results.push(sessionResult);\n            }\n            if (!options.sheets || options.sheets.includes('attendance')) {\n                const attendanceResult = await this.syncAttendanceToSheets(data.attendanceRecords);\n                results.push(attendanceResult);\n            }\n            const totalRecords = results.reduce((sum, result)=>sum + result.recordsAffected, 0);\n            const allSuccessful = results.every((result)=>result.success);\n            return {\n                success: allSuccessful,\n                message: allSuccessful ? `完整同步成功，共處理 ${totalRecords} 條記錄` : `部分同步失敗，請檢查錯誤信息`,\n                recordsAffected: totalRecords,\n                timestamp: new Date()\n            };\n        } catch (error) {\n            console.error('執行完整同步時發生錯誤:', error);\n            return {\n                success: false,\n                message: error instanceof Error ? error.message : '未知錯誤',\n                recordsAffected: 0,\n                timestamp: new Date()\n            };\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/sync-service.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgoogle-sheets%2Fsync%2Froute&page=%2Fapi%2Fgoogle-sheets%2Fsync%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgoogle-sheets%2Fsync%2Froute.ts&appDir=C%3A%5CUsers%5CPC-LPK%5CDocuments%5CHKUYA%5Cattendance%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CPC-LPK%5CDocuments%5CHKUYA%5Cattendance&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgoogle-sheets%2Fsync%2Froute&page=%2Fapi%2Fgoogle-sheets%2Fsync%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgoogle-sheets%2Fsync%2Froute.ts&appDir=C%3A%5CUsers%5CPC-LPK%5CDocuments%5CHKUYA%5Cattendance%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CPC-LPK%5CDocuments%5CHKUYA%5Cattendance&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_PC_LPK_Documents_HKUYA_attendance_app_api_google_sheets_sync_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/google-sheets/sync/route.ts */ \"(rsc)/./app/api/google-sheets/sync/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/google-sheets/sync/route\",\n        pathname: \"/api/google-sheets/sync\",\n        filename: \"route\",\n        bundlePath: \"app/api/google-sheets/sync/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\app\\\\api\\\\google-sheets\\\\sync\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_PC_LPK_Documents_HKUYA_attendance_app_api_google_sheets_sync_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgoogle-sheets%2Fsync%2Froute&page=%2Fapi%2Fgoogle-sheets%2Fsync%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgoogle-sheets%2Fsync%2Froute.ts&appDir=C%3A%5CUsers%5CPC-LPK%5CDocuments%5CHKUYA%5Cattendance%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CPC-LPK%5CDocuments%5CHKUYA%5Cattendance&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?96cf":
/*!********************************!*\
  !*** supports-color (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("http2");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1","vendor-chunks/googleapis@129.0.0","vendor-chunks/google-auth-library@9.15.1","vendor-chunks/tr46@0.0.3","vendor-chunks/bignumber.js@9.3.0","vendor-chunks/node-fetch@2.7.0","vendor-chunks/googleapis-common@7.2.0","vendor-chunks/whatwg-url@5.0.0","vendor-chunks/gaxios@6.7.1","vendor-chunks/qs@6.14.0","vendor-chunks/json-bigint@1.0.0","vendor-chunks/google-logging-utils@0.0.2","vendor-chunks/object-inspect@1.13.4","vendor-chunks/gcp-metadata@6.1.1","vendor-chunks/debug@4.4.1","vendor-chunks/get-intrinsic@1.3.0","vendor-chunks/https-proxy-agent@7.0.6","vendor-chunks/gtoken@7.1.0","vendor-chunks/uuid@9.0.1","vendor-chunks/agent-base@7.1.3","vendor-chunks/jws@4.0.0","vendor-chunks/jwa@2.0.1","vendor-chunks/url-template@2.0.8","vendor-chunks/ecdsa-sig-formatter@1.0.11","vendor-chunks/webidl-conversions@3.0.1","vendor-chunks/base64-js@1.5.1","vendor-chunks/side-channel-list@1.0.0","vendor-chunks/extend@3.0.2","vendor-chunks/ms@2.1.3","vendor-chunks/side-channel-weakmap@1.0.2","vendor-chunks/has-symbols@1.1.0","vendor-chunks/function-bind@1.1.2","vendor-chunks/side-channel-map@1.0.1","vendor-chunks/safe-buffer@5.2.1","vendor-chunks/side-channel@1.1.0","vendor-chunks/get-proto@1.0.1","vendor-chunks/call-bind-apply-helpers@1.0.2","vendor-chunks/buffer-equal-constant-time@1.0.1","vendor-chunks/dunder-proto@1.0.1","vendor-chunks/math-intrinsics@1.1.0","vendor-chunks/call-bound@1.0.4","vendor-chunks/is-stream@2.0.1","vendor-chunks/es-errors@1.3.0","vendor-chunks/gopd@1.2.0","vendor-chunks/es-define-property@1.0.1","vendor-chunks/hasown@2.0.2","vendor-chunks/es-object-atoms@1.1.1"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgoogle-sheets%2Fsync%2Froute&page=%2Fapi%2Fgoogle-sheets%2Fsync%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgoogle-sheets%2Fsync%2Froute.ts&appDir=C%3A%5CUsers%5CPC-LPK%5CDocuments%5CHKUYA%5Cattendance%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CPC-LPK%5CDocuments%5CHKUYA%5Cattendance&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();