"use client"

import { useState } from "react"
import type { ActivityLevelSettings } from "../types"
import { validateActivityLevelSettings } from "../utils/activity-level"

interface ActivityLevelSettingsProps {
  settings: ActivityLevelSettings
  onSettingsChange: (settings: ActivityLevelSettings) => void
  className?: string
}

export function ActivityLevelSettingsComponent({
  settings,
  onSettingsChange,
  className = "",
}: ActivityLevelSettingsProps) {
  const [localSettings, setLocalSettings] = useState<ActivityLevelSettings>(settings)
  const [isValid, setIsValid] = useState(true)

  const handleChange = (field: keyof ActivityLevelSettings, value: number) => {
    const newSettings = { ...localSettings, [field]: value }
    setLocalSettings(newSettings)

    const valid = validateActivityLevelSettings(newSettings)
    setIsValid(valid)

    if (valid) {
      onSettingsChange(newSettings)
    }
  }

  const resetToDefaults = () => {
    const defaultSettings = { veryActive: 80, active: 40 }
    setLocalSettings(defaultSettings)
    setIsValid(true)
    onSettingsChange(defaultSettings)
  }

  return (
    <div className={`bg-white dark:bg-gray-700 rounded-lg shadow p-4 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-md font-medium text-gray-900 dark:text-white">活躍等級設定</h3>
        <button
          onClick={resetToDefaults}
          className="px-3 py-1 text-sm bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors"
        >
          重置預設值
        </button>
      </div>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            非常活躍閾值 (出席率 ≥ {localSettings.veryActive}%)
          </label>
          <div className="flex items-center space-x-2">
            <input
              type="range"
              min="0"
              max="100"
              value={localSettings.veryActive}
              onChange={(e) => handleChange("veryActive", Number(e.target.value))}
              className="flex-1"
            />
            <input
              type="number"
              min="0"
              max="100"
              value={localSettings.veryActive}
              onChange={(e) => handleChange("veryActive", Number(e.target.value))}
              className="w-16 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700 dark:text-white"
            />
            <span className="text-sm text-gray-500 dark:text-gray-400">%</span>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            活躍閾值 ({localSettings.active}% ≤ 出席率 &lt; {localSettings.veryActive}%)
          </label>
          <div className="flex items-center space-x-2">
            <input
              type="range"
              min="0"
              max="100"
              value={localSettings.active}
              onChange={(e) => handleChange("active", Number(e.target.value))}
              className="flex-1"
            />
            <input
              type="number"
              min="0"
              max="100"
              value={localSettings.active}
              onChange={(e) => handleChange("active", Number(e.target.value))}
              className="w-16 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700 dark:text-white"
            />
            <span className="text-sm text-gray-500 dark:text-gray-400">%</span>
          </div>
        </div>

        <div className="text-sm text-gray-600 dark:text-gray-400">
          <p>不活躍：出席率 &lt; {localSettings.active}%</p>
        </div>

        {!isValid && (
          <div className="text-sm text-red-600 dark:text-red-400">⚠️ 設定無效：非常活躍閾值必須大於或等於活躍閾值</div>
        )}

        <div className="grid grid-cols-3 gap-2 text-xs">
          <div className="p-2 rounded bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 text-center">
            不活躍
            <br />
            &lt; {localSettings.active}%
          </div>
          <div className="p-2 rounded bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 text-center">
            活躍
            <br />
            {localSettings.active}% - {localSettings.veryActive - 1}%
          </div>
          <div className="p-2 rounded bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 text-center">
            非常活躍
            <br />≥ {localSettings.veryActive}%
          </div>
        </div>
      </div>
    </div>
  )
}
