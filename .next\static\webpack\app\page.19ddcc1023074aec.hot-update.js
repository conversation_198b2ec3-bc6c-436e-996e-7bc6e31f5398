"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/participant-management-page.tsx":
/*!****************************************************!*\
  !*** ./components/participant-management-page.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ParticipantManagementPage: () => (/* binding */ ParticipantManagementPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var _universal_bulk_import__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./universal-bulk-import */ \"(app-pages-browser)/./components/universal-bulk-import.tsx\");\n/* harmony import */ var _participant_attendance_history__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./participant-attendance-history */ \"(app-pages-browser)/./components/participant-attendance-history.tsx\");\n/* harmony import */ var _utils_activity_level__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils/activity-level */ \"(app-pages-browser)/./utils/activity-level.ts\");\n/* harmony import */ var _utils_statistics__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../utils/statistics */ \"(app-pages-browser)/./utils/statistics.ts\");\n/* __next_internal_client_entry_do_not_use__ ParticipantManagementPage auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction ParticipantManagementPage(param) {\n    let { allParticipants, sessionParticipants, sessions, selectedSessionId, onAddParticipant, onAddSessionParticipant, onBulkAddParticipants, onUpdateParticipant, onUpdateSessionParticipant, onDeleteParticipant, onRemoveFromSession, onBulkDeleteTitle, onBack, activities, activityLevelSettings = _utils_activity_level__WEBPACK_IMPORTED_MODULE_5__.DEFAULT_ACTIVITY_LEVEL_SETTINGS } = param;\n    _s();\n    const [newParticipant, setNewParticipant] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        category: \"\"\n    });\n    const [newParticipantTitle, setNewParticipantTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\") // 新增職銜時的臨時輸入\n    ;\n    const [isAddingParticipant, setIsAddingParticipant] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false) // 新增參加者的載入狀態\n    ;\n    const [editingParticipant, setEditingParticipant] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingSessionParticipant, setEditingSessionParticipant] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [categoryFilter, setCategoryFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [showBulkImport, setShowBulkImport] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"session\");\n    const [viewingAttendanceHistory, setViewingAttendanceHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 職銜管理相關狀態\n    const [showTitleManagement, setShowTitleManagement] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingTitle, setEditingTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [newTitle, setNewTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 添加現有參加者優化相關狀態\n    const [showAddExisting, setShowAddExisting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [addExistingSearchTerm, setAddExistingSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [addExistingCategoryFilter, setAddExistingCategoryFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedParticipants, setSelectedParticipants] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [pageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(20);\n    const [sortField, setSortField] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [sortDirection, setSortDirection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"asc\");\n    // 獲取當前屆別\n    const currentSession = sessions.find((s)=>s.id === selectedSessionId);\n    // 獲取當前屆別的參加者\n    const currentSessionParticipants = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ParticipantManagementPage.useMemo[currentSessionParticipants]\": ()=>{\n            return sessionParticipants.filter({\n                \"ParticipantManagementPage.useMemo[currentSessionParticipants]\": (sp)=>sp.sessionId === selectedSessionId\n            }[\"ParticipantManagementPage.useMemo[currentSessionParticipants]\"]);\n        }\n    }[\"ParticipantManagementPage.useMemo[currentSessionParticipants]\"], [\n        sessionParticipants,\n        selectedSessionId\n    ]);\n    // 獲取當前屆別的所有職銜\n    const currentSessionCategories = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ParticipantManagementPage.useMemo[currentSessionCategories]\": ()=>{\n            const categories = new Set(currentSessionParticipants.map({\n                \"ParticipantManagementPage.useMemo[currentSessionCategories]\": (sp)=>sp.category\n            }[\"ParticipantManagementPage.useMemo[currentSessionCategories]\"]).filter(Boolean));\n            return Array.from(categories);\n        }\n    }[\"ParticipantManagementPage.useMemo[currentSessionCategories]\"], [\n        currentSessionParticipants\n    ]);\n    // 獲取全局所有職銜\n    const allCategories = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ParticipantManagementPage.useMemo[allCategories]\": ()=>{\n            const globalCategories = new Set(allParticipants.map({\n                \"ParticipantManagementPage.useMemo[allCategories]\": (p)=>p.category\n            }[\"ParticipantManagementPage.useMemo[allCategories]\"]).filter(Boolean));\n            const sessionCategories = new Set(sessionParticipants.map({\n                \"ParticipantManagementPage.useMemo[allCategories]\": (sp)=>sp.category\n            }[\"ParticipantManagementPage.useMemo[allCategories]\"]).filter(Boolean));\n            return Array.from(new Set([\n                ...globalCategories,\n                ...sessionCategories\n            ]));\n        }\n    }[\"ParticipantManagementPage.useMemo[allCategories]\"], [\n        allParticipants,\n        sessionParticipants\n    ]);\n    // 計算統計數據\n    const statistics = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ParticipantManagementPage.useMemo[statistics]\": ()=>{\n            return (0,_utils_statistics__WEBPACK_IMPORTED_MODULE_6__.calculateStatistics)(activities, allParticipants, sessions);\n        }\n    }[\"ParticipantManagementPage.useMemo[statistics]\"], [\n        activities,\n        allParticipants,\n        sessions\n    ]);\n    const handleSort = (field)=>{\n        if (sortField === field) {\n            setSortDirection(sortDirection === \"asc\" ? \"desc\" : \"asc\");\n        } else {\n            setSortField(field);\n            setSortDirection(\"asc\");\n        }\n    };\n    // 過濾參加者\n    const filteredSessionParticipants = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ParticipantManagementPage.useMemo[filteredSessionParticipants]\": ()=>{\n            const filtered = currentSessionParticipants.filter({\n                \"ParticipantManagementPage.useMemo[filteredSessionParticipants].filtered\": (sp)=>{\n                    const matchesSearch = sp.name.toLowerCase().includes(searchTerm.toLowerCase());\n                    const matchesCategory = categoryFilter ? sp.category === categoryFilter : true;\n                    const matchesStatus = statusFilter === \"all\" || statusFilter === \"active\" && sp.isActive || statusFilter === \"inactive\" && !sp.isActive;\n                    return matchesSearch && matchesCategory && matchesStatus;\n                }\n            }[\"ParticipantManagementPage.useMemo[filteredSessionParticipants].filtered\"]);\n            // 計算每個參加者的出席率和活躍等級\n            const participantsWithActivityLevel = filtered.map({\n                \"ParticipantManagementPage.useMemo[filteredSessionParticipants].participantsWithActivityLevel\": (sp)=>{\n                    // 獲取該參加者在當前屆別的所有活動\n                    const participantActivities = activities.filter({\n                        \"ParticipantManagementPage.useMemo[filteredSessionParticipants].participantsWithActivityLevel.participantActivities\": (activity)=>activity.sessionId === selectedSessionId\n                    }[\"ParticipantManagementPage.useMemo[filteredSessionParticipants].participantsWithActivityLevel.participantActivities\"]);\n                    // 計算該參加者參與的活動數量和出席數量\n                    let totalParticipated = 0;\n                    let totalAttended = 0;\n                    participantActivities.forEach({\n                        \"ParticipantManagementPage.useMemo[filteredSessionParticipants].participantsWithActivityLevel\": (activity)=>{\n                            // 檢查該參加者是否參與了這個活動\n                            const participantInActivity = activity.participants.find({\n                                \"ParticipantManagementPage.useMemo[filteredSessionParticipants].participantsWithActivityLevel.participantInActivity\": (p)=>p.id === sp.participantId\n                            }[\"ParticipantManagementPage.useMemo[filteredSessionParticipants].participantsWithActivityLevel.participantInActivity\"]);\n                            if (participantInActivity) {\n                                totalParticipated++;\n                                // 檢查出席記錄 - 嘗試多種可能的鍵\n                                const attendanceRecord = participantInActivity.attendance;\n                                let isAttended = false;\n                                // 嘗試不同的鍵格式\n                                if (attendanceRecord[activity.id] === true) {\n                                    isAttended = true;\n                                } else if (attendanceRecord[activity.date] === true) {\n                                    isAttended = true;\n                                } else if (attendanceRecord[\"\".concat(activity.date)] === true) {\n                                    isAttended = true;\n                                }\n                                if (isAttended) {\n                                    totalAttended++;\n                                }\n                            }\n                        }\n                    }[\"ParticipantManagementPage.useMemo[filteredSessionParticipants].participantsWithActivityLevel\"]);\n                    const attendanceRate = totalParticipated > 0 ? totalAttended / totalParticipated * 100 : 0;\n                    const activityLevel = (0,_utils_activity_level__WEBPACK_IMPORTED_MODULE_5__.getActivityLevel)(attendanceRate, activityLevelSettings);\n                    return {\n                        ...sp,\n                        attendanceRate,\n                        activityLevel,\n                        totalParticipated,\n                        totalAttended\n                    };\n                }\n            }[\"ParticipantManagementPage.useMemo[filteredSessionParticipants].participantsWithActivityLevel\"]);\n            // 排序邏輯\n            if (sortField) {\n                participantsWithActivityLevel.sort({\n                    \"ParticipantManagementPage.useMemo[filteredSessionParticipants]\": (a, b)=>{\n                        let aValue = \"\";\n                        let bValue = \"\";\n                        switch(sortField){\n                            case \"name\":\n                                aValue = a.name.toLowerCase();\n                                bValue = b.name.toLowerCase();\n                                break;\n                            case \"category\":\n                                aValue = a.category.toLowerCase();\n                                bValue = b.category.toLowerCase();\n                                break;\n                            case \"joinDate\":\n                                aValue = a.joinDate || \"\";\n                                bValue = b.joinDate || \"\";\n                                break;\n                            case \"status\":\n                                aValue = a.isActive ? \"active\" : \"inactive\";\n                                bValue = b.isActive ? \"active\" : \"inactive\";\n                                break;\n                            case \"attendanceRate\":\n                                aValue = a.attendanceRate;\n                                bValue = b.attendanceRate;\n                                break;\n                            default:\n                                return 0;\n                        }\n                        if (aValue < bValue) return sortDirection === \"asc\" ? -1 : 1;\n                        if (aValue > bValue) return sortDirection === \"asc\" ? 1 : -1;\n                        return 0;\n                    }\n                }[\"ParticipantManagementPage.useMemo[filteredSessionParticipants]\"]);\n            }\n            return participantsWithActivityLevel;\n        }\n    }[\"ParticipantManagementPage.useMemo[filteredSessionParticipants]\"], [\n        currentSessionParticipants,\n        searchTerm,\n        categoryFilter,\n        statusFilter,\n        sortField,\n        sortDirection,\n        activities,\n        selectedSessionId,\n        activityLevelSettings\n    ]);\n    // 獲取不在當前屆別的全局參加者（優化版）\n    const availableGlobalParticipants = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ParticipantManagementPage.useMemo[availableGlobalParticipants]\": ()=>{\n            const sessionParticipantIds = new Set(currentSessionParticipants.map({\n                \"ParticipantManagementPage.useMemo[availableGlobalParticipants]\": (sp)=>sp.participantId\n            }[\"ParticipantManagementPage.useMemo[availableGlobalParticipants]\"]));\n            return allParticipants.filter({\n                \"ParticipantManagementPage.useMemo[availableGlobalParticipants]\": (p)=>!sessionParticipantIds.has(p.id)\n            }[\"ParticipantManagementPage.useMemo[availableGlobalParticipants]\"]);\n        }\n    }[\"ParticipantManagementPage.useMemo[availableGlobalParticipants]\"], [\n        allParticipants,\n        currentSessionParticipants\n    ]);\n    // 過濾和分頁的可用參加者\n    const filteredAvailableParticipants = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ParticipantManagementPage.useMemo[filteredAvailableParticipants]\": ()=>{\n            const filtered = availableGlobalParticipants.filter({\n                \"ParticipantManagementPage.useMemo[filteredAvailableParticipants].filtered\": (participant)=>{\n                    const matchesSearch = participant.name.toLowerCase().includes(addExistingSearchTerm.toLowerCase());\n                    const matchesCategory = addExistingCategoryFilter ? participant.category === addExistingCategoryFilter : true;\n                    return matchesSearch && matchesCategory;\n                }\n            }[\"ParticipantManagementPage.useMemo[filteredAvailableParticipants].filtered\"]);\n            const startIndex = (currentPage - 1) * pageSize;\n            const endIndex = startIndex + pageSize;\n            return {\n                participants: filtered.slice(startIndex, endIndex),\n                total: filtered.length,\n                totalPages: Math.ceil(filtered.length / pageSize)\n            };\n        }\n    }[\"ParticipantManagementPage.useMemo[filteredAvailableParticipants]\"], [\n        availableGlobalParticipants,\n        addExistingSearchTerm,\n        addExistingCategoryFilter,\n        currentPage,\n        pageSize\n    ]);\n    // 處理新增參加者\n    const handleAddParticipant = async ()=>{\n        if (newParticipant.name.trim() && !isAddingParticipant) {\n            setIsAddingParticipant(true);\n            try {\n                const trimmedName = newParticipant.name.trim();\n                // 檢查是否已存在相同姓名的參加者\n                const existingParticipant = allParticipants.find((p)=>p.name.toLowerCase() === trimmedName.toLowerCase());\n                if (existingParticipant) {\n                    alert(\"參加者「\".concat(trimmedName, \"」已存在，請使用不同的姓名\"));\n                    return;\n                }\n                // 處理新增職銜的情況\n                let finalCategory = newParticipant.category;\n                if (finalCategory === \"新增職銜\") {\n                    finalCategory = newParticipantTitle.trim() // 使用新輸入的職銜\n                    ;\n                }\n                const participantToAdd = {\n                    name: trimmedName,\n                    category: finalCategory\n                };\n                console.log('Adding participant:', participantToAdd) // 調試日誌\n                ;\n                onAddParticipant(participantToAdd);\n                setNewParticipant({\n                    name: \"\",\n                    category: \"\"\n                });\n                setNewParticipantTitle(\"\") // 清空新職銜輸入\n                ;\n                // 顯示成功提示\n                alert(\"成功新增參加者「\".concat(participantToAdd.name, \"」\").concat(finalCategory ? \"，職銜：\".concat(finalCategory) : ''));\n            } catch (error) {\n                console.error('新增參加者時發生錯誤:', error);\n                alert(\"新增參加者時發生錯誤: \".concat(error instanceof Error ? error.message : '未知錯誤'));\n            } finally{\n                setIsAddingParticipant(false);\n            }\n        }\n    };\n    // 處理更新參加者\n    const handleUpdateParticipant = ()=>{\n        if (editingParticipant) {\n            onUpdateParticipant(editingParticipant);\n            setEditingParticipant(null);\n        }\n    };\n    // 處理更新屆別參加者\n    const handleUpdateSessionParticipant = ()=>{\n        if (editingSessionParticipant) {\n            onUpdateSessionParticipant(editingSessionParticipant);\n            setEditingSessionParticipant(null);\n        }\n    };\n    // 處理批量導入\n    const handleBulkImport = (participants)=>{\n        onBulkAddParticipants(participants);\n        setShowBulkImport(false);\n    };\n    // 處理添加現有參加者到當前屆別\n    const handleAddToSession = (participant, category)=>{\n        if (selectedSessionId) {\n            onAddSessionParticipant({\n                participantId: participant.id,\n                sessionId: selectedSessionId,\n                name: participant.name,\n                category: category,\n                joinDate: new Date().toISOString().split(\"T\")[0],\n                isActive: true\n            });\n        }\n    };\n    // 批量添加選中的參加者\n    const handleBatchAddToSession = ()=>{\n        if (selectedSessionId && selectedParticipants.size > 0) {\n            selectedParticipants.forEach((participantId)=>{\n                const participant = allParticipants.find((p)=>p.id === participantId);\n                if (participant) {\n                    onAddSessionParticipant({\n                        participantId: participant.id,\n                        sessionId: selectedSessionId,\n                        name: participant.name,\n                        category: participant.category || \"\",\n                        joinDate: new Date().toISOString().split(\"T\")[0],\n                        isActive: true\n                    });\n                }\n            });\n            setSelectedParticipants(new Set());\n            setShowAddExisting(false);\n        }\n    };\n    // 處理職銜編輯\n    const handleUpdateTitle = (oldTitle, newTitle)=>{\n        if (newTitle.trim() && newTitle !== oldTitle) {\n            try {\n                // 檢查新職銜是否已存在（不區分大小寫）\n                if (allCategories.some((category)=>category.toLowerCase() === newTitle.trim().toLowerCase() && category.toLowerCase() !== oldTitle.toLowerCase())) {\n                    alert(\"職銜「\".concat(newTitle.trim(), \"」已存在\"));\n                    setEditingTitle(\"\");\n                    return;\n                }\n                // 找出所有使用該職銜的參加者\n                const participantsToUpdate = allParticipants.filter((p)=>p.category === oldTitle && !p.name.startsWith('職銜佔位符-') // 排除佔位符參加者\n                );\n                // 找出佔位符參加者（如果有）\n                const placeholders = allParticipants.filter((p)=>p.category === oldTitle && p.name.startsWith('職銜佔位符-'));\n                // 找出所有使用該職銜的屆別參加者\n                const sessionParticipantsToUpdate = sessionParticipants.filter((sp)=>sp.category === oldTitle);\n                // 顯示進度提示\n                const totalUpdates = participantsToUpdate.length;\n                const hasPlaceholders = placeholders.length > 0;\n                // 確認是否繼續\n                if (totalUpdates > 0 || hasPlaceholders || sessionParticipantsToUpdate.length > 0) {\n                    if (!window.confirm(\"將更新職銜從「\".concat(oldTitle, \"」到「\").concat(newTitle, \"」，\").concat(totalUpdates > 0 ? \"影響 \".concat(totalUpdates, \" 位參加者\") : '', \"。\\n\\n確定繼續嗎？\"))) {\n                        setEditingTitle(\"\");\n                        return;\n                    }\n                    // 批量更新全局參加者\n                    if (participantsToUpdate.length > 0) {\n                        const batchUpdatePromises = participantsToUpdate.map((participant)=>{\n                            const updatedParticipant = {\n                                ...participant,\n                                category: newTitle\n                            };\n                            return new Promise((resolve)=>{\n                                onUpdateParticipant(updatedParticipant);\n                                resolve(null);\n                            });\n                        });\n                        Promise.all(batchUpdatePromises).catch((err)=>{\n                            console.error('批量更新參加者時發生錯誤:', err);\n                        });\n                    }\n                    // 批量更新佔位符參加者\n                    if (placeholders.length > 0) {\n                        const batchUpdatePlaceholderPromises = placeholders.map((placeholder)=>{\n                            const updatedPlaceholder = {\n                                ...placeholder,\n                                name: \"職銜佔位符-\".concat(newTitle),\n                                category: newTitle\n                            };\n                            return new Promise((resolve)=>{\n                                onUpdateParticipant(updatedPlaceholder);\n                                resolve(null);\n                            });\n                        });\n                        Promise.all(batchUpdatePlaceholderPromises).catch((err)=>{\n                            console.error('批量更新佔位符時發生錯誤:', err);\n                        });\n                    }\n                    // 批量更新屆別參加者\n                    if (sessionParticipantsToUpdate.length > 0) {\n                        const batchUpdateSessionPromises = sessionParticipantsToUpdate.map((sessionParticipant)=>{\n                            if ('category' in sessionParticipant) {\n                                const updatedSessionParticipant = {\n                                    ...sessionParticipant,\n                                    category: newTitle\n                                };\n                                return new Promise((resolve)=>{\n                                    onUpdateSessionParticipant(updatedSessionParticipant);\n                                    resolve(null);\n                                });\n                            }\n                            return Promise.resolve(null);\n                        });\n                        Promise.all(batchUpdateSessionPromises).catch((err)=>{\n                            console.error('批量更新屆別參加者時發生錯誤:', err);\n                        });\n                    }\n                    // 顯示完成提示\n                    if (totalUpdates > 0) {\n                        alert(\"成功更新 \".concat(totalUpdates, \" 位參加者的職銜從「\").concat(oldTitle, \"」到「\").concat(newTitle, \"」\"));\n                    } else if (hasPlaceholders) {\n                        alert(\"成功更新職銜「\".concat(oldTitle, \"」為「\").concat(newTitle, \"」\"));\n                    } else {\n                        alert(\"成功更新職銜「\".concat(oldTitle, \"」為「\").concat(newTitle, \"」，沒有參加者使用此職銜\"));\n                    }\n                } else {\n                    alert(\"沒有找到使用「\".concat(oldTitle, \"」職銜的參加者\"));\n                }\n            } catch (error) {\n                console.error('更新職銜時發生錯誤:', error);\n                alert(\"更新職銜時發生錯誤: \".concat(error instanceof Error ? error.message : '未知錯誤'));\n            }\n        }\n        setEditingTitle(\"\");\n    };\n    // 處理職銜刪除\n    const handleDeleteTitle = (title)=>{\n        try {\n            // 找出所有使用該職銜的實際參加者（排除佔位符）\n            const participantsToUpdate = allParticipants.filter((p)=>p.category === title && !p.name.startsWith('職銜佔位符-'));\n            // 找出使用該職銜的佔位符參加者\n            const placeholders = allParticipants.filter((p)=>p.category === title && p.name.startsWith('職銜佔位符-'));\n            // 找出所有使用該職銜的屆別參加者\n            const sessionParticipantsToUpdate = sessionParticipants.filter((sp)=>sp.category === title);\n            const totalUpdates = participantsToUpdate.length;\n            const hasPlaceholders = placeholders.length > 0;\n            let confirmMessage = '確定要刪除職銜 \"'.concat(title, '\" 嗎？');\n            if (totalUpdates > 0) {\n                confirmMessage += \"\\n\\n這將會：\";\n                confirmMessage += \"\\n• 清除 \".concat(totalUpdates, \" 位成員的職銜\");\n                confirmMessage += \"\\n• 將他們的職銜設為空白\";\n            }\n            if (hasPlaceholders) {\n                confirmMessage += totalUpdates > 0 ? \"\\n• 移除職銜佔位符\" : \"\\n\\n這將移除職銜佔位符。\";\n            }\n            if (totalUpdates === 0 && !hasPlaceholders) {\n                confirmMessage += \"\\n\\n沒有成員使用此職銜，將直接移除。\";\n            }\n            if (window.confirm(confirmMessage)) {\n                try {\n                    // 使用新的批量刪除函數\n                    const result = onBulkDeleteTitle(title);\n                    // 顯示完成提示\n                    let successMessage = '成功刪除職銜 \"'.concat(title, '\"');\n                    if (result.participantsUpdated > 0) {\n                        successMessage += \"\\n\\n已完成：\";\n                        successMessage += \"\\n• 清除了 \".concat(result.participantsUpdated, \" 位成員的職銜\");\n                        successMessage += \"\\n• 這些成員的職銜現在為空白\";\n                    }\n                    if (result.placeholdersRemoved > 0) {\n                        successMessage += result.participantsUpdated > 0 ? \"\\n• 移除了職銜佔位符\" : \"\\n\\n已移除職銜佔位符。\";\n                    }\n                    if (result.participantsUpdated === 0 && result.placeholdersRemoved === 0) {\n                        successMessage += \"\\n\\n沒有成員使用此職銜，已直接移除。\";\n                    }\n                    alert(successMessage);\n                } catch (error) {\n                    console.error('刪除職銜時發生錯誤:', error);\n                    alert(\"刪除職銜時發生錯誤: \".concat(error instanceof Error ? error.message : '未知錯誤'));\n                }\n            }\n        } catch (error) {\n            console.error('刪除職銜時發生錯誤:', error);\n            alert(\"刪除職銜時發生錯誤: \".concat(error instanceof Error ? error.message : '未知錯誤'));\n        }\n    };\n    // 獲取參加者的歷史屆別信息\n    const getParticipantHistory = (participantId)=>{\n        return sessionParticipants.filter((sp)=>sp.participantId === participantId).map((sp)=>{\n            var _sessions_find;\n            return {\n                ...sp,\n                sessionName: ((_sessions_find = sessions.find((s)=>s.id === sp.sessionId)) === null || _sessions_find === void 0 ? void 0 : _sessions_find.name) || \"未知屆別\"\n            };\n        }).sort((a, b)=>b.sessionId.localeCompare(a.sessionId));\n    };\n    const sortedAllParticipants = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ParticipantManagementPage.useMemo[sortedAllParticipants]\": ()=>{\n            if (!sortField) return allParticipants;\n            return [\n                ...allParticipants\n            ].sort({\n                \"ParticipantManagementPage.useMemo[sortedAllParticipants]\": (a, b)=>{\n                    let aValue = \"\";\n                    let bValue = \"\";\n                    switch(sortField){\n                        case \"name\":\n                            aValue = a.name.toLowerCase();\n                            bValue = b.name.toLowerCase();\n                            break;\n                        default:\n                            return 0;\n                    }\n                    if (aValue < bValue) return sortDirection === \"asc\" ? -1 : 1;\n                    if (aValue > bValue) return sortDirection === \"asc\" ? 1 : -1;\n                    return 0;\n                }\n            }[\"ParticipantManagementPage.useMemo[sortedAllParticipants]\"]);\n        }\n    }[\"ParticipantManagementPage.useMemo[sortedAllParticipants]\"], [\n        allParticipants,\n        sortField,\n        sortDirection\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onBack,\n                        className: \"px-3 py-1 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors text-sm\",\n                        children: \"← 返回\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 571,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-gray-900 dark:text-white\",\n                        children: \"參加者管理\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 577,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowTitleManagement(true),\n                                className: \"px-3 py-1 bg-purple-500 text-white rounded-md hover:bg-purple-600 transition-colors text-sm flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        className: \"h-4 w-4 mr-1\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        stroke: \"currentColor\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 590,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 596,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 583,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"職銜管理\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 579,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowBulkImport(true),\n                                className: \"px-3 py-1 bg-indigo-500 text-white rounded-md hover:bg-indigo-600 transition-colors text-sm flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        className: \"h-4 w-4 mr-1\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        stroke: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                            lineNumber: 611,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 604,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"批量導入\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 600,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 578,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                lineNumber: 570,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm text-blue-800 dark:text-blue-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: \"當前活躍設定：\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                            lineNumber: 626,\n                            columnNumber: 11\n                        }, this),\n                        \"非常活躍 ≥ \",\n                        activityLevelSettings.veryActive,\n                        \"%， 活躍 \",\n                        activityLevelSettings.active,\n                        \"%-\",\n                        activityLevelSettings.veryActive - 1,\n                        \"%， 不活躍 < \",\n                        activityLevelSettings.active,\n                        \"%\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                    lineNumber: 625,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                lineNumber: 624,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-700 rounded-lg shadow p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-md font-medium text-gray-900 dark:text-white mb-4\",\n                        children: \"參與度統計\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 634,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-medium text-blue-700 dark:text-blue-300\",\n                                        children: \"整體平均參與度\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 637,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-blue-600 dark:text-blue-400\",\n                                        children: [\n                                            statistics.participationStats.averageParticipationRate.toFixed(1),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 638,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-blue-600 dark:text-blue-400 mt-1\",\n                                        children: \"成員參與活動的平均比例\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 641,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 636,\n                                columnNumber: 11\n                            }, this),\n                            selectedSessionId && statistics.participationStats.sessionParticipationStats[selectedSessionId] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 dark:bg-green-900/20 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-medium text-green-700 dark:text-green-300\",\n                                        children: \"當前屆別參與度\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 648,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-green-600 dark:text-green-400\",\n                                        children: [\n                                            statistics.participationStats.sessionParticipationStats[selectedSessionId].averageParticipationRate.toFixed(1),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 649,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-green-600 dark:text-green-400 mt-1\",\n                                        children: statistics.participationStats.sessionParticipationStats[selectedSessionId].sessionName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 652,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 647,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-medium text-purple-700 dark:text-purple-300\",\n                                        children: \"活躍成員數\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 659,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-purple-600 dark:text-purple-400\",\n                                        children: selectedSessionId && statistics.participationStats.sessionParticipationStats[selectedSessionId] ? statistics.participationStats.sessionParticipationStats[selectedSessionId].activeParticipants : statistics.participantStats.filter((p)=>p.participationRate > 0).length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 660,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-purple-600 dark:text-purple-400 mt-1\",\n                                        children: \"參與過活動的成員\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 665,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 658,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-medium text-orange-700 dark:text-orange-300\",\n                                        children: \"高參與度成員\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 671,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-orange-600 dark:text-orange-400\",\n                                        children: statistics.participationStats.highParticipationStats.count\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 672,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-orange-600 dark:text-orange-400 mt-1\",\n                                        children: [\n                                            \"≥\",\n                                            statistics.participationStats.highParticipationStats.threshold,\n                                            \"% (\",\n                                            statistics.participationStats.highParticipationStats.percentage.toFixed(1),\n                                            \"%)\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 675,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 670,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 635,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                lineNumber: 633,\n                columnNumber: 7\n            }, this),\n            showTitleManagement && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 dark:text-white\",\n                                        children: \"職銜管理\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 688,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowTitleManagement(false),\n                                        className: \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M6 18L18 6M6 6l12 12\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 694,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                            lineNumber: 693,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 689,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 687,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                        children: \"新增職銜\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 701,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: newTitle,\n                                                onChange: (e)=>setNewTitle(e.target.value),\n                                                placeholder: \"輸入新職銜名稱\",\n                                                className: \"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 703,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    if (newTitle.trim()) {\n                                                        try {\n                                                            // 檢查職銜是否已存在（不區分大小寫）\n                                                            if (allCategories.some((category)=>category.toLowerCase() === newTitle.trim().toLowerCase())) {\n                                                                alert(\"職銜「\".concat(newTitle.trim(), \"」已存在\"));\n                                                                return;\n                                                            }\n                                                            // 新增職銜 - 由於職銜是從參加者資料中提取的，\n                                                            // 我們創建一個使用這個職銜的佔位符參加者\n                                                            const newParticipantWithTitle = {\n                                                                id: \"temp-\".concat(Date.now()),\n                                                                name: \"職銜佔位符-\".concat(newTitle.trim()),\n                                                                category: newTitle.trim(),\n                                                                isActive: true\n                                                            };\n                                                            // 添加到全局參加者列表\n                                                            onAddParticipant(newParticipantWithTitle);\n                                                            alert(\"成功新增職銜「\".concat(newTitle.trim(), \"」\"));\n                                                            setNewTitle(\"\");\n                                                        } catch (error) {\n                                                            console.error('新增職銜時發生錯誤:', error);\n                                                            alert(\"新增職銜時發生錯誤: \".concat(error instanceof Error ? error.message : '未知錯誤'));\n                                                        }\n                                                    }\n                                                },\n                                                className: \"px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors\",\n                                                disabled: !newTitle.trim(),\n                                                children: \"新增\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 710,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 702,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 700,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-md font-medium text-gray-900 dark:text-white mb-3\",\n                                        children: \"現有職銜\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 752,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            allCategories.map((title)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-md\",\n                                                    children: editingTitle === title ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 flex space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                defaultValue: title,\n                                                                className: \"flex-1 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-600 dark:text-white\",\n                                                                onKeyDown: (e)=>{\n                                                                    if (e.key === \"Enter\") {\n                                                                        handleUpdateTitle(title, e.currentTarget.value);\n                                                                    } else if (e.key === \"Escape\") {\n                                                                        setEditingTitle(\"\");\n                                                                    }\n                                                                },\n                                                                autoFocus: true\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 761,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>setEditingTitle(\"\"),\n                                                                className: \"px-2 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600\",\n                                                                children: \"取消\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 774,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 760,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-900 dark:text-white\",\n                                                                children: title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 783,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>setEditingTitle(title),\n                                                                        className: \"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm\",\n                                                                        children: \"編輯\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 785,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleDeleteTitle(title),\n                                                                        className: \"text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 text-sm\",\n                                                                        children: \"刪除\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 791,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 784,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true)\n                                                }, title, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 755,\n                                                    columnNumber: 21\n                                                }, this)),\n                                            allCategories.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-500 dark:text-gray-400 text-center py-4\",\n                                                children: \"暫無職銜\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 803,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 753,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 751,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 686,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                    lineNumber: 685,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                lineNumber: 684,\n                columnNumber: 9\n            }, this),\n            showBulkImport && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_universal_bulk_import__WEBPACK_IMPORTED_MODULE_3__.UniversalBulkImport, {\n                        dataType: \"participants\",\n                        sessions: sessions,\n                        selectedSessionId: selectedSessionId,\n                        onImport: handleBulkImport,\n                        onCancel: ()=>setShowBulkImport(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 816,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                    lineNumber: 815,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                lineNumber: 814,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-700 rounded-lg shadow p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-md font-medium text-gray-900 dark:text-white\",\n                                    children: \"當前屆別\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                    lineNumber: 831,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full text-sm font-medium\",\n                                    children: (currentSession === null || currentSession === void 0 ? void 0 : currentSession.name) || \"未選擇屆別\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                    lineNumber: 832,\n                                    columnNumber: 13\n                                }, this),\n                                currentSession && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                    children: [\n                                        currentSession.startDate,\n                                        \" - \",\n                                        currentSession.endDate\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                    lineNumber: 836,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                            lineNumber: 830,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                            children: [\n                                \"屆別參加者: \",\n                                currentSessionParticipants.length,\n                                \" 人\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                    lineNumber: 843,\n                                    columnNumber: 13\n                                }, this),\n                                \"所有成員: \",\n                                allParticipants.length,\n                                \" 人\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                            lineNumber: 841,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                    lineNumber: 829,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                lineNumber: 828,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-700 rounded-lg shadow p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setViewMode(\"session\"),\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-4 py-2 rounded-md transition-colors\", viewMode === \"session\" ? \"bg-blue-500 text-white\" : \"bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-500\"),\n                                    children: \"屆別參加者\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                    lineNumber: 853,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setViewMode(\"global\"),\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-4 py-2 rounded-md transition-colors\", viewMode === \"global\" ? \"bg-blue-500 text-white\" : \"bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-500\"),\n                                    children: \"所有成員\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                    lineNumber: 864,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                            lineNumber: 852,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                            children: viewMode === \"session\" ? \"顯示 \".concat(filteredSessionParticipants.length, \" / \").concat(currentSessionParticipants.length, \" 位屆別參加者\") : \"顯示 \".concat(allParticipants.length, \" 位所有成員\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                            lineNumber: 876,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                    lineNumber: 851,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                lineNumber: 850,\n                columnNumber: 7\n            }, this),\n            viewMode === \"session\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                        children: \"搜索參加者\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 889,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        placeholder: \"輸入參加者姓名\",\n                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 890,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 888,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                        children: \"職銜過濾\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 899,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: categoryFilter,\n                                        onChange: (e)=>setCategoryFilter(e.target.value),\n                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"全部職銜\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 905,\n                                                columnNumber: 17\n                                            }, this),\n                                            currentSessionCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: category,\n                                                    children: category\n                                                }, category, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 907,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 900,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 898,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                        children: \"狀態過濾\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 914,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: statusFilter,\n                                        onChange: (e)=>setStatusFilter(e.target.value),\n                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"all\",\n                                                children: \"全部狀態\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 920,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"active\",\n                                                children: \"活躍\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 921,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"inactive\",\n                                                children: \"非活躍\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 922,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 915,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 913,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 887,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-700 rounded-lg shadow overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-6 py-4 border-b border-gray-200 dark:border-gray-600\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 dark:text-white\",\n                                    children: [\n                                        currentSession === null || currentSession === void 0 ? void 0 : currentSession.name,\n                                        \" 參加者列表 (\",\n                                        filteredSessionParticipants.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                    lineNumber: 930,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 929,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"min-w-full divide-y divide-gray-200 dark:divide-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            className: \"bg-gray-50 dark:bg-gray-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleSort(\"name\"),\n                                                            className: \"flex items-center space-x-1 hover:bg-gray-100 dark:hover:bg-gray-700 px-2 py-1 rounded transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"姓名\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 943,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: sortField === \"name\" ? sortDirection === \"asc\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M5 15l7-7 7 7\",\n                                                                        className: \"text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 947,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M19 9l-7 7-7-7\",\n                                                                        className: \"text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 955,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M8 9l4-4 4 4m0 6l-4 4-4-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 964,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 944,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 939,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 938,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleSort(\"category\"),\n                                                            className: \"flex items-center space-x-1 hover:bg-gray-100 dark:hover:bg-gray-700 px-2 py-1 rounded transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"職銜\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 979,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: sortField === \"category\" ? sortDirection === \"asc\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M5 15l7-7 7 7\",\n                                                                        className: \"text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 983,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M19 9l-7 7-7-7\",\n                                                                        className: \"text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 991,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M8 9l4-4 4 4m0 6l-4 4-4-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1000,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 980,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 975,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 974,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleSort(\"joinDate\"),\n                                                            className: \"flex items-center space-x-1 hover:bg-gray-100 dark:hover:bg-gray-700 px-2 py-1 rounded transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"加入日期\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1015,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: sortField === \"joinDate\" ? sortDirection === \"asc\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M5 15l7-7 7 7\",\n                                                                        className: \"text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1019,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M19 9l-7 7-7-7\",\n                                                                        className: \"text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1027,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M8 9l4-4 4 4m0 6l-4 4-4-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1036,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1016,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1011,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1010,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleSort(\"attendanceRate\"),\n                                                            className: \"flex items-center space-x-1 hover:bg-gray-100 dark:hover:bg-gray-700 px-2 py-1 rounded transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"活躍狀態\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1051,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: sortField === \"attendanceRate\" ? sortDirection === \"asc\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M5 15l7-7 7 7\",\n                                                                        className: \"text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1055,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M19 9l-7 7-7-7\",\n                                                                        className: \"text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1063,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M8 9l4-4 4 4m0 6l-4 4-4-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1072,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1052,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1047,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1046,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                                                        children: \"參與度詳情\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1082,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                                                        children: \"歷史屆別\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1085,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                                                        children: \"操作\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1088,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 937,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                            lineNumber: 936,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            className: \"bg-white dark:bg-gray-700 divide-y divide-gray-200 dark:divide-gray-600\",\n                                            children: filteredSessionParticipants.length > 0 ? filteredSessionParticipants.map((sessionParticipant)=>{\n                                                var _sessionParticipant_attendanceRate;\n                                                const history = getParticipantHistory(sessionParticipant.participantId);\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"hover:bg-gray-50 dark:hover:bg-gray-650\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\",\n                                                            children: sessionParticipant.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1099,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded\",\n                                                                children: sessionParticipant.category || \"無職銜\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 1103,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1102,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\",\n                                                            children: sessionParticipant.joinDate || \"-\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1107,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-sm\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-col space-y-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-2 py-1 text-xs rounded font-medium\", (0,_utils_activity_level__WEBPACK_IMPORTED_MODULE_5__.getActivityLevelColor)(sessionParticipant.activityLevel)),\n                                                                        children: (0,_utils_activity_level__WEBPACK_IMPORTED_MODULE_5__.getActivityLevelText)(sessionParticipant.activityLevel)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1112,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: [\n                                                                            \"出席率: \",\n                                                                            ((_sessionParticipant_attendanceRate = sessionParticipant.attendanceRate) === null || _sessionParticipant_attendanceRate === void 0 ? void 0 : _sessionParticipant_attendanceRate.toFixed(1)) || 0,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1120,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: [\n                                                                            \"(\",\n                                                                            sessionParticipant.totalAttended || 0,\n                                                                            \"/\",\n                                                                            sessionParticipant.totalParticipated || 0,\n                                                                            \")\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1123,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 1111,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1110,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 text-sm text-gray-900 dark:text-white\",\n                                                            children: (()=>{\n                                                                const participantStat = statistics.participantStats.find((p)=>p.id === sessionParticipant.participantId);\n                                                                if (!participantStat) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                    children: \"無資料\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1131,\n                                                                    columnNumber: 60\n                                                                }, this);\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-col space-y-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs font-medium text-gray-700 dark:text-gray-300\",\n                                                                                    children: [\n                                                                                        \"總體: \",\n                                                                                        participantStat.participationDetails.participationRatio\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                                    lineNumber: 1136,\n                                                                                    columnNumber: 37\n                                                                                }, this),\n                                                                                participantStat.isHighParticipation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"px-1 py-0.5 bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 text-xs rounded\",\n                                                                                    children: \"高參與\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                                    lineNumber: 1140,\n                                                                                    columnNumber: 39\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                            lineNumber: 1135,\n                                                                            columnNumber: 35\n                                                                        }, this),\n                                                                        selectedSessionId && participantStat.sessionParticipationDetails[selectedSessionId] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-blue-600 dark:text-blue-400\",\n                                                                            children: [\n                                                                                \"本屆: \",\n                                                                                participantStat.sessionParticipationDetails[selectedSessionId].participationRatio\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                            lineNumber: 1146,\n                                                                            columnNumber: 37\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                            children: [\n                                                                                \"參與率: \",\n                                                                                participantStat.participationRate.toFixed(1),\n                                                                                \"%\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                            lineNumber: 1150,\n                                                                            columnNumber: 35\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1134,\n                                                                    columnNumber: 33\n                                                                }, this);\n                                                            })()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1128,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 text-sm text-gray-900 dark:text-white\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-wrap gap-1\",\n                                                                children: [\n                                                                    history.slice(0, 3).map((h)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-1 py-0.5 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 text-xs rounded\",\n                                                                            title: \"\".concat(h.sessionName, \": \").concat(h.category),\n                                                                            children: h.sessionName\n                                                                        }, h.id, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                            lineNumber: 1160,\n                                                                            columnNumber: 33\n                                                                        }, this)),\n                                                                    history.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: [\n                                                                            \"+\",\n                                                                            history.length - 3\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1169,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 1158,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1157,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>{\n                                                                        const globalParticipant = allParticipants.find((p)=>p.id === sessionParticipant.participantId);\n                                                                        if (globalParticipant) {\n                                                                            setViewingAttendanceHistory(globalParticipant);\n                                                                        }\n                                                                    },\n                                                                    className: \"text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 mr-4\",\n                                                                    children: \"出席記錄\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1174,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>setEditingSessionParticipant(sessionParticipant),\n                                                                    className: \"text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 mr-4\",\n                                                                    children: \"編輯\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1187,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>{\n                                                                        if (window.confirm('確定要將 \"'.concat(sessionParticipant.name, '\" 從當前屆別中移除嗎？'))) {\n                                                                            onRemoveFromSession(sessionParticipant.id);\n                                                                        }\n                                                                    },\n                                                                    className: \"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300\",\n                                                                    children: \"移除\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1193,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1173,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, sessionParticipant.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 1098,\n                                                    columnNumber: 25\n                                                }, this);\n                                            }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    colSpan: 6,\n                                                    className: \"px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400\",\n                                                    children: \"沒有找到符合條件的參加者\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 1209,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1208,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                            lineNumber: 1093,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                    lineNumber: 935,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 934,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 928,\n                        columnNumber: 11\n                    }, this),\n                    availableGlobalParticipants.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-700 rounded-lg shadow p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 dark:text-white\",\n                                        children: [\n                                            \"添加現有參加者到 \",\n                                            currentSession === null || currentSession === void 0 ? void 0 : currentSession.name\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1223,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowAddExisting(!showAddExisting),\n                                        className: \"px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                className: \"h-4 w-4 mr-2\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M12 4v16m8-8H4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 1237,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1230,\n                                                columnNumber: 19\n                                            }, this),\n                                            showAddExisting ? \"收起\" : \"添加成員 (\".concat(availableGlobalParticipants.length, \")\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1226,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1222,\n                                columnNumber: 15\n                            }, this),\n                            showAddExisting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                                        children: \"搜索成員\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1248,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: addExistingSearchTerm,\n                                                        onChange: (e)=>{\n                                                            setAddExistingSearchTerm(e.target.value);\n                                                            setCurrentPage(1);\n                                                        },\n                                                        placeholder: \"輸入成員姓名\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1251,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1247,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                                        children: \"職銜篩選\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1263,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: addExistingCategoryFilter,\n                                                        onChange: (e)=>{\n                                                            setAddExistingCategoryFilter(e.target.value);\n                                                            setCurrentPage(1);\n                                                        },\n                                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"全部職銜\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 1274,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            allCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: category,\n                                                                    children: category\n                                                                }, category, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1276,\n                                                                    columnNumber: 27\n                                                                }, this))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1266,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1262,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1246,\n                                        columnNumber: 19\n                                    }, this),\n                                    selectedParticipants.size > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-blue-800 dark:text-blue-200\",\n                                                children: [\n                                                    \"已選擇 \",\n                                                    selectedParticipants.size,\n                                                    \" 位成員\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1287,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setSelectedParticipants(new Set()),\n                                                        className: \"px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600\",\n                                                        children: \"清除選擇\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1291,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleBatchAddToSession,\n                                                        className: \"px-3 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600\",\n                                                        children: \"批量添加\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1297,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1290,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1286,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3\",\n                                        children: filteredAvailableParticipants.participants.map((participant)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-3 border rounded-md cursor-pointer transition-colors\", selectedParticipants.has(participant.id) ? \"border-blue-500 bg-blue-50 dark:bg-blue-900/20\" : \"border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-650\"),\n                                                onClick: ()=>{\n                                                    const newSelected = new Set(selectedParticipants);\n                                                    if (newSelected.has(participant.id)) {\n                                                        newSelected.delete(participant.id);\n                                                    } else {\n                                                        newSelected.add(participant.id);\n                                                    }\n                                                    setSelectedParticipants(newSelected);\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-gray-900 dark:text-white\",\n                                                                    children: participant.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1330,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                participant.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                    children: [\n                                                                        \"職銜: \",\n                                                                        participant.category\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1332,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                (()=>{\n                                                                    const participantStat = statistics.participantStats.find((p)=>p.id === participant.id);\n                                                                    if (!participantStat) return null;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"mt-1 space-y-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                                                                        children: [\n                                                                                            \"總體: \",\n                                                                                            participantStat.participationDetails.participationRatio\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                                        lineNumber: 1341,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    participantStat.isHighParticipation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"px-1 py-0.5 bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 text-xs rounded\",\n                                                                                        children: \"高參與\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                                        lineNumber: 1345,\n                                                                                        columnNumber: 39\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                                lineNumber: 1340,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            selectedSessionId && participantStat.sessionParticipationDetails[selectedSessionId] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs text-blue-600 dark:text-blue-400\",\n                                                                                children: [\n                                                                                    \"本屆: \",\n                                                                                    participantStat.sessionParticipationDetails[selectedSessionId].participationRatio\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                                lineNumber: 1351,\n                                                                                columnNumber: 37\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                        lineNumber: 1339,\n                                                                        columnNumber: 33\n                                                                    }, this);\n                                                                })()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1329,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: selectedParticipants.has(participant.id),\n                                                            onChange: ()=>{},\n                                                            className: \"ml-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1359,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 1328,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, participant.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1310,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1308,\n                                        columnNumber: 19\n                                    }, this),\n                                    filteredAvailableParticipants.totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                children: [\n                                                    \"顯示 \",\n                                                    (currentPage - 1) * pageSize + 1,\n                                                    \" -\",\n                                                    \" \",\n                                                    Math.min(currentPage * pageSize, filteredAvailableParticipants.total),\n                                                    \" /\",\n                                                    \" \",\n                                                    filteredAvailableParticipants.total,\n                                                    \" 位成員\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1373,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setCurrentPage(Math.max(1, currentPage - 1)),\n                                                        disabled: currentPage === 1,\n                                                        className: \"px-3 py-1 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded text-sm hover:bg-gray-300 dark:hover:bg-gray-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                        children: \"上一頁\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1379,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-3 py-1 text-sm text-gray-700 dark:text-gray-300\",\n                                                        children: [\n                                                            currentPage,\n                                                            \" / \",\n                                                            filteredAvailableParticipants.totalPages\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1386,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setCurrentPage(Math.min(filteredAvailableParticipants.totalPages, currentPage + 1)),\n                                                        disabled: currentPage === filteredAvailableParticipants.totalPages,\n                                                        className: \"px-3 py-1 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded text-sm hover:bg-gray-300 dark:hover:bg-gray-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                        children: \"下一頁\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1389,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1378,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1372,\n                                        columnNumber: 21\n                                    }, this),\n                                    filteredAvailableParticipants.participants.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500 dark:text-gray-400\",\n                                        children: availableGlobalParticipants.length > 0 ? \"沒有找到符合篩選條件的成員\" : \"沒有可添加的成員\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1403,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1244,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 1221,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true) : /* 全局參加者視圖 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-700 rounded-lg shadow overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-6 py-4 border-b border-gray-200 dark:border-gray-600\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 dark:text-white\",\n                            children: [\n                                \"所有成員列表 (\",\n                                allParticipants.length,\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                            lineNumber: 1416,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 1415,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"min-w-full divide-y divide-gray-200 dark:divide-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    className: \"bg-gray-50 dark:bg-gray-800\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleSort(\"name\"),\n                                                    className: \"flex items-center space-x-1 hover:bg-gray-100 dark:hover:bg-gray-700 px-2 py-1 rounded transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"姓名\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1429,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-4 h-4\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: sortField === \"name\" ? sortDirection === \"asc\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M5 15l7-7 7 7\",\n                                                                className: \"text-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 1433,\n                                                                columnNumber: 29\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M19 9l-7 7-7-7\",\n                                                                className: \"text-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 1441,\n                                                                columnNumber: 29\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M8 9l4-4 4 4m0 6l-4 4-4-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 1450,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1430,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 1425,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1424,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                                                children: \"參與屆別\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1460,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\",\n                                                children: \"操作\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1463,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1423,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                    lineNumber: 1422,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    className: \"bg-white dark:bg-gray-700 divide-y divide-gray-200 dark:divide-gray-600\",\n                                    children: sortedAllParticipants.map((participant)=>{\n                                        const history = getParticipantHistory(participant.id);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"hover:bg-gray-50 dark:hover:bg-gray-650\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\",\n                                                    children: participant.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 1473,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 text-sm text-gray-900 dark:text-white\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-1\",\n                                                        children: [\n                                                            history.map((h)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded\",\n                                                                    title: \"\".concat(h.sessionName, \": \").concat(h.category),\n                                                                    children: h.sessionName\n                                                                }, h.id, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                    lineNumber: 1479,\n                                                                    columnNumber: 29\n                                                                }, this)),\n                                                            history.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                children: \"未參與任何屆別\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                                lineNumber: 1488,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                        lineNumber: 1477,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 1476,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setViewingAttendanceHistory(participant),\n                                                            className: \"text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 mr-4\",\n                                                            children: \"出席記錄\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1493,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setEditingParticipant(participant),\n                                                            className: \"text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 mr-4\",\n                                                            children: \"編輯\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1499,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                if (window.confirm('確定要刪除參加者 \"'.concat(participant.name, '\" 嗎？這將刪除所有相關數據。'))) {\n                                                                    onDeleteParticipant(participant.id);\n                                                                }\n                                                            },\n                                                            className: \"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300\",\n                                                            children: \"刪除\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                            lineNumber: 1505,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 1492,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, participant.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                            lineNumber: 1472,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                    lineNumber: 1468,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                            lineNumber: 1421,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 1420,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                lineNumber: 1414,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-700 rounded-lg shadow p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 dark:text-white mb-4\",\n                        children: editingParticipant ? \"編輯成員\" : editingSessionParticipant ? \"編輯屆別參加者\" : \"新增參加者\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 1527,\n                        columnNumber: 9\n                    }, this),\n                    editingSessionParticipant ? /* 編輯屆別參加者表單 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                        children: \"姓名\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1535,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: editingSessionParticipant.name,\n                                        onChange: (e)=>setEditingSessionParticipant({\n                                                ...editingSessionParticipant,\n                                                name: e.target.value\n                                            }),\n                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1536,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1534,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                        children: \"職銜\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1544,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: editingSessionParticipant.category,\n                                        onChange: (e)=>setEditingSessionParticipant({\n                                                ...editingSessionParticipant,\n                                                category: e.target.value\n                                            }),\n                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"選擇職銜\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1552,\n                                                columnNumber: 17\n                                            }, this),\n                                            allCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: category,\n                                                    children: category\n                                                }, category, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 1554,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1545,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1543,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                        children: \"加入日期\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1561,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"date\",\n                                        value: editingSessionParticipant.joinDate || \"\",\n                                        onChange: (e)=>setEditingSessionParticipant({\n                                                ...editingSessionParticipant,\n                                                joinDate: e.target.value\n                                            }),\n                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1562,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1560,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: editingSessionParticipant.isActive,\n                                            onChange: (e)=>setEditingSessionParticipant({\n                                                    ...editingSessionParticipant,\n                                                    isActive: e.target.checked\n                                                }),\n                                            className: \"mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                            lineNumber: 1573,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                            children: \"在此屆別中活躍\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                            lineNumber: 1581,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                    lineNumber: 1572,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1571,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 1533,\n                        columnNumber: 11\n                    }, this) : /* 新增/編輯全局參加者表單 */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                        children: \"姓名\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1589,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: editingParticipant ? editingParticipant.name : newParticipant.name,\n                                        onChange: (e)=>editingParticipant ? setEditingParticipant({\n                                                ...editingParticipant,\n                                                name: e.target.value\n                                            }) : setNewParticipant({\n                                                ...newParticipant,\n                                                name: e.target.value\n                                            }),\n                                        placeholder: \"輸入參加者姓名\",\n                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1590,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1588,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                        children: \"默認職銜\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1603,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: editingParticipant ? editingParticipant.category || \"\" : newParticipant.category,\n                                        onChange: (e)=>editingParticipant ? setEditingParticipant({\n                                                ...editingParticipant,\n                                                category: e.target.value\n                                            }) : setNewParticipant({\n                                                ...newParticipant,\n                                                category: e.target.value\n                                            }),\n                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"選擇職銜\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1613,\n                                                columnNumber: 17\n                                            }, this),\n                                            allCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: category,\n                                                    children: category\n                                                }, category, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                    lineNumber: 1615,\n                                                    columnNumber: 19\n                                                }, this)),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"新增職銜\",\n                                                children: \"+ 新增職銜\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                                lineNumber: 1619,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1604,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1602,\n                                columnNumber: 13\n                            }, this),\n                            ((editingParticipant === null || editingParticipant === void 0 ? void 0 : editingParticipant.category) === \"新增職銜\" || newParticipant.category === \"新增職銜\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"md:col-span-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                        children: \"新職銜名稱\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1624,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: editingParticipant ? editingParticipant.category === \"新增職銜\" ? \"\" : editingParticipant.category : newParticipantTitle,\n                                        placeholder: \"輸入新職銜名稱\",\n                                        className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white\",\n                                        onChange: (e)=>editingParticipant ? setEditingParticipant({\n                                                ...editingParticipant,\n                                                category: e.target.value\n                                            }) : setNewParticipantTitle(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1625,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1623,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 1587,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 flex justify-end space-x-3\",\n                        children: [\n                            (editingParticipant || editingSessionParticipant) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setEditingParticipant(null);\n                                    setEditingSessionParticipant(null);\n                                },\n                                className: \"px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors\",\n                                children: \"取消\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1643,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: editingSessionParticipant ? handleUpdateSessionParticipant : editingParticipant ? handleUpdateParticipant : handleAddParticipant,\n                                className: \"px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors\",\n                                disabled: isAddingParticipant || (editingSessionParticipant ? !editingSessionParticipant.name.trim() || !editingSessionParticipant.category : editingParticipant ? !editingParticipant.name.trim() : !newParticipant.name.trim() || newParticipant.category === \"新增職銜\" && !newParticipantTitle.trim()),\n                                children: editingSessionParticipant ? \"更新屆別參加者\" : editingParticipant ? \"更新成員\" : \"新增參加者\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1653,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 1641,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                lineNumber: 1526,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-700 rounded-lg shadow p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 dark:text-white mb-4\",\n                        children: \"統計信息\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 1678,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-blue-600 dark:text-blue-400\",\n                                        children: allParticipants.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1681,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                        children: \"所有成員總數\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1682,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1680,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-green-600 dark:text-green-400\",\n                                        children: currentSessionParticipants.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1685,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                        children: \"當前屆別參加者\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1686,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1684,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-purple-600 dark:text-purple-400\",\n                                        children: currentSessionParticipants.filter((sp)=>sp.isActive).length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1689,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                        children: \"活躍參加者\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1692,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1688,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-orange-600 dark:text-orange-400\",\n                                        children: currentSessionCategories.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1695,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                        children: \"當前屆別職銜數\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                        lineNumber: 1696,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                                lineNumber: 1694,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                        lineNumber: 1679,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                lineNumber: 1677,\n                columnNumber: 7\n            }, this),\n            viewingAttendanceHistory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_participant_attendance_history__WEBPACK_IMPORTED_MODULE_4__.ParticipantAttendanceHistory, {\n                participant: viewingAttendanceHistory,\n                activities: activities,\n                sessionParticipants: sessionParticipants,\n                sessions: sessions,\n                onClose: ()=>setViewingAttendanceHistory(null)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n                lineNumber: 1703,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\components\\\\participant-management-page.tsx\",\n        lineNumber: 569,\n        columnNumber: 5\n    }, this);\n}\n_s(ParticipantManagementPage, \"8Ah4ZwH2CyQSLTciY+cPZmXrnsY=\");\n_c = ParticipantManagementPage;\nvar _c;\n$RefreshReg$(_c, \"ParticipantManagementPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/participant-management-page.tsx\n"));

/***/ })

});