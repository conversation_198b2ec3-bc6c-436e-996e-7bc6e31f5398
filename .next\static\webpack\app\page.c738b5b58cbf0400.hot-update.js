"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./hooks/use-participant-handlers.ts":
/*!*******************************************!*\
  !*** ./hooks/use-participant-handlers.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useParticipantHandlers: () => (/* binding */ useParticipantHandlers)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useParticipantHandlers auto */ \nfunction useParticipantHandlers(props) {\n    const { allParticipants, sessionParticipants, activities, setAllParticipants, setSessionParticipants, setActivities } = props;\n    const handleAddParticipant = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useParticipantHandlers.useCallback[handleAddParticipant]\": (newParticipant)=>{\n            // 使用時間戳和隨機數生成唯一ID，避免ID衝突\n            const id = \"\".concat(Date.now(), \"-\").concat(Math.random().toString(36).substr(2, 9));\n            const participant = {\n                id,\n                ...newParticipant,\n                attendance: {}\n            };\n            console.log('Adding participant to global list:', participant) // 調試日誌\n            ;\n            setAllParticipants([\n                ...allParticipants,\n                participant\n            ]);\n        }\n    }[\"useParticipantHandlers.useCallback[handleAddParticipant]\"], [\n        allParticipants,\n        setAllParticipants\n    ]);\n    const handleAddSessionParticipant = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useParticipantHandlers.useCallback[handleAddSessionParticipant]\": (newSessionParticipant)=>{\n            // 使用時間戳和隨機數生成唯一ID，避免ID衝突\n            const id = \"session-\".concat(Date.now(), \"-\").concat(Math.random().toString(36).substr(2, 9));\n            const sessionParticipant = {\n                id,\n                ...newSessionParticipant\n            };\n            console.log('Adding session participant:', sessionParticipant) // 調試日誌\n            ;\n            setSessionParticipants([\n                ...sessionParticipants,\n                sessionParticipant\n            ]);\n        }\n    }[\"useParticipantHandlers.useCallback[handleAddSessionParticipant]\"], [\n        sessionParticipants,\n        setSessionParticipants\n    ]);\n    const handleBulkAddParticipants = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useParticipantHandlers.useCallback[handleBulkAddParticipants]\": (newParticipants)=>{\n            const participants = newParticipants.map({\n                \"useParticipantHandlers.useCallback[handleBulkAddParticipants].participants\": (p)=>{\n                    // 使用時間戳和隨機數生成唯一ID，避免ID衝突\n                    const id = \"bulk-\".concat(Date.now(), \"-\").concat(Math.random().toString(36).substr(2, 9));\n                    const participant = {\n                        id,\n                        ...p,\n                        attendance: {}\n                    };\n                    return participant;\n                }\n            }[\"useParticipantHandlers.useCallback[handleBulkAddParticipants].participants\"]);\n            console.log('Bulk adding participants:', participants) // 調試日誌\n            ;\n            setAllParticipants([\n                ...allParticipants,\n                ...participants\n            ]);\n        }\n    }[\"useParticipantHandlers.useCallback[handleBulkAddParticipants]\"], [\n        allParticipants,\n        setAllParticipants\n    ]);\n    const handleUpdateParticipant = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useParticipantHandlers.useCallback[handleUpdateParticipant]\": (updatedParticipant)=>{\n            setAllParticipants(allParticipants.map({\n                \"useParticipantHandlers.useCallback[handleUpdateParticipant]\": (p)=>p.id === updatedParticipant.id ? updatedParticipant : p\n            }[\"useParticipantHandlers.useCallback[handleUpdateParticipant]\"]));\n            setSessionParticipants(sessionParticipants.map({\n                \"useParticipantHandlers.useCallback[handleUpdateParticipant]\": (sp)=>sp.participantId === updatedParticipant.id ? {\n                        ...sp,\n                        name: updatedParticipant.name\n                    } : sp\n            }[\"useParticipantHandlers.useCallback[handleUpdateParticipant]\"]));\n        }\n    }[\"useParticipantHandlers.useCallback[handleUpdateParticipant]\"], [\n        allParticipants,\n        sessionParticipants,\n        setAllParticipants,\n        setSessionParticipants\n    ]);\n    const handleUpdateSessionParticipant = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useParticipantHandlers.useCallback[handleUpdateSessionParticipant]\": (updatedSessionParticipant)=>{\n            setSessionParticipants(sessionParticipants.map({\n                \"useParticipantHandlers.useCallback[handleUpdateSessionParticipant]\": (sp)=>sp.id === updatedSessionParticipant.id ? updatedSessionParticipant : sp\n            }[\"useParticipantHandlers.useCallback[handleUpdateSessionParticipant]\"]));\n        }\n    }[\"useParticipantHandlers.useCallback[handleUpdateSessionParticipant]\"], [\n        sessionParticipants,\n        setSessionParticipants\n    ]);\n    const handleDeleteParticipant = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useParticipantHandlers.useCallback[handleDeleteParticipant]\": (participantId)=>{\n            setAllParticipants(allParticipants.filter({\n                \"useParticipantHandlers.useCallback[handleDeleteParticipant]\": (p)=>p.id !== participantId\n            }[\"useParticipantHandlers.useCallback[handleDeleteParticipant]\"]));\n            setSessionParticipants(sessionParticipants.filter({\n                \"useParticipantHandlers.useCallback[handleDeleteParticipant]\": (sp)=>sp.participantId !== participantId\n            }[\"useParticipantHandlers.useCallback[handleDeleteParticipant]\"]));\n            setActivities(activities.map({\n                \"useParticipantHandlers.useCallback[handleDeleteParticipant]\": (activity)=>({\n                        ...activity,\n                        participants: activity.participants.filter({\n                            \"useParticipantHandlers.useCallback[handleDeleteParticipant]\": (p)=>p.id !== participantId\n                        }[\"useParticipantHandlers.useCallback[handleDeleteParticipant]\"])\n                    })\n            }[\"useParticipantHandlers.useCallback[handleDeleteParticipant]\"]));\n        }\n    }[\"useParticipantHandlers.useCallback[handleDeleteParticipant]\"], [\n        allParticipants,\n        sessionParticipants,\n        activities,\n        setAllParticipants,\n        setSessionParticipants,\n        setActivities\n    ]);\n    const handleRemoveFromSession = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useParticipantHandlers.useCallback[handleRemoveFromSession]\": (sessionParticipantId)=>{\n            // 找到要移除的屆別參加者\n            const sessionParticipantToRemove = sessionParticipants.find({\n                \"useParticipantHandlers.useCallback[handleRemoveFromSession].sessionParticipantToRemove\": (sp)=>sp.id === sessionParticipantId\n            }[\"useParticipantHandlers.useCallback[handleRemoveFromSession].sessionParticipantToRemove\"]);\n            if (sessionParticipantToRemove) {\n                // 從屆別參加者列表中移除\n                setSessionParticipants(sessionParticipants.filter({\n                    \"useParticipantHandlers.useCallback[handleRemoveFromSession]\": (sp)=>sp.id !== sessionParticipantId\n                }[\"useParticipantHandlers.useCallback[handleRemoveFromSession]\"]));\n                // 從所有相關活動中移除該參加者\n                setActivities(activities.map({\n                    \"useParticipantHandlers.useCallback[handleRemoveFromSession]\": (activity)=>({\n                            ...activity,\n                            participants: activity.participants.filter({\n                                \"useParticipantHandlers.useCallback[handleRemoveFromSession]\": (p)=>p.id !== sessionParticipantToRemove.participantId\n                            }[\"useParticipantHandlers.useCallback[handleRemoveFromSession]\"])\n                        })\n                }[\"useParticipantHandlers.useCallback[handleRemoveFromSession]\"]));\n            }\n        }\n    }[\"useParticipantHandlers.useCallback[handleRemoveFromSession]\"], [\n        sessionParticipants,\n        activities,\n        setSessionParticipants,\n        setActivities\n    ]);\n    const handleBulkDeleteTitle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useParticipantHandlers.useCallback[handleBulkDeleteTitle]\": (title)=>{\n            // 找出所有使用該職銜的參加者\n            const participantsToUpdate = allParticipants.filter({\n                \"useParticipantHandlers.useCallback[handleBulkDeleteTitle].participantsToUpdate\": (p)=>p.category === title && !p.name.startsWith('職銜佔位符-')\n            }[\"useParticipantHandlers.useCallback[handleBulkDeleteTitle].participantsToUpdate\"]);\n            const placeholders = allParticipants.filter({\n                \"useParticipantHandlers.useCallback[handleBulkDeleteTitle].placeholders\": (p)=>p.category === title && p.name.startsWith('職銜佔位符-')\n            }[\"useParticipantHandlers.useCallback[handleBulkDeleteTitle].placeholders\"]);\n            const sessionParticipantsToUpdate = sessionParticipants.filter({\n                \"useParticipantHandlers.useCallback[handleBulkDeleteTitle].sessionParticipantsToUpdate\": (sp)=>sp.category === title\n            }[\"useParticipantHandlers.useCallback[handleBulkDeleteTitle].sessionParticipantsToUpdate\"]);\n            // 一次性更新所有狀態\n            // 更新全局參加者 - 清除職銜\n            const updatedAllParticipants = allParticipants.map({\n                \"useParticipantHandlers.useCallback[handleBulkDeleteTitle].updatedAllParticipants\": (p)=>{\n                    if (p.category === title && !p.name.startsWith('職銜佔位符-')) {\n                        return {\n                            ...p,\n                            category: \"\"\n                        };\n                    }\n                    return p;\n                }\n            }[\"useParticipantHandlers.useCallback[handleBulkDeleteTitle].updatedAllParticipants\"]).filter({\n                \"useParticipantHandlers.useCallback[handleBulkDeleteTitle].updatedAllParticipants\": (p)=>!p.name.startsWith(\"職銜佔位符-\".concat(title))\n            }[\"useParticipantHandlers.useCallback[handleBulkDeleteTitle].updatedAllParticipants\"]) // 移除佔位符\n            ;\n            // 更新屆別參加者 - 清除職銜\n            const updatedSessionParticipants = sessionParticipants.map({\n                \"useParticipantHandlers.useCallback[handleBulkDeleteTitle].updatedSessionParticipants\": (sp)=>{\n                    if (sp.category === title) {\n                        return {\n                            ...sp,\n                            category: \"\"\n                        };\n                    }\n                    return sp;\n                }\n            }[\"useParticipantHandlers.useCallback[handleBulkDeleteTitle].updatedSessionParticipants\"]);\n            // 從活動中移除佔位符參加者\n            const updatedActivities = activities.map({\n                \"useParticipantHandlers.useCallback[handleBulkDeleteTitle].updatedActivities\": (activity)=>({\n                        ...activity,\n                        participants: activity.participants.filter({\n                            \"useParticipantHandlers.useCallback[handleBulkDeleteTitle].updatedActivities\": (p)=>!placeholders.some({\n                                    \"useParticipantHandlers.useCallback[handleBulkDeleteTitle].updatedActivities\": (placeholder)=>placeholder.id === p.id\n                                }[\"useParticipantHandlers.useCallback[handleBulkDeleteTitle].updatedActivities\"])\n                        }[\"useParticipantHandlers.useCallback[handleBulkDeleteTitle].updatedActivities\"])\n                    })\n            }[\"useParticipantHandlers.useCallback[handleBulkDeleteTitle].updatedActivities\"]);\n            // 批量更新所有狀態\n            setAllParticipants(updatedAllParticipants);\n            setSessionParticipants(updatedSessionParticipants);\n            setActivities(updatedActivities);\n            return {\n                participantsUpdated: participantsToUpdate.length,\n                placeholdersRemoved: placeholders.length,\n                sessionParticipantsUpdated: sessionParticipantsToUpdate.length\n            };\n        }\n    }[\"useParticipantHandlers.useCallback[handleBulkDeleteTitle]\"], [\n        allParticipants,\n        sessionParticipants,\n        activities,\n        setAllParticipants,\n        setSessionParticipants,\n        setActivities\n    ]);\n    return {\n        handleAddParticipant,\n        handleAddSessionParticipant,\n        handleBulkAddParticipants,\n        handleUpdateParticipant,\n        handleUpdateSessionParticipant,\n        handleDeleteParticipant,\n        handleRemoveFromSession,\n        handleBulkDeleteTitle\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/use-participant-handlers.ts\n"));

/***/ })

});