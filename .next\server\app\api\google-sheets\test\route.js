/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/google-sheets/test/route";
exports.ids = ["app/api/google-sheets/test/route"];
exports.modules = {

/***/ "(rsc)/./app/api/google-sheets/test/route.ts":
/*!*********************************************!*\
  !*** ./app/api/google-sheets/test/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/server.js\");\n/* harmony import */ var googleapis__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! googleapis */ \"(rsc)/./node_modules/.pnpm/googleapis@129.0.0/node_modules/googleapis/build/src/index.js\");\n// Google Sheets 連接測試 API 路由\n\n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { accessToken, spreadsheetId } = body;\n        // 驗證必要參數\n        if (!spreadsheetId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: '缺少 Spreadsheet ID'\n            }, {\n                status: 400\n            });\n        }\n        if (!accessToken) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: '缺少 Access Token，請先完成 Google 登入'\n            }, {\n                status: 400\n            });\n        }\n        // 使用 access token 創建 sheets 實例\n        const sheets = googleapis__WEBPACK_IMPORTED_MODULE_1__.google.sheets({\n            version: 'v4',\n            auth: accessToken\n        });\n        // 測試連接：嘗試獲取試算表信息\n        const response = await sheets.spreadsheets.get({\n            spreadsheetId: spreadsheetId\n        });\n        // 如果成功獲取試算表信息，表示連接正常\n        if (response.data) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: '連接測試成功！',\n                spreadsheetTitle: response.data.properties?.title || '未知標題',\n                sheetCount: response.data.sheets?.length || 0\n            });\n        } else {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: '無法獲取試算表信息'\n            }, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        console.error('Google Sheets 連接測試失敗:', error);\n        // 處理常見錯誤\n        let errorMessage = '連接測試失敗';\n        if (error.code === 404) {\n            errorMessage = '找不到指定的 Google Sheets，請檢查 Spreadsheet ID 是否正確';\n        } else if (error.code === 403) {\n            errorMessage = '沒有權限訪問此 Google Sheets，請確保已授權或將 Google 帳戶添加為編輯者';\n        } else if (error.code === 401) {\n            errorMessage = 'Access Token 無效或已過期，請重新登入';\n        } else if (error.message) {\n            errorMessage = error.message;\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: errorMessage,\n            details: error.code ? `錯誤代碼: ${error.code}` : undefined\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        message: 'Google Sheets 連接測試 API'\n    }, {\n        status: 200\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/google-sheets/test/route.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgoogle-sheets%2Ftest%2Froute&page=%2Fapi%2Fgoogle-sheets%2Ftest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgoogle-sheets%2Ftest%2Froute.ts&appDir=C%3A%5CUsers%5CPC-LPK%5CDocuments%5CHKUYA%5Cattendance%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CPC-LPK%5CDocuments%5CHKUYA%5Cattendance&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgoogle-sheets%2Ftest%2Froute&page=%2Fapi%2Fgoogle-sheets%2Ftest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgoogle-sheets%2Ftest%2Froute.ts&appDir=C%3A%5CUsers%5CPC-LPK%5CDocuments%5CHKUYA%5Cattendance%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CPC-LPK%5CDocuments%5CHKUYA%5Cattendance&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_PC_LPK_Documents_HKUYA_attendance_app_api_google_sheets_test_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/google-sheets/test/route.ts */ \"(rsc)/./app/api/google-sheets/test/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/google-sheets/test/route\",\n        pathname: \"/api/google-sheets/test\",\n        filename: \"route\",\n        bundlePath: \"app/api/google-sheets/test/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\HKUYA\\\\attendance\\\\app\\\\api\\\\google-sheets\\\\test\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_PC_LPK_Documents_HKUYA_attendance_app_api_google_sheets_test_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgoogle-sheets%2Ftest%2Froute&page=%2Fapi%2Fgoogle-sheets%2Ftest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgoogle-sheets%2Ftest%2Froute.ts&appDir=C%3A%5CUsers%5CPC-LPK%5CDocuments%5CHKUYA%5Cattendance%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CPC-LPK%5CDocuments%5CHKUYA%5Cattendance&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?96cf":
/*!********************************!*\
  !*** supports-color (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("http2");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1","vendor-chunks/googleapis@129.0.0","vendor-chunks/google-auth-library@9.15.1","vendor-chunks/tr46@0.0.3","vendor-chunks/bignumber.js@9.3.0","vendor-chunks/node-fetch@2.7.0","vendor-chunks/googleapis-common@7.2.0","vendor-chunks/whatwg-url@5.0.0","vendor-chunks/gaxios@6.7.1","vendor-chunks/qs@6.14.0","vendor-chunks/json-bigint@1.0.0","vendor-chunks/google-logging-utils@0.0.2","vendor-chunks/object-inspect@1.13.4","vendor-chunks/gcp-metadata@6.1.1","vendor-chunks/debug@4.4.1","vendor-chunks/get-intrinsic@1.3.0","vendor-chunks/https-proxy-agent@7.0.6","vendor-chunks/gtoken@7.1.0","vendor-chunks/uuid@9.0.1","vendor-chunks/agent-base@7.1.3","vendor-chunks/jws@4.0.0","vendor-chunks/jwa@2.0.1","vendor-chunks/url-template@2.0.8","vendor-chunks/ecdsa-sig-formatter@1.0.11","vendor-chunks/webidl-conversions@3.0.1","vendor-chunks/base64-js@1.5.1","vendor-chunks/side-channel-list@1.0.0","vendor-chunks/extend@3.0.2","vendor-chunks/ms@2.1.3","vendor-chunks/side-channel-weakmap@1.0.2","vendor-chunks/has-symbols@1.1.0","vendor-chunks/function-bind@1.1.2","vendor-chunks/side-channel-map@1.0.1","vendor-chunks/safe-buffer@5.2.1","vendor-chunks/side-channel@1.1.0","vendor-chunks/get-proto@1.0.1","vendor-chunks/call-bind-apply-helpers@1.0.2","vendor-chunks/buffer-equal-constant-time@1.0.1","vendor-chunks/dunder-proto@1.0.1","vendor-chunks/math-intrinsics@1.1.0","vendor-chunks/call-bound@1.0.4","vendor-chunks/is-stream@2.0.1","vendor-chunks/es-errors@1.3.0","vendor-chunks/gopd@1.2.0","vendor-chunks/es-define-property@1.0.1","vendor-chunks/hasown@2.0.2","vendor-chunks/es-object-atoms@1.1.1"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@15.2.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgoogle-sheets%2Ftest%2Froute&page=%2Fapi%2Fgoogle-sheets%2Ftest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgoogle-sheets%2Ftest%2Froute.ts&appDir=C%3A%5CUsers%5CPC-LPK%5CDocuments%5CHKUYA%5Cattendance%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CPC-LPK%5CDocuments%5CHKUYA%5Cattendance&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();