"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/gtoken@7.1.0";
exports.ids = ["vendor-chunks/gtoken@7.1.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/gtoken@7.1.0/node_modules/gtoken/build/src/index.js":
/*!********************************************************************************!*\
  !*** ./node_modules/.pnpm/gtoken@7.1.0/node_modules/gtoken/build/src/index.js ***!
  \********************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/**\n * Copyright 2018 Google LLC\n *\n * Distributed under MIT license.\n * See file LICENSE for detail or copy at https://opensource.org/licenses/MIT\n */\nvar __classPrivateFieldGet = (this && this.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar __classPrivateFieldSet = (this && this.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\nvar _GoogleToken_instances, _GoogleToken_inFlightRequest, _GoogleToken_getTokenAsync, _GoogleToken_getTokenAsyncInner, _GoogleToken_ensureEmail, _GoogleToken_revokeTokenAsync, _GoogleToken_configure, _GoogleToken_requestToken;\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.GoogleToken = void 0;\nconst fs = __webpack_require__(/*! fs */ \"fs\");\nconst gaxios_1 = __webpack_require__(/*! gaxios */ \"(rsc)/./node_modules/.pnpm/gaxios@6.7.1/node_modules/gaxios/build/src/index.js\");\nconst jws = __webpack_require__(/*! jws */ \"(rsc)/./node_modules/.pnpm/jws@4.0.0/node_modules/jws/index.js\");\nconst path = __webpack_require__(/*! path */ \"path\");\nconst util_1 = __webpack_require__(/*! util */ \"util\");\nconst readFile = fs.readFile\n    ? (0, util_1.promisify)(fs.readFile)\n    : async () => {\n        // if running in the web-browser, fs.readFile may not have been shimmed.\n        throw new ErrorWithCode('use key rather than keyFile.', 'MISSING_CREDENTIALS');\n    };\nconst GOOGLE_TOKEN_URL = 'https://www.googleapis.com/oauth2/v4/token';\nconst GOOGLE_REVOKE_TOKEN_URL = 'https://accounts.google.com/o/oauth2/revoke?token=';\nclass ErrorWithCode extends Error {\n    constructor(message, code) {\n        super(message);\n        this.code = code;\n    }\n}\nclass GoogleToken {\n    get accessToken() {\n        return this.rawToken ? this.rawToken.access_token : undefined;\n    }\n    get idToken() {\n        return this.rawToken ? this.rawToken.id_token : undefined;\n    }\n    get tokenType() {\n        return this.rawToken ? this.rawToken.token_type : undefined;\n    }\n    get refreshToken() {\n        return this.rawToken ? this.rawToken.refresh_token : undefined;\n    }\n    /**\n     * Create a GoogleToken.\n     *\n     * @param options  Configuration object.\n     */\n    constructor(options) {\n        _GoogleToken_instances.add(this);\n        this.transporter = {\n            request: opts => (0, gaxios_1.request)(opts),\n        };\n        _GoogleToken_inFlightRequest.set(this, void 0);\n        __classPrivateFieldGet(this, _GoogleToken_instances, \"m\", _GoogleToken_configure).call(this, options);\n    }\n    /**\n     * Returns whether the token has expired.\n     *\n     * @return true if the token has expired, false otherwise.\n     */\n    hasExpired() {\n        const now = new Date().getTime();\n        if (this.rawToken && this.expiresAt) {\n            return now >= this.expiresAt;\n        }\n        else {\n            return true;\n        }\n    }\n    /**\n     * Returns whether the token will expire within eagerRefreshThresholdMillis\n     *\n     * @return true if the token will be expired within eagerRefreshThresholdMillis, false otherwise.\n     */\n    isTokenExpiring() {\n        var _a;\n        const now = new Date().getTime();\n        const eagerRefreshThresholdMillis = (_a = this.eagerRefreshThresholdMillis) !== null && _a !== void 0 ? _a : 0;\n        if (this.rawToken && this.expiresAt) {\n            return this.expiresAt <= now + eagerRefreshThresholdMillis;\n        }\n        else {\n            return true;\n        }\n    }\n    getToken(callback, opts = {}) {\n        if (typeof callback === 'object') {\n            opts = callback;\n            callback = undefined;\n        }\n        opts = Object.assign({\n            forceRefresh: false,\n        }, opts);\n        if (callback) {\n            const cb = callback;\n            __classPrivateFieldGet(this, _GoogleToken_instances, \"m\", _GoogleToken_getTokenAsync).call(this, opts).then(t => cb(null, t), callback);\n            return;\n        }\n        return __classPrivateFieldGet(this, _GoogleToken_instances, \"m\", _GoogleToken_getTokenAsync).call(this, opts);\n    }\n    /**\n     * Given a keyFile, extract the key and client email if available\n     * @param keyFile Path to a json, pem, or p12 file that contains the key.\n     * @returns an object with privateKey and clientEmail properties\n     */\n    async getCredentials(keyFile) {\n        const ext = path.extname(keyFile);\n        switch (ext) {\n            case '.json': {\n                const key = await readFile(keyFile, 'utf8');\n                const body = JSON.parse(key);\n                const privateKey = body.private_key;\n                const clientEmail = body.client_email;\n                if (!privateKey || !clientEmail) {\n                    throw new ErrorWithCode('private_key and client_email are required.', 'MISSING_CREDENTIALS');\n                }\n                return { privateKey, clientEmail };\n            }\n            case '.der':\n            case '.crt':\n            case '.pem': {\n                const privateKey = await readFile(keyFile, 'utf8');\n                return { privateKey };\n            }\n            case '.p12':\n            case '.pfx': {\n                throw new ErrorWithCode('*.p12 certificates are not supported after v6.1.2. ' +\n                    'Consider utilizing *.json format or converting *.p12 to *.pem using the OpenSSL CLI.', 'UNKNOWN_CERTIFICATE_TYPE');\n            }\n            default:\n                throw new ErrorWithCode('Unknown certificate type. Type is determined based on file extension. ' +\n                    'Current supported extensions are *.json, and *.pem.', 'UNKNOWN_CERTIFICATE_TYPE');\n        }\n    }\n    revokeToken(callback) {\n        if (callback) {\n            __classPrivateFieldGet(this, _GoogleToken_instances, \"m\", _GoogleToken_revokeTokenAsync).call(this).then(() => callback(), callback);\n            return;\n        }\n        return __classPrivateFieldGet(this, _GoogleToken_instances, \"m\", _GoogleToken_revokeTokenAsync).call(this);\n    }\n}\nexports.GoogleToken = GoogleToken;\n_GoogleToken_inFlightRequest = new WeakMap(), _GoogleToken_instances = new WeakSet(), _GoogleToken_getTokenAsync = async function _GoogleToken_getTokenAsync(opts) {\n    if (__classPrivateFieldGet(this, _GoogleToken_inFlightRequest, \"f\") && !opts.forceRefresh) {\n        return __classPrivateFieldGet(this, _GoogleToken_inFlightRequest, \"f\");\n    }\n    try {\n        return await (__classPrivateFieldSet(this, _GoogleToken_inFlightRequest, __classPrivateFieldGet(this, _GoogleToken_instances, \"m\", _GoogleToken_getTokenAsyncInner).call(this, opts), \"f\"));\n    }\n    finally {\n        __classPrivateFieldSet(this, _GoogleToken_inFlightRequest, undefined, \"f\");\n    }\n}, _GoogleToken_getTokenAsyncInner = async function _GoogleToken_getTokenAsyncInner(opts) {\n    if (this.isTokenExpiring() === false && opts.forceRefresh === false) {\n        return Promise.resolve(this.rawToken);\n    }\n    if (!this.key && !this.keyFile) {\n        throw new Error('No key or keyFile set.');\n    }\n    if (!this.key && this.keyFile) {\n        const creds = await this.getCredentials(this.keyFile);\n        this.key = creds.privateKey;\n        this.iss = creds.clientEmail || this.iss;\n        if (!creds.clientEmail) {\n            __classPrivateFieldGet(this, _GoogleToken_instances, \"m\", _GoogleToken_ensureEmail).call(this);\n        }\n    }\n    return __classPrivateFieldGet(this, _GoogleToken_instances, \"m\", _GoogleToken_requestToken).call(this);\n}, _GoogleToken_ensureEmail = function _GoogleToken_ensureEmail() {\n    if (!this.iss) {\n        throw new ErrorWithCode('email is required.', 'MISSING_CREDENTIALS');\n    }\n}, _GoogleToken_revokeTokenAsync = async function _GoogleToken_revokeTokenAsync() {\n    if (!this.accessToken) {\n        throw new Error('No token to revoke.');\n    }\n    const url = GOOGLE_REVOKE_TOKEN_URL + this.accessToken;\n    await this.transporter.request({\n        url,\n        retry: true,\n    });\n    __classPrivateFieldGet(this, _GoogleToken_instances, \"m\", _GoogleToken_configure).call(this, {\n        email: this.iss,\n        sub: this.sub,\n        key: this.key,\n        keyFile: this.keyFile,\n        scope: this.scope,\n        additionalClaims: this.additionalClaims,\n    });\n}, _GoogleToken_configure = function _GoogleToken_configure(options = {}) {\n    this.keyFile = options.keyFile;\n    this.key = options.key;\n    this.rawToken = undefined;\n    this.iss = options.email || options.iss;\n    this.sub = options.sub;\n    this.additionalClaims = options.additionalClaims;\n    if (typeof options.scope === 'object') {\n        this.scope = options.scope.join(' ');\n    }\n    else {\n        this.scope = options.scope;\n    }\n    this.eagerRefreshThresholdMillis = options.eagerRefreshThresholdMillis;\n    if (options.transporter) {\n        this.transporter = options.transporter;\n    }\n}, _GoogleToken_requestToken = \n/**\n * Request the token from Google.\n */\nasync function _GoogleToken_requestToken() {\n    var _a, _b;\n    const iat = Math.floor(new Date().getTime() / 1000);\n    const additionalClaims = this.additionalClaims || {};\n    const payload = Object.assign({\n        iss: this.iss,\n        scope: this.scope,\n        aud: GOOGLE_TOKEN_URL,\n        exp: iat + 3600,\n        iat,\n        sub: this.sub,\n    }, additionalClaims);\n    const signedJWT = jws.sign({\n        header: { alg: 'RS256' },\n        payload,\n        secret: this.key,\n    });\n    try {\n        const r = await this.transporter.request({\n            method: 'POST',\n            url: GOOGLE_TOKEN_URL,\n            data: {\n                grant_type: 'urn:ietf:params:oauth:grant-type:jwt-bearer',\n                assertion: signedJWT,\n            },\n            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },\n            responseType: 'json',\n            retryConfig: {\n                httpMethodsToRetry: ['POST'],\n            },\n        });\n        this.rawToken = r.data;\n        this.expiresAt =\n            r.data.expires_in === null || r.data.expires_in === undefined\n                ? undefined\n                : (iat + r.data.expires_in) * 1000;\n        return this.rawToken;\n    }\n    catch (e) {\n        this.rawToken = undefined;\n        this.tokenExpires = undefined;\n        const body = e.response && ((_a = e.response) === null || _a === void 0 ? void 0 : _a.data)\n            ? (_b = e.response) === null || _b === void 0 ? void 0 : _b.data\n            : {};\n        if (body.error) {\n            const desc = body.error_description\n                ? `: ${body.error_description}`\n                : '';\n            e.message = `${body.error}${desc}`;\n        }\n        throw e;\n    }\n};\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/gtoken@7.1.0/node_modules/gtoken/build/src/index.js\n");

/***/ })

};
;