// Google Sheets 連接測試 API 路由
import { NextRequest, NextResponse } from 'next/server'
import { google } from 'googleapis'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { accessToken, spreadsheetId } = body as { 
      accessToken?: string
      spreadsheetId: string 
    }

    // 驗證必要參數
    if (!spreadsheetId) {
      return NextResponse.json(
        { success: false, error: '缺少 Spreadsheet ID' },
        { status: 400 }
      )
    }

    if (!accessToken) {
      return NextResponse.json(
        { success: false, error: '缺少 Access Token，請先完成 Google 登入' },
        { status: 400 }
      )
    }

    // 使用 access token 創建 sheets 實例
    const sheets = google.sheets({ 
      version: 'v4', 
      auth: accessToken
    })

    // 測試連接：嘗試獲取試算表信息
    const response = await sheets.spreadsheets.get({
      spreadsheetId: spreadsheetId,
    })

    // 如果成功獲取試算表信息，表示連接正常
    if (response.data) {
      return NextResponse.json({
        success: true,
        message: '連接測試成功！',
        spreadsheetTitle: response.data.properties?.title || '未知標題',
        sheetCount: response.data.sheets?.length || 0
      })
    } else {
      return NextResponse.json(
        { success: false, error: '無法獲取試算表信息' },
        { status: 500 }
      )
    }

  } catch (error: any) {
    console.error('Google Sheets 連接測試失敗:', error)
    
    // 處理常見錯誤
    let errorMessage = '連接測試失敗'
    
    if (error.code === 404) {
      errorMessage = '找不到指定的 Google Sheets，請檢查 Spreadsheet ID 是否正確'
    } else if (error.code === 403) {
      errorMessage = '沒有權限訪問此 Google Sheets，請確保已授權或將 Google 帳戶添加為編輯者'
    } else if (error.code === 401) {
      errorMessage = 'Access Token 無效或已過期，請重新登入'
    } else if (error.message) {
      errorMessage = error.message
    }

    return NextResponse.json(
      { 
        success: false, 
        error: errorMessage,
        details: error.code ? `錯誤代碼: ${error.code}` : undefined
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json(
    { message: 'Google Sheets 連接測試 API' },
    { status: 200 }
  )
}
