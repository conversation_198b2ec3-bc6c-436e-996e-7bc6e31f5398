// Google API 測試組件
import React, { useState, useEffect } from 'react'
import { AlertCircle, CheckCircle, Loader2 } from 'lucide-react'

interface GoogleApiTestProps {
  clientId: string
}

export function GoogleApiTest({ clientId }: GoogleApiTestProps) {
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading')
  const [message, setMessage] = useState<string>('')
  const [details, setDetails] = useState<any>(null)

  useEffect(() => {
    const testGoogleApi = async () => {
      try {
        setStatus('loading')
        setMessage('正在測試 Google API...')

        // 檢查 Client ID
        if (!clientId) {
          throw new Error('Client ID 未設置')
        }

        if (!clientId.includes('.apps.googleusercontent.com')) {
          throw new Error('Client ID 格式不正確')
        }

        // 載入 Google API
        if (!window.gapi) {
          setMessage('正在載入 Google API...')
          await loadGoogleApi()
        }

        // 測試 auth2 初始化
        setMessage('正在初始化 Google Auth2...')
        await new Promise((resolve, reject) => {
          window.gapi.load('auth2', () => {
            window.gapi.auth2.init({
              client_id: clientId,
              scope: 'https://www.googleapis.com/auth/spreadsheets'
            }).then((authInstance: any) => {
              setDetails({
                clientId: clientId.substring(0, 20) + '...',
                isSignedIn: authInstance.isSignedIn.get(),
                currentUser: authInstance.currentUser.get()?.getBasicProfile()?.getName() || 'N/A',
                origin: window.location.origin
              })
              resolve(authInstance)
            }).catch(reject)
          })
        })

        setStatus('success')
        setMessage('Google API 測試成功！')

      } catch (error: any) {
        console.error('Google API 測試失敗:', error)
        setStatus('error')
        setMessage(error.message || '測試失敗')
        setDetails(error)
      }
    }

    const loadGoogleApi = (): Promise<void> => {
      return new Promise((resolve, reject) => {
        if (window.gapi) {
          resolve()
          return
        }

        const script = document.createElement('script')
        script.src = 'https://apis.google.com/js/api.js'
        script.onload = () => resolve()
        script.onerror = () => reject(new Error('無法載入 Google API'))
        document.body.appendChild(script)
      })
    }

    if (clientId) {
      testGoogleApi()
    }
  }, [clientId])

  const getStatusIcon = () => {
    switch (status) {
      case 'loading':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />
    }
  }

  const getStatusColor = () => {
    switch (status) {
      case 'loading':
        return 'border-blue-200 bg-blue-50 dark:bg-blue-900/20'
      case 'success':
        return 'border-green-200 bg-green-50 dark:bg-green-900/20'
      case 'error':
        return 'border-red-200 bg-red-50 dark:bg-red-900/20'
    }
  }

  return (
    <div className={`p-4 border rounded-lg ${getStatusColor()}`}>
      <div className="flex items-start space-x-3">
        {getStatusIcon()}
        <div className="flex-1">
          <div className="font-medium text-gray-900 dark:text-white">
            Google API 連接測試
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {message}
          </div>

          {details && (
            <div className="mt-3 p-3 bg-white dark:bg-gray-800 rounded border">
              <div className="text-xs text-gray-600 dark:text-gray-400">
                <div className="font-medium mb-2">詳細信息：</div>
                <div className="space-y-1">
                  <div>Client ID: {details.clientId}</div>
                  <div>當前域名: {details.origin}</div>
                  <div>登入狀態: {details.isSignedIn ? '已登入' : '未登入'}</div>
                  <div>當前用戶: {details.currentUser}</div>
                </div>
              </div>
            </div>
          )}

          {status === 'error' && (
            <div className="mt-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded">
              <div className="text-sm text-yellow-700 dark:text-yellow-300">
                <div className="font-medium mb-1">可能的解決方案：</div>
                <ol className="list-decimal list-inside space-y-1 text-xs">
                  <li>檢查 Google Cloud Console 中的 OAuth2 設置</li>
                  <li>確認授權的 JavaScript 來源包含當前域名</li>
                  <li>確認 Client ID 格式正確</li>
                  <li>檢查 Google Sheets API 是否已啟用</li>
                </ol>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

declare global {
  interface Window {
    gapi: any
  }
}
