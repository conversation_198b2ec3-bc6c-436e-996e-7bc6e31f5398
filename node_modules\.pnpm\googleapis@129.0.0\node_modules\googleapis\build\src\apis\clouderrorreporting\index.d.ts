/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { clouderrorreporting_v1beta1 } from './v1beta1';
export declare const VERSIONS: {
    v1beta1: typeof clouderrorreporting_v1beta1.Clouderrorreporting;
};
export declare function clouderrorreporting(version: 'v1beta1'): clouderrorreporting_v1beta1.Clouderrorreporting;
export declare function clouderrorreporting(options: clouderrorreporting_v1beta1.Options): clouderrorreporting_v1beta1.Clouderrorreporting;
declare const auth: AuthPlus;
export { auth };
export { clouderrorreporting_v1beta1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
