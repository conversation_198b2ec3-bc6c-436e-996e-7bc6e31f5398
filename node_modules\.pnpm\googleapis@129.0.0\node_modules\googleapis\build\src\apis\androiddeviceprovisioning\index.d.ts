/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { androiddeviceprovisioning_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof androiddeviceprovisioning_v1.Androiddeviceprovisioning;
};
export declare function androiddeviceprovisioning(version: 'v1'): androiddeviceprovisioning_v1.Androiddeviceprovisioning;
export declare function androiddeviceprovisioning(options: androiddeviceprovisioning_v1.Options): androiddeviceprovisioning_v1.Androiddeviceprovisioning;
declare const auth: AuthPlus;
export { auth };
export { androiddeviceprovisioning_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
