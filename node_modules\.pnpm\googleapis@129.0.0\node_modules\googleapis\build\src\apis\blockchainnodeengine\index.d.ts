/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { blockchainnodeengine_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof blockchainnodeengine_v1.Blockchainnodeengine;
};
export declare function blockchainnodeengine(version: 'v1'): blockchainnodeengine_v1.Blockchainnodeengine;
export declare function blockchainnodeengine(options: blockchainnodeengine_v1.Options): blockchainnodeengine_v1.Blockchainnodeengine;
declare const auth: AuthPlus;
export { auth };
export { blockchainnodeengine_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
