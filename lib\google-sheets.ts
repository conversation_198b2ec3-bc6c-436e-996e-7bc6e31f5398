// Google Sheets API 服務層
import { GoogleAuth } from 'google-auth-library'
import { sheets_v4, google } from 'googleapis'
import type {
  GoogleSheetsConfig,
  SheetNames,
  GoogleSheetsApiResponse,
  ParticipantSheetRow,
  ActivitySheetRow,
  AttendanceSheetRow,
  SessionSheetRow,
  SessionParticipantSheetRow,
  SyncResult,
  BatchOperation,
  BatchResult
} from '../types/google-sheets'

export class GoogleSheetsService {
  private sheets: sheets_v4.Sheets | null = null
  private auth: GoogleAuth | null = null
  private config: GoogleSheetsConfig
  private sheetNames: SheetNames

  constructor(config: GoogleSheetsConfig, sheetNames: SheetNames) {
    this.config = config
    this.sheetNames = sheetNames
  }

  /**
   * 初始化 Google Sheets API 連接
   */
  async initialize(accessToken?: string): Promise<GoogleSheetsApiResponse<boolean>> {
    try {
      // 使用 Service Account 認證
      if (this.config.serviceAccountEmail && this.config.privateKey) {
        this.auth = new GoogleAuth({
          credentials: {
            client_email: this.config.serviceAccountEmail,
            private_key: this.config.privateKey.replace(/\\n/g, '\n'),
            project_id: this.config.projectId,
          },
          scopes: ['https://www.googleapis.com/auth/spreadsheets'],
        })
        this.sheets = google.sheets({ version: 'v4', auth: this.auth })
      }
      // 使用 OAuth2 Access Token
      else if (accessToken) {
        this.auth = new GoogleAuth({
          scopes: ['https://www.googleapis.com/auth/spreadsheets'],
        })

        // 直接使用 access token 創建 sheets 實例
        this.sheets = google.sheets({
          version: 'v4',
          auth: accessToken  // 直接使用 access token
        })
      }
      // 使用 OAuth2 認證配置 (備用方案)
      else if (this.config.clientId && this.config.clientSecret) {
        throw new Error('OAuth2 認證需要在客戶端完成，請先登入 Google 帳戶')
      } else {
        throw new Error('缺少必要的認證配置')
      }

      // 測試連接
      await this.testConnection()

      return {
        success: true,
        data: true,
        timestamp: new Date()
      }
    } catch (error) {
      console.error('Google Sheets 初始化失敗:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知錯誤',
        timestamp: new Date()
      }
    }
  }

  /**
   * 測試連接
   */
  private async testConnection(): Promise<void> {
    if (!this.sheets) {
      throw new Error('Google Sheets API 未初始化')
    }

    try {
      await this.sheets.spreadsheets.get({
        spreadsheetId: this.config.spreadsheetId,
      })
    } catch (error) {
      throw new Error(`無法連接到 Google Sheets: ${error}`)
    }
  }

  /**
   * 確保工作表存在，如果不存在則創建
   */
  async ensureSheetsExist(): Promise<GoogleSheetsApiResponse<boolean>> {
    try {
      if (!this.sheets) {
        throw new Error('Google Sheets API 未初始化')
      }

      const spreadsheet = await this.sheets.spreadsheets.get({
        spreadsheetId: this.config.spreadsheetId,
      })

      const existingSheets = spreadsheet.data.sheets?.map(sheet => sheet.properties?.title) || []
      const requiredSheets = Object.values(this.sheetNames)

      for (const sheetName of requiredSheets) {
        if (!existingSheets.includes(sheetName)) {
          await this.createSheet(sheetName)
        }
      }

      // 初始化表頭
      await this.initializeHeaders()

      return {
        success: true,
        data: true,
        timestamp: new Date()
      }
    } catch (error) {
      console.error('確保工作表存在時發生錯誤:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知錯誤',
        timestamp: new Date()
      }
    }
  }

  /**
   * 創建新工作表
   */
  private async createSheet(title: string): Promise<void> {
    if (!this.sheets) {
      throw new Error('Google Sheets API 未初始化')
    }

    await this.sheets.spreadsheets.batchUpdate({
      spreadsheetId: this.config.spreadsheetId,
      requestBody: {
        requests: [{
          addSheet: {
            properties: {
              title,
              gridProperties: {
                rowCount: 1000,
                columnCount: 20
              }
            }
          }
        }]
      }
    })
  }

  /**
   * 初始化表頭
   */
  private async initializeHeaders(): Promise<void> {
    const headers = {
      [this.sheetNames.participants]: [
        'ID', '姓名', '職銜', '電子郵件', '電話', '加入日期', '是否活躍', '備註', '創建時間', '更新時間'
      ],
      [this.sheetNames.activities]: [
        'ID', '活動名稱', '日期', '地點', '描述', '委員會', '類型', '最大參與人數', '是否活躍', '創建時間', '更新時間'
      ],
      [this.sheetNames.attendance]: [
        'ID', '參加者ID', '參加者姓名', '活動ID', '活動名稱', '出席狀態', '簽到時間', '備註', '記錄者', '創建時間', '更新時間'
      ],
      [this.sheetNames.sessions]: [
        'ID', '屆別名稱', '開始日期', '結束日期', '描述', '是否活躍', '創建時間', '更新時間'
      ]
    }

    for (const [sheetName, headerRow] of Object.entries(headers)) {
      await this.updateRange(sheetName, 'A1:Z1', [headerRow])
    }
  }

  /**
   * 讀取指定範圍的數據
   */
  async readRange(sheetName: string, range: string): Promise<GoogleSheetsApiResponse<any[][]>> {
    try {
      if (!this.sheets) {
        throw new Error('Google Sheets API 未初始化')
      }

      const response = await this.sheets.spreadsheets.values.get({
        spreadsheetId: this.config.spreadsheetId,
        range: `${sheetName}!${range}`,
      })

      return {
        success: true,
        data: response.data.values || [],
        timestamp: new Date()
      }
    } catch (error) {
      console.error('讀取數據時發生錯誤:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知錯誤',
        timestamp: new Date()
      }
    }
  }

  /**
   * 更新指定範圍的數據
   */
  async updateRange(sheetName: string, range: string, values: any[][]): Promise<GoogleSheetsApiResponse<boolean>> {
    try {
      if (!this.sheets) {
        throw new Error('Google Sheets API 未初始化')
      }

      await this.sheets.spreadsheets.values.update({
        spreadsheetId: this.config.spreadsheetId,
        range: `${sheetName}!${range}`,
        valueInputOption: 'RAW',
        requestBody: {
          values,
        },
      })

      return {
        success: true,
        data: true,
        timestamp: new Date()
      }
    } catch (error) {
      console.error('更新數據時發生錯誤:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知錯誤',
        timestamp: new Date()
      }
    }
  }

  /**
   * 追加數據到工作表末尾
   */
  async appendData(sheetName: string, values: any[][]): Promise<GoogleSheetsApiResponse<boolean>> {
    try {
      if (!this.sheets) {
        throw new Error('Google Sheets API 未初始化')
      }

      await this.sheets.spreadsheets.values.append({
        spreadsheetId: this.config.spreadsheetId,
        range: `${sheetName}!A:Z`,
        valueInputOption: 'RAW',
        requestBody: {
          values,
        },
      })

      return {
        success: true,
        data: true,
        timestamp: new Date()
      }
    } catch (error) {
      console.error('追加數據時發生錯誤:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知錯誤',
        timestamp: new Date()
      }
    }
  }

  /**
   * 清空工作表數據（保留表頭）
   */
  async clearSheet(sheetName: string): Promise<GoogleSheetsApiResponse<boolean>> {
    try {
      if (!this.sheets) {
        throw new Error('Google Sheets API 未初始化')
      }

      await this.sheets.spreadsheets.values.clear({
        spreadsheetId: this.config.spreadsheetId,
        range: `${sheetName}!A2:Z`,
      })

      return {
        success: true,
        data: true,
        timestamp: new Date()
      }
    } catch (error) {
      console.error('清空工作表時發生錯誤:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知錯誤',
        timestamp: new Date()
      }
    }
  }

  /**
   * 批量操作
   */
  async batchOperations(operations: BatchOperation[]): Promise<GoogleSheetsApiResponse<BatchResult>> {
    try {
      let successful = 0
      let failed = 0
      const errors: string[] = []

      for (const operation of operations) {
        try {
          switch (operation.operation) {
            case 'create':
              await this.appendData(operation.sheetName, operation.data)
              break
            case 'update':
              // 實現更新邏輯
              break
            case 'delete':
              // 實現刪除邏輯
              break
          }
          successful++
        } catch (error) {
          failed++
          errors.push(`${operation.operation} 操作失敗: ${error}`)
        }
      }

      return {
        success: true,
        data: { successful, failed, errors },
        timestamp: new Date()
      }
    } catch (error) {
      console.error('批量操作時發生錯誤:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知錯誤',
        timestamp: new Date()
      }
    }
  }
}
