/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { acceleratedmobilepageurl_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof acceleratedmobilepageurl_v1.Acceleratedmobilepageurl;
};
export declare function acceleratedmobilepageurl(version: 'v1'): acceleratedmobilepageurl_v1.Acceleratedmobilepageurl;
export declare function acceleratedmobilepageurl(options: acceleratedmobilepageurl_v1.Options): acceleratedmobilepageurl_v1.Acceleratedmobilepageurl;
declare const auth: AuthPlus;
export { auth };
export { acceleratedmobilepageurl_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
