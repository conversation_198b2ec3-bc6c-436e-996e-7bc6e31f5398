// Google Sheets 同步 API 路由
import { NextRequest, NextResponse } from 'next/server'
import { GoogleSheetsService } from '../../../../lib/google-sheets'
import { SyncService } from '../../../../lib/sync-service'
import type { 
  GoogleSheetsConfig, 
  SheetNames,
  AttendanceRecord 
} from '../../../../types/google-sheets'
import type { 
  Participant, 
  Activity, 
  Session 
} from '../../../../types'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      config, 
      data, 
      direction = 'push' 
    } = body as { 
      config: GoogleSheetsConfig
      data: {
        participants: Participant[]
        activities: Activity[]
        sessions: Session[]
        attendanceRecords: AttendanceRecord[]
      }
      direction: 'push' | 'pull' | 'bidirectional'
    }

    // 驗證配置
    if (!config.spreadsheetId) {
      return NextResponse.json(
        { success: false, error: '缺少 Spreadsheet ID' },
        { status: 400 }
      )
    }

    const sheetNames: SheetNames = {
      participants: process.env.PARTICIPANTS_SHEET_NAME || '參加者資料',
      activities: process.env.ACTIVITIES_SHEET_NAME || '活動資料',
      attendance: process.env.ATTENDANCE_SHEET_NAME || '出席記錄',
      sessions: process.env.SESSIONS_SHEET_NAME || '屆別資料'
    }

    // 初始化服務
    const googleSheetsService = new GoogleSheetsService(config, sheetNames)
    const syncService = new SyncService(googleSheetsService)

    // 初始化連接
    const initResult = await googleSheetsService.initialize()
    if (!initResult.success) {
      return NextResponse.json(
        { success: false, error: initResult.error },
        { status: 500 }
      )
    }

    let result

    if (direction === 'push') {
      // 推送數據到 Google Sheets
      result = await syncService.performFullSync(data)
    } else if (direction === 'pull') {
      // 從 Google Sheets 拉取數據
      const [participantsResult, activitiesResult, sessionsResult] = await Promise.all([
        syncService.syncParticipantsFromSheets(),
        syncService.syncActivitiesFromSheets(),
        syncService.syncSessionsFromSheets()
      ])

      if (participantsResult.success && activitiesResult.success && sessionsResult.success) {
        result = {
          success: true,
          message: '數據同步成功',
          recordsAffected: (participantsResult.data?.length || 0) + 
                          (activitiesResult.data?.length || 0) + 
                          (sessionsResult.data?.length || 0),
          timestamp: new Date(),
          data: {
            participants: participantsResult.data || [],
            activities: activitiesResult.data || [],
            sessions: sessionsResult.data || []
          }
        }
      } else {
        result = {
          success: false,
          message: '部分數據同步失敗',
          recordsAffected: 0,
          timestamp: new Date()
        }
      }
    } else {
      // 雙向同步（先拉取，再推送）
      const pullResult = await syncService.syncParticipantsFromSheets()
      if (pullResult.success) {
        result = await syncService.performFullSync(data)
      } else {
        result = {
          success: false,
          message: '雙向同步失敗：無法從 Google Sheets 拉取數據',
          recordsAffected: 0,
          timestamp: new Date()
        }
      }
    }

    return NextResponse.json(result)

  } catch (error) {
    console.error('Google Sheets 同步錯誤:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : '未知錯誤',
        recordsAffected: 0,
        timestamp: new Date()
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json(
    { message: 'Google Sheets 同步 API' },
    { status: 200 }
  )
}
