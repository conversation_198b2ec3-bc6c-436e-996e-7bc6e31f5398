"use client"

import type React from "react"

import { useState } from "react"
import { parseFile } from "../utils/file-parsers"
import type { Participant, Activity, Session } from "../types"

export type ImportDataType = "participants" | "activities" | "committees" | "activities-with-participants"

interface ImportData {
  participants: Omit<Participant, "id" | "attendance">[]
  activities: Omit<Activity, "id" | "participants">[]
  committees: string[]
  "activities-with-participants": {
    activity: Omit<Activity, "id" | "participants">
    participants: Omit<Participant, "id" | "attendance">[]
  }[]
}

interface UniversalBulkImportProps {
  dataType: ImportDataType
  sessions?: Session[]
  selectedSessionId?: string | null
  onImport: (data: ImportData[keyof ImportData]) => void
  onCancel: () => void
}

export function UniversalBulkImport({
  dataType,
  sessions = [],
  selectedSessionId,
  onImport,
  onCancel,
}: UniversalBulkImportProps) {
  const [file, setFile] = useState<File | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [preview, setPreview] = useState<any[]>([])
  const [step, setStep] = useState<"upload" | "preview" | "complete">("upload")

  // 獲取數據類型的配置
  const getDataTypeConfig = () => {
    switch (dataType) {
      case "participants":
        return {
          title: "參加者",
          description: "批量導入參加者數據",
          requiredFields: ["姓名 (name/姓名/名稱)", "類別 (category/類別/類型/組別) - 選填"],
          sampleData: [
            { name: "張三", category: "核心成員" },
            { name: "李四", category: "一般成員" },
          ],
        }
      case "activities":
        return {
          title: "活動",
          description: "批量導入活動數據",
          requiredFields: [
            "日期 (date/日期) - 格式: YYYY-MM-DD",
            "活動名稱 (name/名稱/活動名稱)",
            "委員會 (committee/委員會)",
            "描述 (description/描述/說明) - 選填",
          ],
          sampleData: [
            { date: "2024-01-15", name: "週會", committee: "學術委員會", description: "每週例行會議" },
            { date: "2024-01-22", name: "技術分享", committee: "技術委員會", description: "技術交流活動" },
          ],
        }
      case "activities-with-participants":
        return {
          title: "活動和參加者",
          description: "批量導入活動及其參加者數據",
          requiredFields: [
            "日期 (date/日期) - 格式: YYYY-MM-DD",
            "活動名稱 (activityName/活動名稱)",
            "委員會 (committee/委員會)",
            "參加者姓名 (participantName/參加者姓名/姓名)",
            "參加者類別 (participantCategory/參加者類別/類別) - 選填",
            "活動描述 (description/描述/說明) - 選填",
          ],
          sampleData: [
            {
              date: "2024-01-15",
              activityName: "週會",
              committee: "學術委員會",
              participantName: "張三",
              participantCategory: "核心成員",
              description: "每週例行會議",
            },
            {
              date: "2024-01-15",
              activityName: "週會",
              committee: "學術委員會",
              participantName: "李四",
              participantCategory: "一般成員",
              description: "每週例行會議",
            },
            {
              date: "2024-01-22",
              activityName: "技術分享",
              committee: "技術委員會",
              participantName: "王五",
              participantCategory: "技術成員",
              description: "技術交流活動",
            },
          ],
        }
      case "committees":
        return {
          title: "委員會",
          description: "批量導入委員會數據",
          requiredFields: ["委員會名稱 (name/名稱/委員會)"],
          sampleData: [{ name: "學術委員會" }, { name: "活動委員會" }, { name: "技術委員會" }],
        }
      default:
        return {
          title: "數據",
          description: "批量導入數據",
          requiredFields: [],
          sampleData: [],
        }
    }
  }

  const config = getDataTypeConfig()

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0]
    if (selectedFile) {
      setFile(selectedFile)
      setError(null)
    }
  }

  const parseDataByType = (rawData: any[]): any[] => {
    switch (dataType) {
      case "participants":
        return rawData.map((row: any) => {
          if (!row.name && !row["姓名"] && !row["名稱"]) {
            throw new Error("找不到姓名欄位")
          }
          return {
            name: row.name || row["姓名"] || row["名稱"] || "",
            category: row.category || row["類別"] || row["類型"] || row["組別"] || "",
          }
        })

      case "activities":
        return rawData.map((row: any) => {
          if (!row.date && !row["日期"]) {
            throw new Error("找不到日期欄位")
          }
          if (!row.name && !row["名稱"] && !row["活動名稱"]) {
            throw new Error("找不到活動名稱欄位")
          }
          if (!row.committee && !row["委員會"]) {
            throw new Error("找不到委員會欄位")
          }

          const date = row.date || row["日期"]
          // 驗證日期格式
          if (!/^\d{4}-\d{2}-\d{2}$/.test(date)) {
            throw new Error(`日期格式錯誤: ${date}，請使用 YYYY-MM-DD 格式`)
          }

          return {
            sessionId: selectedSessionId || sessions.find((s) => s.isActive)?.id || "",
            date: date,
            name: row.name || row["名稱"] || row["活動名稱"] || "",
            committee: row.committee || row["委員會"] || "",
            description: row.description || row["描述"] || row["說明"] || "",
          }
        })

      case "activities-with-participants":
        // 首先解析所有行
        const parsedRows = rawData.map((row: any, index: number) => {
          if (!row.date && !row["日期"]) {
            throw new Error(`第 ${index + 1} 行：找不到日期欄位`)
          }
          if (!row.activityName && !row["活動名稱"]) {
            throw new Error(`第 ${index + 1} 行：找不到活動名稱欄位`)
          }
          if (!row.committee && !row["委員會"]) {
            throw new Error(`第 ${index + 1} 行：找不到委員會欄位`)
          }
          if (!row.participantName && !row["參加者姓名"] && !row["姓名"]) {
            throw new Error(`第 ${index + 1} 行：找不到參加者姓名欄位`)
          }

          const date = row.date || row["日期"]
          // 驗證日期格式
          if (!/^\d{4}-\d{2}-\d{2}$/.test(date)) {
            throw new Error(`第 ${index + 1} 行：日期格式錯誤: ${date}，請使用 YYYY-MM-DD 格式`)
          }

          return {
            date: date,
            activityName: row.activityName || row["活動名稱"] || "",
            committee: row.committee || row["委員會"] || "",
            participantName: row.participantName || row["參加者姓名"] || row["姓名"] || "",
            participantCategory: row.participantCategory || row["參加者類別"] || row["類別"] || "",
            description: row.description || row["描述"] || row["說明"] || "",
          }
        })

        // 按活動分組
        const activitiesMap = new Map<string, any>()

        parsedRows.forEach((row) => {
          const activityKey = `${row.date}-${row.activityName}-${row.committee}`

          if (!activitiesMap.has(activityKey)) {
            activitiesMap.set(activityKey, {
              activity: {
                sessionId: selectedSessionId || sessions.find((s) => s.isActive)?.id || "",
                date: row.date,
                name: row.activityName,
                committee: row.committee,
                description: row.description,
              },
              participants: [],
            })
          }

          // 添加參加者（避免重複）
          const activityData = activitiesMap.get(activityKey)!
          const existingParticipant = activityData.participants.find(
            (p: any) => p.name.toLowerCase() === row.participantName.toLowerCase(),
          )

          if (!existingParticipant) {
            activityData.participants.push({
              name: row.participantName,
              category: row.participantCategory,
            })
          }
        })

        return Array.from(activitiesMap.values())

      case "committees":
        return rawData.map((row: any) => {
          if (!row.name && !row["名稱"] && !row["委員會"]) {
            throw new Error("找不到委員會名稱欄位")
          }
          return row.name || row["名稱"] || row["委員會"] || ""
        })

      default:
        return rawData
    }
  }

  const handleFileUpload = async () => {
    if (!file) {
      setError("請選擇一個文件")
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const rawData = await parseFile(file)

      if (rawData.length === 0) {
        throw new Error("文件中未找到有效數據")
      }

      const parsedData = parseDataByType(rawData)
      setPreview(parsedData)
      setStep("preview")
    } catch (err) {
      setError(err instanceof Error ? err.message : "文件解析失敗")
    } finally {
      setIsLoading(false)
    }
  }

  const handleConfirmImport = () => {
    onImport(preview)
    setStep("complete")
  }

  const handleReset = () => {
    setFile(null)
    setPreview([])
    setError(null)
    setStep("upload")
  }

  const renderPreviewTable = () => {
    if (preview.length === 0) return null

    switch (dataType) {
      case "participants":
        return (
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
            <thead className="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">
                  姓名
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">
                  類別
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-700 divide-y divide-gray-200 dark:divide-gray-600">
              {preview.map((item, index) => (
                <tr key={index}>
                  <td className="px-4 py-2 text-sm text-gray-900 dark:text-white">{item.name}</td>
                  <td className="px-4 py-2 text-sm text-gray-900 dark:text-white">{item.category || "-"}</td>
                </tr>
              ))}
            </tbody>
          </table>
        )

      case "activities":
        return (
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
            <thead className="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">
                  日期
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">
                  活動名稱
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">
                  委員會
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">
                  描述
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-700 divide-y divide-gray-200 dark:divide-gray-600">
              {preview.map((item, index) => (
                <tr key={index}>
                  <td className="px-4 py-2 text-sm text-gray-900 dark:text-white">{item.date}</td>
                  <td className="px-4 py-2 text-sm text-gray-900 dark:text-white">{item.name}</td>
                  <td className="px-4 py-2 text-sm text-gray-900 dark:text-white">{item.committee}</td>
                  <td className="px-4 py-2 text-sm text-gray-900 dark:text-white">{item.description || "-"}</td>
                </tr>
              ))}
            </tbody>
          </table>
        )

      case "activities-with-participants":
        return (
          <div className="space-y-4">
            {preview.map((item, index) => (
              <div key={index} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                <div className="mb-3">
                  <h4 className="font-medium text-gray-900 dark:text-white">
                    活動 {index + 1}: {item.activity.name}
                  </h4>
                  <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    <span className="mr-4">日期: {item.activity.date}</span>
                    <span className="mr-4">委員會: {item.activity.committee}</span>
                    {item.activity.description && <span>描述: {item.activity.description}</span>}
                  </div>
                </div>
                <div>
                  <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    參加者 ({item.participants.length} 人):
                  </h5>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                    {item.participants.map((participant: any, pIndex: number) => (
                      <div
                        key={pIndex}
                        className="text-sm bg-gray-50 dark:bg-gray-800 p-2 rounded flex justify-between"
                      >
                        <span className="text-gray-900 dark:text-white">{participant.name}</span>
                        <span className="text-gray-500 dark:text-gray-400">{participant.category || "無類別"}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )

      case "committees":
        return (
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
            <thead className="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">
                  序號
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">
                  委員會名稱
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-700 divide-y divide-gray-200 dark:divide-gray-600">
              {preview.map((item, index) => (
                <tr key={index}>
                  <td className="px-4 py-2 text-sm text-gray-900 dark:text-white">{index + 1}</td>
                  <td className="px-4 py-2 text-sm text-gray-900 dark:text-white">{item}</td>
                </tr>
              ))}
            </tbody>
          </table>
        )

      default:
        return null
    }
  }

  const getPreviewSummary = () => {
    switch (dataType) {
      case "activities-with-participants":
        const totalParticipants = preview.reduce((sum, item) => sum + item.participants.length, 0)
        return `共找到 ${preview.length} 個活動，${totalParticipants} 位參加者`
      default:
        return `共找到 ${preview.length} 個${config.title}`
    }
  }

  if (step === "complete") {
    return (
      <div className="bg-white dark:bg-gray-700 p-6 rounded-lg shadow-md">
        <div className="text-center mb-6">
          <div className="w-16 h-16 mx-auto bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-8 w-8 text-green-600 dark:text-green-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h3 className="mt-4 text-xl font-medium text-gray-900 dark:text-white">導入成功</h3>
          <p className="mt-2 text-gray-600 dark:text-gray-300">{getPreviewSummary()}</p>
        </div>
        <div className="flex justify-center">
          <button
            onClick={onCancel}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
          >
            返回
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white dark:bg-gray-700 p-6 rounded-lg shadow-md">
      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">{config.description}</h3>

      {step === "upload" && (
        <>
          <div className="mb-4">
            <p className="mb-2 text-sm text-gray-600 dark:text-gray-300">
              上傳CSV或Excel文件以批量導入{config.title}。文件應包含以下列：
            </p>
            <ul className="list-disc pl-5 text-sm text-gray-600 dark:text-gray-300">
              {config.requiredFields.map((field, index) => (
                <li key={index}>{field}</li>
              ))}
            </ul>
          </div>

          {/* 特殊說明 */}
          {dataType === "activities-with-participants" && (
            <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-md">
              <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">導入說明：</h4>
              <ul className="text-xs text-blue-700 dark:text-blue-300 list-disc pl-4 space-y-1">
                <li>每行代表一個參加者及其所屬活動</li>
                <li>相同的活動信息（日期+名稱+委員會）會自動合併為一個活動</li>
                <li>同一活動的多個參加者請分別填寫在不同行</li>
                <li>重複的參加者姓名會自動去重</li>
              </ul>
            </div>
          )}

          {/* 範例數據 */}
          <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-md">
            <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">範例數據格式：</h4>
            <div className="text-xs text-blue-700 dark:text-blue-300 font-mono max-h-40 overflow-y-auto">
              {JSON.stringify(config.sampleData, null, 2)}
            </div>
          </div>

          {/* 屆別信息 */}
          {(dataType === "activities" || dataType === "activities-with-participants") && selectedSessionId && (
            <div className="mb-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-md">
              <p className="text-sm text-yellow-800 dark:text-yellow-200">
                <strong>注意：</strong>導入的活動將歸屬於當前選中的屆別：
                <span className="font-medium">
                  {sessions.find((s) => s.id === selectedSessionId)?.name || "未知屆別"}
                </span>
              </p>
            </div>
          )}

          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">選擇文件</label>
            <input
              type="file"
              accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
              onChange={handleFileChange}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white text-sm"
            />
            {file && <p className="mt-2 text-sm text-gray-600 dark:text-gray-300">已選擇：{file.name}</p>}
          </div>

          {error && (
            <div className="mb-4 p-3 bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-200 rounded-md text-sm">
              {error}
            </div>
          )}

          <div className="flex justify-end space-x-3">
            <button
              onClick={onCancel}
              className="px-3 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors"
            >
              取消
            </button>
            <button
              onClick={handleFileUpload}
              disabled={!file || isLoading}
              className="px-3 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? "處理中..." : "上傳並預覽"}
            </button>
          </div>
        </>
      )}

      {step === "preview" && (
        <>
          <div className="mb-4">
            <h4 className="text-md font-medium text-gray-800 dark:text-gray-200 mb-2">數據預覽</h4>
            <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">{getPreviewSummary()}，請確認以下數據無誤：</p>
          </div>

          <div className="mb-6 max-h-96 overflow-y-auto">{renderPreviewTable()}</div>

          <div className="flex justify-end space-x-3">
            <button
              onClick={handleReset}
              className="px-3 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors"
            >
              返回
            </button>
            <button
              onClick={handleConfirmImport}
              className="px-3 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors"
            >
              確認導入
            </button>
          </div>
        </>
      )}
    </div>
  )
}
