// Google Sheets 連接狀態指示器組件
import React, { useState } from 'react'
import {
  Cloud,
  CloudOff,
  Loader2,
  AlertCircle,
  CheckCircle,
  RefreshCw,
  Settings,
  Wifi
} from 'lucide-react'
import type {
  ConnectionState,
  SyncStatus
} from '../types/google-sheets'
import { ConnectionStatus } from '../types/google-sheets'

interface GoogleSheetsStatusProps {
  connectionState: ConnectionState
  syncStatus: SyncStatus
  onConnect: () => Promise<boolean>
  onDisconnect: () => void
  onSync: () => Promise<void>
  onToggleAutoSync: () => void
  className?: string
}

export function GoogleSheetsStatus({
  connectionState,
  syncStatus,
  onConnect,
  onDisconnect,
  onSync,
  onToggleAutoSync,
  className = ''
}: GoogleSheetsStatusProps) {
  const [isConnecting, setIsConnecting] = useState(false)
  const [isSyncing, setIsSyncing] = useState(false)
  const [showDetails, setShowDetails] = useState(false)

  const handleConnect = async () => {
    setIsConnecting(true)
    try {
      await onConnect()
    } finally {
      setIsConnecting(false)
    }
  }

  const handleSync = async () => {
    setIsSyncing(true)
    try {
      await onSync()
    } finally {
      setIsSyncing(false)
    }
  }

  const getStatusIcon = () => {
    if (isConnecting || connectionState.status === ConnectionStatus.CONNECTING) {
      return <Loader2 className="h-4 w-4 animate-spin" />
    }

    if (syncStatus.syncInProgress || isSyncing) {
      return <RefreshCw className="h-4 w-4 animate-spin" />
    }

    switch (connectionState.status) {
      case ConnectionStatus.CONNECTED:
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case ConnectionStatus.DISCONNECTED:
        return <CloudOff className="h-4 w-4 text-gray-400" />
      case ConnectionStatus.ERROR:
        return <AlertCircle className="h-4 w-4 text-red-500" />
      case ConnectionStatus.SYNCING:
        return <RefreshCw className="h-4 w-4 animate-spin text-blue-500" />
      default:
        return <Cloud className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusText = () => {
    if (isConnecting) return '連接中...'
    if (isSyncing || syncStatus.syncInProgress) return '同步中...'

    switch (connectionState.status) {
      case ConnectionStatus.CONNECTED:
        return 'Google Sheets 已連接'
      case ConnectionStatus.DISCONNECTED:
        return 'Google Sheets 未連接'
      case ConnectionStatus.ERROR:
        return 'Google Sheets 連接錯誤'
      case ConnectionStatus.SYNCING:
        return 'Google Sheets 同步中'
      default:
        return 'Google Sheets 狀態未知'
    }
  }

  const getStatusColor = () => {
    switch (connectionState.status) {
      case ConnectionStatus.CONNECTED:
        return 'text-green-600 dark:text-green-400'
      case ConnectionStatus.DISCONNECTED:
        return 'text-gray-500 dark:text-gray-400'
      case ConnectionStatus.ERROR:
        return 'text-red-600 dark:text-red-400'
      case ConnectionStatus.SYNCING:
        return 'text-blue-600 dark:text-blue-400'
      default:
        return 'text-gray-500 dark:text-gray-400'
    }
  }

  const formatLastSync = () => {
    if (!syncStatus.lastSyncTime) return '從未同步'

    const now = new Date()
    const diff = now.getTime() - syncStatus.lastSyncTime.getTime()
    const minutes = Math.floor(diff / 60000)

    if (minutes < 1) return '剛剛同步'
    if (minutes < 60) return `${minutes} 分鐘前同步`

    const hours = Math.floor(minutes / 60)
    if (hours < 24) return `${hours} 小時前同步`

    const days = Math.floor(hours / 24)
    return `${days} 天前同步`
  }

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 ${className}`}>
      {/* 主要狀態顯示 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          {getStatusIcon()}
          <div>
            <div className={`font-medium ${getStatusColor()}`}>
              {getStatusText()}
            </div>
            {syncStatus.lastSyncTime && (
              <div className="text-sm text-gray-500 dark:text-gray-400">
                {formatLastSync()}
              </div>
            )}
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {/* 自動同步指示器 */}
          {syncStatus.autoSyncEnabled && connectionState.status === ConnectionStatus.CONNECTED && (
            <div className="flex items-center space-x-1 text-sm text-blue-600 dark:text-blue-400">
              <Wifi className="h-3 w-3" />
              <span>自動同步</span>
            </div>
          )}

          {/* 操作按鈕 */}
          <div className="flex space-x-1">
            {connectionState.status === ConnectionStatus.DISCONNECTED ? (
              <button
                onClick={handleConnect}
                disabled={isConnecting}
                className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isConnecting ? '連接中...' : '連接'}
              </button>
            ) : (
              <>
                <button
                  onClick={handleSync}
                  disabled={isSyncing || syncStatus.syncInProgress}
                  className="px-3 py-1 text-sm bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSyncing || syncStatus.syncInProgress ? '同步中...' : '同步'}
                </button>
                <button
                  onClick={onDisconnect}
                  className="px-3 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600"
                >
                  斷開
                </button>
              </>
            )}

            <button
              onClick={() => setShowDetails(!showDetails)}
              className="px-2 py-1 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
            >
              <Settings className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* 錯誤信息 */}
      {(connectionState.error || syncStatus.error) && (
        <div className="mt-3 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded">
          <div className="flex items-start space-x-2">
            <AlertCircle className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-red-700 dark:text-red-300">
              <div className="font-medium">連接錯誤</div>
              <div className="mt-1">
                {connectionState.error || syncStatus.error}
              </div>
              {connectionState.retryCount > 0 && (
                <div className="mt-1 text-xs">
                  重試次數: {connectionState.retryCount}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* 詳細信息 */}
      {showDetails && (
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <div className="font-medium text-gray-700 dark:text-gray-300 mb-2">連接信息</div>
              <div className="space-y-1 text-gray-600 dark:text-gray-400">
                <div>狀態: {connectionState.status}</div>
                {connectionState.lastConnected && (
                  <div>
                    最後連接: {connectionState.lastConnected.toLocaleString()}
                  </div>
                )}
                <div>重試次數: {connectionState.retryCount}</div>
              </div>
            </div>

            <div>
              <div className="font-medium text-gray-700 dark:text-gray-300 mb-2">同步設置</div>
              <div className="space-y-1 text-gray-600 dark:text-gray-400">
                <div className="flex items-center justify-between">
                  <span>自動同步:</span>
                  <button
                    onClick={onToggleAutoSync}
                    className={`px-2 py-1 text-xs rounded ${
                      syncStatus.autoSyncEnabled
                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                        : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                    }`}
                  >
                    {syncStatus.autoSyncEnabled ? '已啟用' : '已禁用'}
                  </button>
                </div>
                {syncStatus.lastSyncTime && (
                  <div>
                    最後同步: {syncStatus.lastSyncTime.toLocaleString()}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
