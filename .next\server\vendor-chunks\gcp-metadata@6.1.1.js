"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/gcp-metadata@6.1.1";
exports.ids = ["vendor-chunks/gcp-metadata@6.1.1"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/gcp-metadata@6.1.1/node_modules/gcp-metadata/build/src/gcp-residency.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/.pnpm/gcp-metadata@6.1.1/node_modules/gcp-metadata/build/src/gcp-residency.js ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/**\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.GCE_LINUX_BIOS_PATHS = void 0;\nexports.isGoogleCloudServerless = isGoogleCloudServerless;\nexports.isGoogleComputeEngineLinux = isGoogleComputeEngineLinux;\nexports.isGoogleComputeEngineMACAddress = isGoogleComputeEngineMACAddress;\nexports.isGoogleComputeEngine = isGoogleComputeEngine;\nexports.detectGCPResidency = detectGCPResidency;\nconst fs_1 = __webpack_require__(/*! fs */ \"fs\");\nconst os_1 = __webpack_require__(/*! os */ \"os\");\n/**\n * Known paths unique to Google Compute Engine Linux instances\n */\nexports.GCE_LINUX_BIOS_PATHS = {\n    BIOS_DATE: '/sys/class/dmi/id/bios_date',\n    BIOS_VENDOR: '/sys/class/dmi/id/bios_vendor',\n};\nconst GCE_MAC_ADDRESS_REGEX = /^42:01/;\n/**\n * Determines if the process is running on a Google Cloud Serverless environment (Cloud Run or Cloud Functions instance).\n *\n * Uses the:\n * - {@link https://cloud.google.com/run/docs/container-contract#env-vars Cloud Run environment variables}.\n * - {@link https://cloud.google.com/functions/docs/env-var Cloud Functions environment variables}.\n *\n * @returns {boolean} `true` if the process is running on GCP serverless, `false` otherwise.\n */\nfunction isGoogleCloudServerless() {\n    /**\n     * `CLOUD_RUN_JOB` is used for Cloud Run Jobs\n     * - See {@link https://cloud.google.com/run/docs/container-contract#env-vars Cloud Run environment variables}.\n     *\n     * `FUNCTION_NAME` is used in older Cloud Functions environments:\n     * - See {@link https://cloud.google.com/functions/docs/env-var Python 3.7 and Go 1.11}.\n     *\n     * `K_SERVICE` is used in Cloud Run and newer Cloud Functions environments:\n     * - See {@link https://cloud.google.com/run/docs/container-contract#env-vars Cloud Run environment variables}.\n     * - See {@link https://cloud.google.com/functions/docs/env-var Cloud Functions newer runtimes}.\n     */\n    const isGFEnvironment = process.env.CLOUD_RUN_JOB ||\n        process.env.FUNCTION_NAME ||\n        process.env.K_SERVICE;\n    return !!isGFEnvironment;\n}\n/**\n * Determines if the process is running on a Linux Google Compute Engine instance.\n *\n * @returns {boolean} `true` if the process is running on Linux GCE, `false` otherwise.\n */\nfunction isGoogleComputeEngineLinux() {\n    if ((0, os_1.platform)() !== 'linux')\n        return false;\n    try {\n        // ensure this file exist\n        (0, fs_1.statSync)(exports.GCE_LINUX_BIOS_PATHS.BIOS_DATE);\n        // ensure this file exist and matches\n        const biosVendor = (0, fs_1.readFileSync)(exports.GCE_LINUX_BIOS_PATHS.BIOS_VENDOR, 'utf8');\n        return /Google/.test(biosVendor);\n    }\n    catch (_a) {\n        return false;\n    }\n}\n/**\n * Determines if the process is running on a Google Compute Engine instance with a known\n * MAC address.\n *\n * @returns {boolean} `true` if the process is running on GCE (as determined by MAC address), `false` otherwise.\n */\nfunction isGoogleComputeEngineMACAddress() {\n    const interfaces = (0, os_1.networkInterfaces)();\n    for (const item of Object.values(interfaces)) {\n        if (!item)\n            continue;\n        for (const { mac } of item) {\n            if (GCE_MAC_ADDRESS_REGEX.test(mac)) {\n                return true;\n            }\n        }\n    }\n    return false;\n}\n/**\n * Determines if the process is running on a Google Compute Engine instance.\n *\n * @returns {boolean} `true` if the process is running on GCE, `false` otherwise.\n */\nfunction isGoogleComputeEngine() {\n    return isGoogleComputeEngineLinux() || isGoogleComputeEngineMACAddress();\n}\n/**\n * Determines if the process is running on Google Cloud Platform.\n *\n * @returns {boolean} `true` if the process is running on GCP, `false` otherwise.\n */\nfunction detectGCPResidency() {\n    return isGoogleCloudServerless() || isGoogleComputeEngine();\n}\n//# sourceMappingURL=gcp-residency.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/gcp-metadata@6.1.1/node_modules/gcp-metadata/build/src/gcp-residency.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/gcp-metadata@6.1.1/node_modules/gcp-metadata/build/src/index.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/.pnpm/gcp-metadata@6.1.1/node_modules/gcp-metadata/build/src/index.js ***!
  \********************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/**\n * Copyright 2018 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.gcpResidencyCache = exports.METADATA_SERVER_DETECTION = exports.HEADERS = exports.HEADER_VALUE = exports.HEADER_NAME = exports.SECONDARY_HOST_ADDRESS = exports.HOST_ADDRESS = exports.BASE_PATH = void 0;\nexports.instance = instance;\nexports.project = project;\nexports.universe = universe;\nexports.bulk = bulk;\nexports.isAvailable = isAvailable;\nexports.resetIsAvailableCache = resetIsAvailableCache;\nexports.getGCPResidency = getGCPResidency;\nexports.setGCPResidency = setGCPResidency;\nexports.requestTimeout = requestTimeout;\nconst gaxios_1 = __webpack_require__(/*! gaxios */ \"(rsc)/./node_modules/.pnpm/gaxios@6.7.1/node_modules/gaxios/build/src/index.js\");\nconst jsonBigint = __webpack_require__(/*! json-bigint */ \"(rsc)/./node_modules/.pnpm/json-bigint@1.0.0/node_modules/json-bigint/index.js\");\nconst gcp_residency_1 = __webpack_require__(/*! ./gcp-residency */ \"(rsc)/./node_modules/.pnpm/gcp-metadata@6.1.1/node_modules/gcp-metadata/build/src/gcp-residency.js\");\nconst logger = __webpack_require__(/*! google-logging-utils */ \"(rsc)/./node_modules/.pnpm/google-logging-utils@0.0.2/node_modules/google-logging-utils/build/src/index.js\");\nexports.BASE_PATH = '/computeMetadata/v1';\nexports.HOST_ADDRESS = 'http://***************';\nexports.SECONDARY_HOST_ADDRESS = 'http://metadata.google.internal.';\nexports.HEADER_NAME = 'Metadata-Flavor';\nexports.HEADER_VALUE = 'Google';\nexports.HEADERS = Object.freeze({ [exports.HEADER_NAME]: exports.HEADER_VALUE });\nconst log = logger.log('gcp metadata');\n/**\n * Metadata server detection override options.\n *\n * Available via `process.env.METADATA_SERVER_DETECTION`.\n */\nexports.METADATA_SERVER_DETECTION = Object.freeze({\n    'assume-present': \"don't try to ping the metadata server, but assume it's present\",\n    none: \"don't try to ping the metadata server, but don't try to use it either\",\n    'bios-only': \"treat the result of a BIOS probe as canonical (don't fall back to pinging)\",\n    'ping-only': 'skip the BIOS probe, and go straight to pinging',\n});\n/**\n * Returns the base URL while taking into account the GCE_METADATA_HOST\n * environment variable if it exists.\n *\n * @returns The base URL, e.g., http://***************/computeMetadata/v1.\n */\nfunction getBaseUrl(baseUrl) {\n    if (!baseUrl) {\n        baseUrl =\n            process.env.GCE_METADATA_IP ||\n                process.env.GCE_METADATA_HOST ||\n                exports.HOST_ADDRESS;\n    }\n    // If no scheme is provided default to HTTP:\n    if (!/^https?:\\/\\//.test(baseUrl)) {\n        baseUrl = `http://${baseUrl}`;\n    }\n    return new URL(exports.BASE_PATH, baseUrl).href;\n}\n// Accepts an options object passed from the user to the API. In previous\n// versions of the API, it referred to a `Request` or an `Axios` request\n// options object.  Now it refers to an object with very limited property\n// names. This is here to help ensure users don't pass invalid options when\n// they  upgrade from 0.4 to 0.5 to 0.8.\nfunction validate(options) {\n    Object.keys(options).forEach(key => {\n        switch (key) {\n            case 'params':\n            case 'property':\n            case 'headers':\n                break;\n            case 'qs':\n                throw new Error(\"'qs' is not a valid configuration option. Please use 'params' instead.\");\n            default:\n                throw new Error(`'${key}' is not a valid configuration option.`);\n        }\n    });\n}\nasync function metadataAccessor(type, options = {}, noResponseRetries = 3, fastFail = false) {\n    let metadataKey = '';\n    let params = {};\n    let headers = {};\n    if (typeof type === 'object') {\n        const metadataAccessor = type;\n        metadataKey = metadataAccessor.metadataKey;\n        params = metadataAccessor.params || params;\n        headers = metadataAccessor.headers || headers;\n        noResponseRetries = metadataAccessor.noResponseRetries || noResponseRetries;\n        fastFail = metadataAccessor.fastFail || fastFail;\n    }\n    else {\n        metadataKey = type;\n    }\n    if (typeof options === 'string') {\n        metadataKey += `/${options}`;\n    }\n    else {\n        validate(options);\n        if (options.property) {\n            metadataKey += `/${options.property}`;\n        }\n        headers = options.headers || headers;\n        params = options.params || params;\n    }\n    const requestMethod = fastFail ? fastFailMetadataRequest : gaxios_1.request;\n    const req = {\n        url: `${getBaseUrl()}/${metadataKey}`,\n        headers: { ...exports.HEADERS, ...headers },\n        retryConfig: { noResponseRetries },\n        params,\n        responseType: 'text',\n        timeout: requestTimeout(),\n    };\n    log.info('instance request %j', req);\n    const res = await requestMethod(req);\n    log.info('instance metadata is %s', res.data);\n    // NOTE: node.js converts all incoming headers to lower case.\n    if (res.headers[exports.HEADER_NAME.toLowerCase()] !== exports.HEADER_VALUE) {\n        throw new Error(`Invalid response from metadata service: incorrect ${exports.HEADER_NAME} header. Expected '${exports.HEADER_VALUE}', got ${res.headers[exports.HEADER_NAME.toLowerCase()] ? `'${res.headers[exports.HEADER_NAME.toLowerCase()]}'` : 'no header'}`);\n    }\n    if (typeof res.data === 'string') {\n        try {\n            return jsonBigint.parse(res.data);\n        }\n        catch (_a) {\n            /* ignore */\n        }\n    }\n    return res.data;\n}\nasync function fastFailMetadataRequest(options) {\n    var _a;\n    const secondaryOptions = {\n        ...options,\n        url: (_a = options.url) === null || _a === void 0 ? void 0 : _a.toString().replace(getBaseUrl(), getBaseUrl(exports.SECONDARY_HOST_ADDRESS)),\n    };\n    // We race a connection between DNS/IP to metadata server. There are a couple\n    // reasons for this:\n    //\n    // 1. the DNS is slow in some GCP environments; by checking both, we might\n    //    detect the runtime environment signficantly faster.\n    // 2. we can't just check the IP, which is tarpitted and slow to respond\n    //    on a user's local machine.\n    //\n    // Additional logic has been added to make sure that we don't create an\n    // unhandled rejection in scenarios where a failure happens sometime\n    // after a success.\n    //\n    // Note, however, if a failure happens prior to a success, a rejection should\n    // occur, this is for folks running locally.\n    //\n    let responded = false;\n    const r1 = (0, gaxios_1.request)(options)\n        .then(res => {\n        responded = true;\n        return res;\n    })\n        .catch(err => {\n        if (responded) {\n            return r2;\n        }\n        else {\n            responded = true;\n            throw err;\n        }\n    });\n    const r2 = (0, gaxios_1.request)(secondaryOptions)\n        .then(res => {\n        responded = true;\n        return res;\n    })\n        .catch(err => {\n        if (responded) {\n            return r1;\n        }\n        else {\n            responded = true;\n            throw err;\n        }\n    });\n    return Promise.race([r1, r2]);\n}\n/**\n * Obtain metadata for the current GCE instance.\n *\n * @see {@link https://cloud.google.com/compute/docs/metadata/predefined-metadata-keys}\n *\n * @example\n * ```\n * const serviceAccount: {} = await instance('service-accounts/');\n * const serviceAccountEmail: string = await instance('service-accounts/default/email');\n * ```\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction instance(options) {\n    return metadataAccessor('instance', options);\n}\n/**\n * Obtain metadata for the current GCP project.\n *\n * @see {@link https://cloud.google.com/compute/docs/metadata/predefined-metadata-keys}\n *\n * @example\n * ```\n * const projectId: string = await project('project-id');\n * const numericProjectId: number = await project('numeric-project-id');\n * ```\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction project(options) {\n    return metadataAccessor('project', options);\n}\n/**\n * Obtain metadata for the current universe.\n *\n * @see {@link https://cloud.google.com/compute/docs/metadata/predefined-metadata-keys}\n *\n * @example\n * ```\n * const universeDomain: string = await universe('universe-domain');\n * ```\n */\nfunction universe(options) {\n    return metadataAccessor('universe', options);\n}\n/**\n * Retrieve metadata items in parallel.\n *\n * @see {@link https://cloud.google.com/compute/docs/metadata/predefined-metadata-keys}\n *\n * @example\n * ```\n * const data = await bulk([\n *   {\n *     metadataKey: 'instance',\n *   },\n *   {\n *     metadataKey: 'project/project-id',\n *   },\n * ] as const);\n *\n * // data.instance;\n * // data['project/project-id'];\n * ```\n *\n * @param properties The metadata properties to retrieve\n * @returns The metadata in `metadatakey:value` format\n */\nasync function bulk(properties) {\n    const r = {};\n    await Promise.all(properties.map(item => {\n        return (async () => {\n            const res = await metadataAccessor(item);\n            const key = item.metadataKey;\n            r[key] = res;\n        })();\n    }));\n    return r;\n}\n/*\n * How many times should we retry detecting GCP environment.\n */\nfunction detectGCPAvailableRetries() {\n    return process.env.DETECT_GCP_RETRIES\n        ? Number(process.env.DETECT_GCP_RETRIES)\n        : 0;\n}\nlet cachedIsAvailableResponse;\n/**\n * Determine if the metadata server is currently available.\n */\nasync function isAvailable() {\n    if (process.env.METADATA_SERVER_DETECTION) {\n        const value = process.env.METADATA_SERVER_DETECTION.trim().toLocaleLowerCase();\n        if (!(value in exports.METADATA_SERVER_DETECTION)) {\n            throw new RangeError(`Unknown \\`METADATA_SERVER_DETECTION\\` env variable. Got \\`${value}\\`, but it should be \\`${Object.keys(exports.METADATA_SERVER_DETECTION).join('`, `')}\\`, or unset`);\n        }\n        switch (value) {\n            case 'assume-present':\n                return true;\n            case 'none':\n                return false;\n            case 'bios-only':\n                return getGCPResidency();\n            case 'ping-only':\n            // continue, we want to ping the server\n        }\n    }\n    try {\n        // If a user is instantiating several GCP libraries at the same time,\n        // this may result in multiple calls to isAvailable(), to detect the\n        // runtime environment. We use the same promise for each of these calls\n        // to reduce the network load.\n        if (cachedIsAvailableResponse === undefined) {\n            cachedIsAvailableResponse = metadataAccessor('instance', undefined, detectGCPAvailableRetries(), \n            // If the default HOST_ADDRESS has been overridden, we should not\n            // make an effort to try SECONDARY_HOST_ADDRESS (as we are likely in\n            // a non-GCP environment):\n            !(process.env.GCE_METADATA_IP || process.env.GCE_METADATA_HOST));\n        }\n        await cachedIsAvailableResponse;\n        return true;\n    }\n    catch (e) {\n        const err = e;\n        if (process.env.DEBUG_AUTH) {\n            console.info(err);\n        }\n        if (err.type === 'request-timeout') {\n            // If running in a GCP environment, metadata endpoint should return\n            // within ms.\n            return false;\n        }\n        if (err.response && err.response.status === 404) {\n            return false;\n        }\n        else {\n            if (!(err.response && err.response.status === 404) &&\n                // A warning is emitted if we see an unexpected err.code, or err.code\n                // is not populated:\n                (!err.code ||\n                    ![\n                        'EHOSTDOWN',\n                        'EHOSTUNREACH',\n                        'ENETUNREACH',\n                        'ENOENT',\n                        'ENOTFOUND',\n                        'ECONNREFUSED',\n                    ].includes(err.code))) {\n                let code = 'UNKNOWN';\n                if (err.code)\n                    code = err.code;\n                process.emitWarning(`received unexpected error = ${err.message} code = ${code}`, 'MetadataLookupWarning');\n            }\n            // Failure to resolve the metadata service means that it is not available.\n            return false;\n        }\n    }\n}\n/**\n * reset the memoized isAvailable() lookup.\n */\nfunction resetIsAvailableCache() {\n    cachedIsAvailableResponse = undefined;\n}\n/**\n * A cache for the detected GCP Residency.\n */\nexports.gcpResidencyCache = null;\n/**\n * Detects GCP Residency.\n * Caches results to reduce costs for subsequent calls.\n *\n * @see setGCPResidency for setting\n */\nfunction getGCPResidency() {\n    if (exports.gcpResidencyCache === null) {\n        setGCPResidency();\n    }\n    return exports.gcpResidencyCache;\n}\n/**\n * Sets the detected GCP Residency.\n * Useful for forcing metadata server detection behavior.\n *\n * Set `null` to autodetect the environment (default behavior).\n * @see getGCPResidency for getting\n */\nfunction setGCPResidency(value = null) {\n    exports.gcpResidencyCache = value !== null ? value : (0, gcp_residency_1.detectGCPResidency)();\n}\n/**\n * Obtain the timeout for requests to the metadata server.\n *\n * In certain environments and conditions requests can take longer than\n * the default timeout to complete. This function will determine the\n * appropriate timeout based on the environment.\n *\n * @returns {number} a request timeout duration in milliseconds.\n */\nfunction requestTimeout() {\n    return getGCPResidency() ? 0 : 3000;\n}\n__exportStar(__webpack_require__(/*! ./gcp-residency */ \"(rsc)/./node_modules/.pnpm/gcp-metadata@6.1.1/node_modules/gcp-metadata/build/src/gcp-residency.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/gcp-metadata@6.1.1/node_modules/gcp-metadata/build/src/index.js\n");

/***/ })

};
;