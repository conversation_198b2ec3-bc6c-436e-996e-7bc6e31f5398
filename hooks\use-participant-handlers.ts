"use client"

import { useCallback } from "react"
import type { Activity, Participant, SessionParticipant } from "../types"

interface UseParticipantHandlersProps {
  allParticipants: Participant[]
  sessionParticipants: SessionParticipant[]
  activities: Activity[]
  setAllParticipants: (participants: Participant[]) => void
  setSessionParticipants: (sessionParticipants: SessionParticipant[]) => void
  setActivities: (activities: Activity[]) => void
}

export function useParticipantHandlers(props: UseParticipantHandlersProps) {
  const {
    allParticipants,
    sessionParticipants,
    activities,
    setAllParticipants,
    setSessionParticipants,
    setActivities,
  } = props

  const handleAddParticipant = useCallback(
    (newParticipant: Omit<Participant, "id" | "attendance">) => {
      // 使用時間戳和隨機數生成唯一ID，避免ID衝突
      const id = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      const participant: Participant = {
        id,
        ...newParticipant,
        attendance: {},
      }
      console.log('Adding participant to global list:', participant) // 調試日誌
      setAllParticipants([...allParticipants, participant])
    },
    [allParticipants, setAllParticipants],
  )

  const handleAddSessionParticipant = useCallback(
    (newSessionParticipant: Omit<SessionParticipant, "id">) => {
      // 使用時間戳和隨機數生成唯一ID，避免ID衝突
      const id = `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      const sessionParticipant: SessionParticipant = {
        id,
        ...newSessionParticipant,
      }
      console.log('Adding session participant:', sessionParticipant) // 調試日誌
      setSessionParticipants([...sessionParticipants, sessionParticipant])
    },
    [sessionParticipants, setSessionParticipants],
  )

  const handleBulkAddSessionParticipants = useCallback(
    (newSessionParticipants: Omit<SessionParticipant, "id">[]) => {
      const sessionParticipants: SessionParticipant[] = newSessionParticipants.map((sp, index) => {
        // 使用時間戳和隨機數生成唯一ID，加上索引確保唯一性
        const id = `session-${Date.now()}-${index}-${Math.random().toString(36).substr(2, 9)}`
        return {
          id,
          ...sp,
        }
      })
      console.log('Bulk adding session participants:', sessionParticipants) // 調試日誌
      setSessionParticipants(prev => [...prev, ...sessionParticipants])
    },
    [setSessionParticipants],
  )

  const handleBulkAddParticipants = useCallback(
    (newParticipants: Omit<Participant, "id" | "attendance">[]) => {
      const participants: Participant[] = newParticipants.map((p) => {
        // 使用時間戳和隨機數生成唯一ID，避免ID衝突
        const id = `bulk-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
        const participant: Participant = {
          id,
          ...p,
          attendance: {},
        }
        return participant
      })
      console.log('Bulk adding participants:', participants) // 調試日誌
      setAllParticipants([...allParticipants, ...participants])
    },
    [allParticipants, setAllParticipants],
  )

  const handleUpdateParticipant = useCallback(
    (updatedParticipant: Participant) => {
      setAllParticipants(allParticipants.map((p) => (p.id === updatedParticipant.id ? updatedParticipant : p)))

      setSessionParticipants(
        sessionParticipants.map((sp) =>
          sp.participantId === updatedParticipant.id ? { ...sp, name: updatedParticipant.name } : sp,
        ),
      )
    },
    [allParticipants, sessionParticipants, setAllParticipants, setSessionParticipants],
  )

  const handleUpdateSessionParticipant = useCallback(
    (updatedSessionParticipant: SessionParticipant) => {
      setSessionParticipants(
        sessionParticipants.map((sp) => (sp.id === updatedSessionParticipant.id ? updatedSessionParticipant : sp)),
      )
    },
    [sessionParticipants, setSessionParticipants],
  )

  const handleDeleteParticipant = useCallback(
    (participantId: string) => {
      setAllParticipants(allParticipants.filter((p) => p.id !== participantId))
      setSessionParticipants(sessionParticipants.filter((sp) => sp.participantId !== participantId))
      setActivities(
        activities.map((activity) => ({
          ...activity,
          participants: activity.participants.filter((p) => p.id !== participantId),
        })),
      )
    },
    [allParticipants, sessionParticipants, activities, setAllParticipants, setSessionParticipants, setActivities],
  )

  const handleRemoveFromSession = useCallback(
    (sessionParticipantId: string) => {
      // 找到要移除的屆別參加者
      const sessionParticipantToRemove = sessionParticipants.find((sp) => sp.id === sessionParticipantId)

      if (sessionParticipantToRemove) {
        // 從屆別參加者列表中移除
        setSessionParticipants(sessionParticipants.filter((sp) => sp.id !== sessionParticipantId))

        // 從所有相關活動中移除該參加者
        setActivities(
          activities.map((activity) => ({
            ...activity,
            participants: activity.participants.filter((p) => p.id !== sessionParticipantToRemove.participantId),
          })),
        )
      }
    },
    [sessionParticipants, activities, setSessionParticipants, setActivities],
  )

  const handleBulkDeleteTitle = useCallback(
    (title: string) => {
      // 找出所有使用該職銜的參加者
      const participantsToUpdate = allParticipants.filter(p =>
        p.category === title && !p.name.startsWith('職銜佔位符-')
      )
      const placeholders = allParticipants.filter(p =>
        p.category === title && p.name.startsWith('職銜佔位符-')
      )
      const sessionParticipantsToUpdate = sessionParticipants.filter(sp =>
        sp.category === title
      )

      // 一次性更新所有狀態
      // 更新全局參加者 - 清除職銜
      const updatedAllParticipants = allParticipants.map(p => {
        if (p.category === title && !p.name.startsWith('職銜佔位符-')) {
          return { ...p, category: "" }
        }
        return p
      }).filter(p => !p.name.startsWith(`職銜佔位符-${title}`)) // 移除佔位符

      // 更新屆別參加者 - 清除職銜
      const updatedSessionParticipants = sessionParticipants.map(sp => {
        if (sp.category === title) {
          return { ...sp, category: "" }
        }
        return sp
      })

      // 從活動中移除佔位符參加者
      const updatedActivities = activities.map(activity => ({
        ...activity,
        participants: activity.participants.filter(p =>
          !placeholders.some(placeholder => placeholder.id === p.id)
        )
      }))

      // 批量更新所有狀態
      setAllParticipants(updatedAllParticipants)
      setSessionParticipants(updatedSessionParticipants)
      setActivities(updatedActivities)

      return {
        participantsUpdated: participantsToUpdate.length,
        placeholdersRemoved: placeholders.length,
        sessionParticipantsUpdated: sessionParticipantsToUpdate.length
      }
    },
    [allParticipants, sessionParticipants, activities, setAllParticipants, setSessionParticipants, setActivities],
  )

  return {
    handleAddParticipant,
    handleAddSessionParticipant,
    handleBulkAddSessionParticipants,
    handleBulkAddParticipants,
    handleUpdateParticipant,
    handleUpdateSessionParticipant,
    handleDeleteParticipant,
    handleRemoveFromSession,
    handleBulkDeleteTitle,
  }
}
