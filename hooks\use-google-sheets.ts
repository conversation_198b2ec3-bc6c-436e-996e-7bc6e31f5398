// Google Sheets 整合 React Hook
import { useState, useEffect, useCallback, useRef } from 'react'
import { GoogleSheetsService } from '../lib/google-sheets'
import { SyncService } from '../lib/sync-service'
import type {
  SyncStatus,
  SyncResult,
  ConnectionState,
  GoogleSheetsConfig,
  Sheet<PERSON>ames,
  SyncOptions
} from '../types/google-sheets'
import { ConnectionStatus } from '../types/google-sheets'
import type {
  Participant,
  Activity,
  Session,
  AttendanceRecord
} from '../types'

interface UseGoogleSheetsProps {
  config?: GoogleSheetsConfig
  autoSync?: boolean
  syncInterval?: number
  accessToken?: string
}

interface UseGoogleSheetsReturn {
  // 連接狀態
  connectionState: ConnectionState
  syncStatus: SyncStatus

  // 操作方法
  connect: () => Promise<boolean>
  disconnect: () => void
  testConnection: () => Promise<boolean>

  // 同步方法
  syncToSheets: (data: {
    participants: Participant[]
    activities: Activity[]
    sessions: Session[]
    attendanceRecords: AttendanceRecord[]
  }, options?: SyncOptions) => Promise<SyncResult>

  syncFromSheets: () => Promise<{
    participants: Participant[]
    activities: Activity[]
    sessions: Session[]
  } | null>

  // 手動同步控制
  enableAutoSync: () => void
  disableAutoSync: () => void
  forcSync: () => Promise<SyncResult>
}

export function useGoogleSheets({
  config,
  autoSync = false,
  syncInterval = 5 * 60 * 1000, // 5分鐘
  accessToken
}: UseGoogleSheetsProps = {}): UseGoogleSheetsReturn {

  const [connectionState, setConnectionState] = useState<ConnectionState>({
    status: ConnectionStatus.DISCONNECTED,
    retryCount: 0
  })

  const [syncStatus, setSyncStatus] = useState<SyncStatus>({
    isConnected: false,
    lastSyncTime: null,
    syncInProgress: false,
    error: null,
    autoSyncEnabled: autoSync
  })

  const googleSheetsServiceRef = useRef<GoogleSheetsService | null>(null)
  const syncServiceRef = useRef<SyncService | null>(null)
  const syncIntervalRef = useRef<NodeJS.Timeout | null>(null)

  // 默認配置
  const defaultConfig: GoogleSheetsConfig = {
    spreadsheetId: process.env.NEXT_PUBLIC_GOOGLE_SPREADSHEET_ID || '',
    serviceAccountEmail: process.env.NEXT_PUBLIC_GOOGLE_SERVICE_ACCOUNT_EMAIL,
    privateKey: process.env.NEXT_PUBLIC_GOOGLE_PRIVATE_KEY,
    projectId: process.env.NEXT_PUBLIC_GOOGLE_PROJECT_ID,
    clientId: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
    clientSecret: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_SECRET
  }

  const sheetNames: SheetNames = {
    participants: process.env.NEXT_PUBLIC_PARTICIPANTS_SHEET_NAME || '參加者資料',
    activities: process.env.NEXT_PUBLIC_ACTIVITIES_SHEET_NAME || '活動資料',
    attendance: process.env.NEXT_PUBLIC_ATTENDANCE_SHEET_NAME || '出席記錄',
    sessions: process.env.NEXT_PUBLIC_SESSIONS_SHEET_NAME || '屆別資料'
  }

  const finalConfig = { ...defaultConfig, ...config }

  /**
   * 連接到 Google Sheets
   */
  const connect = useCallback(async (): Promise<boolean> => {
    try {
      setConnectionState(prev => ({
        ...prev,
        status: ConnectionStatus.CONNECTING
      }))

      setSyncStatus(prev => ({
        ...prev,
        error: null
      }))

      // 使用 API 路由連接
      const response = await fetch('/api/google-sheets/connect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          config: finalConfig,
          accessToken: accessToken
        })
      })

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || '連接失敗')
      }

      setConnectionState({
        status: ConnectionStatus.CONNECTED,
        lastConnected: new Date(),
        retryCount: 0
      })

      setSyncStatus(prev => ({
        ...prev,
        isConnected: true,
        error: null
      }))

      // 啟動自動同步
      if (autoSync) {
        enableAutoSync()
      }

      return true
    } catch (error) {
      console.error('連接 Google Sheets 失敗:', error)

      setConnectionState(prev => ({
        status: ConnectionStatus.ERROR,
        error: error instanceof Error ? error.message : '連接失敗',
        retryCount: prev.retryCount + 1
      }))

      setSyncStatus(prev => ({
        ...prev,
        isConnected: false,
        error: error instanceof Error ? error.message : '連接失敗'
      }))

      return false
    }
  }, [finalConfig, autoSync])

  /**
   * 斷開連接
   */
  const disconnect = useCallback(() => {
    if (syncIntervalRef.current) {
      clearInterval(syncIntervalRef.current)
      syncIntervalRef.current = null
    }

    googleSheetsServiceRef.current = null
    syncServiceRef.current = null

    setConnectionState({
      status: ConnectionStatus.DISCONNECTED,
      retryCount: 0
    })

    setSyncStatus(prev => ({
      ...prev,
      isConnected: false,
      autoSyncEnabled: false,
      syncInProgress: false
    }))
  }, [])

  /**
   * 測試連接
   */
  const testConnection = useCallback(async (): Promise<boolean> => {
    if (!googleSheetsServiceRef.current) {
      return false
    }

    try {
      const result = await googleSheetsServiceRef.current.readRange(sheetNames.participants, 'A1:A1')
      return result.success
    } catch (error) {
      console.error('測試連接失敗:', error)
      return false
    }
  }, [sheetNames.participants])

  /**
   * 同步數據到 Google Sheets
   */
  const syncToSheets = useCallback(async (data: {
    participants: Participant[]
    activities: Activity[]
    sessions: Session[]
    attendanceRecords: AttendanceRecord[]
  }, options: SyncOptions = {}): Promise<SyncResult> => {
    try {
      setSyncStatus(prev => ({ ...prev, syncInProgress: true, error: null }))
      setConnectionState(prev => ({ ...prev, status: ConnectionStatus.SYNCING }))

      // 使用 API 路由同步
      const response = await fetch('/api/google-sheets/sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          config: finalConfig,
          data,
          direction: 'push',
          options,
          accessToken: accessToken
        })
      })

      const result = await response.json()

      setSyncStatus(prev => ({
        ...prev,
        syncInProgress: false,
        lastSyncTime: new Date(),
        error: result.success ? null : result.message
      }))

      setConnectionState(prev => ({
        ...prev,
        status: ConnectionStatus.CONNECTED
      }))

      return result
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '同步失敗'

      setSyncStatus(prev => ({
        ...prev,
        syncInProgress: false,
        error: errorMessage
      }))

      setConnectionState(prev => ({
        ...prev,
        status: ConnectionStatus.ERROR,
        error: errorMessage
      }))

      return {
        success: false,
        message: errorMessage,
        recordsAffected: 0,
        timestamp: new Date()
      }
    }
  }, [finalConfig])

  /**
   * 從 Google Sheets 同步數據
   */
  const syncFromSheets = useCallback(async () => {
    try {
      setSyncStatus(prev => ({ ...prev, syncInProgress: true, error: null }))

      // 使用 API 路由從 Google Sheets 拉取數據
      const response = await fetch('/api/google-sheets/sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          config: finalConfig,
          direction: 'pull',
          data: { participants: [], activities: [], sessions: [], attendanceRecords: [] },
          accessToken: accessToken
        })
      })

      const result = await response.json()

      setSyncStatus(prev => ({
        ...prev,
        syncInProgress: false,
        lastSyncTime: new Date(),
        error: result.success ? null : result.message
      }))

      if (result.success && result.data) {
        return result.data
      }

      throw new Error(result.message || '從 Google Sheets 同步失敗')
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '從 Google Sheets 同步失敗'

      setSyncStatus(prev => ({
        ...prev,
        syncInProgress: false,
        error: errorMessage
      }))

      return null
    }
  }, [finalConfig])

  /**
   * 啟用自動同步
   */
  const enableAutoSync = useCallback(() => {
    if (syncIntervalRef.current) {
      clearInterval(syncIntervalRef.current)
    }

    setSyncStatus(prev => ({ ...prev, autoSyncEnabled: true }))

    // 這裡需要從外部獲取數據，實際實現時需要傳入數據獲取函數
    // syncIntervalRef.current = setInterval(async () => {
    //   if (connectionState.status === ConnectionStatus.CONNECTED) {
    //     await syncToSheets(getCurrentData())
    //   }
    // }, syncInterval)
  }, [syncInterval])

  /**
   * 禁用自動同步
   */
  const disableAutoSync = useCallback(() => {
    if (syncIntervalRef.current) {
      clearInterval(syncIntervalRef.current)
      syncIntervalRef.current = null
    }

    setSyncStatus(prev => ({ ...prev, autoSyncEnabled: false }))
  }, [])

  /**
   * 強制同步
   */
  const forcSync = useCallback(async (): Promise<SyncResult> => {
    // 實際實現時需要獲取當前數據
    return {
      success: false,
      message: '需要實現數據獲取邏輯',
      recordsAffected: 0,
      timestamp: new Date()
    }
  }, [])

  // 清理效果
  useEffect(() => {
    return () => {
      disconnect()
    }
  }, [disconnect])

  return {
    connectionState,
    syncStatus,
    connect,
    disconnect,
    testConnection,
    syncToSheets,
    syncFromSheets,
    enableAutoSync,
    disableAutoSync,
    forcSync
  }
}
