"use client"

import { useCallback } from "react"
import type { Activity, Participant, SessionParticipant, Session } from "../types"

interface UseActivityHandlersProps {
  activities: Activity[]
  allParticipants: Participant[]
  sessionParticipants: SessionParticipant[]
  sessions: Session[]
  selectedSessionId: string | null
  setActivities: (activities: Activity[]) => void
  setAllParticipants: (participants: Participant[]) => void
  setSessionParticipants: (sessionParticipants: SessionParticipant[]) => void
}

export function useActivityHandlers(props: UseActivityHandlersProps) {
  const {
    activities,
    allParticipants,
    sessionParticipants,
    sessions,
    selectedSessionId,
    setActivities,
    setAllParticipants,
    setSessionParticipants,
  } = props

  const handleAddActivity = useCallback(
    (newActivity: Omit<Activity, "id" | "participants">) => {
      const id = (activities.length + 1).toString()
      setActivities([
        ...activities,
        {
          id,
          sessionId: selectedSessionId || sessions[0]?.id || "",
          ...newActivity,
          participants: [],
        },
      ])
    },
    [activities, selectedSessionId, sessions, setActivities],
  )

  const handleDeleteActivity = useCallback(
    (activityId: string) => {
      setActivities(activities.filter((activity) => activity.id !== activityId))
    },
    [activities, setActivities],
  )

  const handleEditActivity = useCallback(
    (updatedActivity: Activity) => {
      setActivities(activities.map((activity) => (activity.id === updatedActivity.id ? updatedActivity : activity)))
    },
    [activities, setActivities],
  )

  const handleBulkAddActivitiesWithParticipants = useCallback(
    (activitiesData: Array<{ activity: Omit<Activity, "id" | "participants">; participants: Omit<Participant, "id" | "attendance">[] }>) => {
      // 使用函數式更新確保狀態一致性
      setAllParticipants((currentAllParticipants) => {
        const newParticipants: Participant[] = []
        let nextParticipantId = currentAllParticipants.length + 1

        setActivities((currentActivities) => {
          const newActivities: Activity[] = []
          let nextActivityId = currentActivities.length + 1

          activitiesData.forEach(({ activity, participants }) => {
            const activityId = nextActivityId.toString()
            nextActivityId++

            const activityParticipants: Participant[] = []

            participants.forEach((participant) => {
              // 檢查是否已存在相同姓名的參加者
              const existingParticipant = [...currentAllParticipants, ...newParticipants].find(
                (p) => p.name.toLowerCase() === participant.name.toLowerCase(),
              )

              let participantId: string
              if (existingParticipant) {
                participantId = existingParticipant.id
              } else {
                participantId = nextParticipantId.toString()
                nextParticipantId++
                const newParticipant: Participant = {
                  id: participantId,
                  name: participant.name,
                  category: participant.category,
                  attendance: {},
                }
                newParticipants.push(newParticipant)
              }

              activityParticipants.push({
                id: participantId,
                name: participant.name,
                category: participant.category,
                attendance: { [activity.date]: false },
              })
            })

            newActivities.push({
              id: activityId,
              sessionId: selectedSessionId || sessions[0]?.id || "",
              ...activity,
              participants: activityParticipants,
            })
          })

          return [...currentActivities, ...newActivities]
        })

        return [...currentAllParticipants, ...newParticipants]
      })
    },
    [selectedSessionId, sessions, setActivities, setAllParticipants],
  )

  const handleAddParticipantToActivity = useCallback(
    (activityId: string, newParticipant: Omit<Participant, "id" | "attendance">) => {
      // 先处理全局参与者列表
      setAllParticipants((currentAllParticipants) => {
        const existingParticipant = currentAllParticipants.find(
          (p) => p.name.toLowerCase() === newParticipant.name.toLowerCase(),
        )

        let participantId: string
        let updatedAllParticipants = currentAllParticipants

        if (existingParticipant) {
          participantId = existingParticipant.id
        } else {
          // 使用时间戳和随机数生成唯一ID
          participantId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
          const newGlobalParticipant: Participant = {
            id: participantId,
            name: newParticipant.name,
            category: newParticipant.category,
            attendance: {},
          }
          updatedAllParticipants = [...currentAllParticipants, newGlobalParticipant]
        }

        // 分离活动列表更新
        setActivities((currentActivities) => {
          return currentActivities.map((activity) => {
            if (activity.id === activityId) {
              // 检查参加者是否已经在活动中
              if (activity.participants.some((p) => p.id === participantId)) {
                return activity
              }

              return {
                ...activity,
                participants: [
                  ...activity.participants,
                  {
                    id: participantId,
                    name: newParticipant.name,
                    category: newParticipant.category,
                    attendance: { [activity.date]: false },
                  },
                ],
              }
            }
            return activity
          })
        })

        return updatedAllParticipants
      })
    },
    [setActivities, setAllParticipants],
  )

  const handleRemoveParticipantFromActivity = useCallback(
    (activityId: string, participantId: string) => {
      setActivities(
        activities.map((activity) => {
          if (activity.id === activityId) {
            return {
              ...activity,
              participants: activity.participants.filter((p) => p.id !== participantId),
            }
          }
          return activity
        }),
      )
    },
    [activities, setActivities],
  )

  const handleUpdateParticipantInActivity = useCallback(
    (activityId: string, updatedParticipant: Participant) => {
      setActivities(
        activities.map((activity) => {
          if (activity.id === activityId) {
            return {
              ...activity,
              participants: activity.participants.map((p) => (p.id === updatedParticipant.id ? updatedParticipant : p)),
            }
          }
          return activity
        }),
      )

      setAllParticipants(
        allParticipants.map((p) => {
          if (p.id === updatedParticipant.id) {
            return {
              ...p,
              name: updatedParticipant.name,
              category: updatedParticipant.category,
            }
          }
          return p
        }),
      )
    },
    [activities, allParticipants, setActivities, setAllParticipants],
  )

  const handleToggleAttendance = useCallback(
    (activityId: string, participantId: string) => {
      setActivities(
        activities.map((activity) => {
          if (activity.id === activityId) {
            return {
              ...activity,
              participants: activity.participants.map((p) => {
                if (p.id === participantId) {
                  return {
                    ...p,
                    attendance: {
                      ...p.attendance,
                      [activity.date]: !p.attendance[activity.date],
                    },
                  }
                }
                return p
              }),
            }
          }
          return activity
        }),
      )
    },
    [activities, setActivities],
  )

  const handleBulkAddParticipantsToActivity = useCallback(
    (activityId: string, newParticipants: Omit<Participant, "id" | "attendance">[]) => {
      // 使用單一的狀態更新來確保原子性操作
      setAllParticipants((currentAllParticipants) => {
        const updatedAllParticipants = [...currentAllParticipants]
        const participantsToAdd: Array<{ id: string; participant: Omit<Participant, "id" | "attendance"> }> = []
        
        // 處理所有新參加者
        newParticipants.forEach((newParticipant) => {
          const existingParticipant = updatedAllParticipants.find(
            (p) => p.name.toLowerCase() === newParticipant.name.toLowerCase(),
          )

          let participantId: string

          if (existingParticipant) {
            participantId = existingParticipant.id
          } else {
            // 使用時間戳和隨機數確保ID唯一性
            participantId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
            const newGlobalParticipant: Participant = {
              id: participantId,
              name: newParticipant.name,
              category: newParticipant.category,
              attendance: {},
            }
            updatedAllParticipants.push(newGlobalParticipant)
          }

          participantsToAdd.push({ id: participantId, participant: newParticipant })
        })

        // 同步更新活動狀態
        setActivities((currentActivities) => {
          return currentActivities.map((activity) => {
            if (activity.id === activityId) {
              const newActivityParticipants = [...activity.participants]
              
              participantsToAdd.forEach(({ id: participantId, participant: newParticipant }) => {
                // 檢查參加者是否已經在活動中
                if (!newActivityParticipants.some((p) => p.id === participantId)) {
                  newActivityParticipants.push({
                    id: participantId,
                    name: newParticipant.name,
                    category: newParticipant.category,
                    attendance: { [activity.date]: false },
                  })
                }
              })

              return {
                ...activity,
                participants: newActivityParticipants,
              }
            }
            return activity
          })
        })

        return updatedAllParticipants
      })
    },
    [setActivities, setAllParticipants],
  )

  return {
    handleAddActivity,
    handleDeleteActivity,
    handleEditActivity,
    handleBulkAddActivitiesWithParticipants,
    handleAddParticipantToActivity,
    handleRemoveParticipantFromActivity,
    handleUpdateParticipantInActivity,
    handleToggleAttendance,
    handleBulkAddParticipantsToActivity,
  }
}
